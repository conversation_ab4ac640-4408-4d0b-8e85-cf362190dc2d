rm -rf ../producao

mkdir ../producao

cd ../producao

7z x /root/promokit/producao.zip -y

cd ../Servidor

echo $PWD

. ~/.nvm/nvm.sh
export NODE_ENV=production
nvm use 12

export NODE_OPTIONS=--max-old-space-size=4192
cp -r ../producao/admin/pt/* dist/
sed -i 's/\/pt\//\//g' dist/ngsw.json

cp -r ../producao/loja/pt/* dist/loja/

tsc --p server

grunt copy

cd ../producao/admin/pt

echo $PWD

echo Alterando scriptstemplate e csstemplate do admin

../../../Servidor/geretemplatescripts.sh $(ls main*.js polyfills*.js runtime*.js scripts*.js) > ../../../Servidor/distServer/views/scriptstemplate.ejs
../../../Servidor/geretemplatecss.sh $(ls *.css) > ../../../Servidor/distServer/views/csstemplate.ejs

cd ../..


cd ../producao/loja/pt

echo Alterando scriptstemplate e csstemplate da loja
echo $PWD

../../../Servidor/geretemplatescripts.sh $(ls main*.js polyfills*.js runtime*.js scripts*.js) "/loja/" > ../../../Servidor/distServer/views/loja/scriptstemplate.ejs
../../../Servidor/geretemplatecss.sh $(ls *.css) "/loja/" > ../../../Servidor/distServer/views/loja/csstemplate.ejs

cd ../../../Servidor

echo Copiando styles
echo $PWD

cp src/styles.css distServer/public/styles.css

#forever start --uid "custom-name" server.js
pm2 start distServer/bin/promokit.js -i 8 --name promokit3 --wait-ready --listen-timeout 30000 --node-args="--max-old-space-size=4192" --log "/root/.pm2/logs/promokit-combinado.log" --merge-logs

pm2 reload promokit2 --merge-logs --wait-ready --listen-timeout 60000000

pm2 reload tarefas --wait-ready --listen-timeout 60000000

echo "Iniciando o delay de 2 minutos..."

sleep 120

pm2 delete promokit3
