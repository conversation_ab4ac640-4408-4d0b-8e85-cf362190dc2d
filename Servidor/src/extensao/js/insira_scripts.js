async function  insiraScript(nomeScript) {
  return new Promise( (resolve, reject) => {
    var s = document.createElement('script');
    s.src = nomeScript;
    s.async = false;
    (document.head||document.documentElement).appendChild(s);
    s.onload = function() {
      s.parentNode.removeChild(s);

      resolve();
    };
  });
}

function facaRequest() {
  var r = new XMLHttpRequest();
  r.open("GET", "https://fibo.promokit.com.br/assets/js/wapi.j2s", true);
  r.onreadystatechange = function () {
    debugger;
    if (r.readyState != 4 || r.status != 200) return;
    alert("Success: " + r.responseText);
  };
  r.send("banana=yellow");
}

setTimeout( async() => {
  if( !window.inseriu ) {
    facaRequest();
    /*
    await insiraScript('https://localhost:8443/assets/js/wapi.js');
    await insiraScript('https://localhost:8443/assets/js/props_react.js');

    await insiraScript('https://localhost:8443/assets/js/WhatsappAPI.js');
     */
  }

  window.inseriu = true;
}, 1);

