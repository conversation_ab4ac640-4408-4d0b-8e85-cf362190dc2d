<svg height='100px' width='100px'  fill="#000000" xmlns:x="http://ns.adobe.com/Extensibility/1.0/" xmlns:i="http://ns.adobe.com/AdobeIllustrator/10.0/" xmlns:graph="http://ns.adobe.com/Graphs/1.0/" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve"><metadata><sfw xmlns="http://ns.adobe.com/SaveForWeb/1.0/"><slices></slices><sliceSourceBounds height="263" width="829" y="-146.5" x="-146.5" bottomLeftOrigin="true"></sliceSourceBounds><optimizationSettings></optimizationSettings></sfw></metadata><g display="none"><rect x="-330" display="inline" width="185" height="99"></rect><rect x="-138" y="-36" display="inline" width="100" height="30"></rect><text transform="matrix(1 0 0 1 -72 -19.5)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">http://thenounproject.com</text><text transform="matrix(1 0 0 1 -130.834 -24.5)" display="inline" fill="#000000" font-family="'Helvetica-Bold'" font-size="6.1578">The Noun Project</text><text transform="matrix(1 0 0 1 -129.7139 -19.7002)" display="inline" fill="#000000" font-family="'Helvetica-Bold'" font-size="4">Icon Template</text><text transform="matrix(1 0 0 1 -316.5 10.5)" display="inline" fill="#000000" font-family="'Helvetica-Bold'" font-size="6.1578">Reminders</text><line display="inline" fill="#000000" stroke="#000000" stroke-miterlimit="10" x1="-130" y1="-14.5" x2="-120" y2="-14.5"></line><line display="inline" fill="#000000" stroke="#000000" stroke-miterlimit="10" x1="-317" y1="16.5" x2="-300" y2="16.5"></line><g display="inline"><g><g><rect x="-308.802" y="31.318" fill="#000000" width="8.721" height="8.642"></rect><path fill="#000000" d="M-302.455,42.312h4.747v-4.703h-4.747V42.312z M-297.266,42.749h-5.63V37.17h5.63V42.749      L-297.266,42.749z M-304.221,44.062h8.279v-8.203h-8.279V44.062L-304.221,44.062z M-295.5,44.5h-9.163v-9.079h9.163V44.5      L-295.5,44.5z"></path><polygon fill="#000000" points="-304.149,44.133 -304.292,43.991 -296.013,35.787 -295.871,35.929     "></polygon></g></g></g><rect x="-317" y="58" display="inline" fill="none" width="35" height="32.5"></rect><text transform="matrix(1 0 0 1 -317 60.1572)" display="inline" fill="#000000" font-family="'Helvetica-Bold'" font-size="3">Strokes</text><text transform="matrix(1 0 0 1 -317 65.1572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">Try to keep strokes at 4px</text><text transform="matrix(1 0 0 1 -317 70.1572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">Minimum stroke weight is 2px</text><text transform="matrix(1 0 0 1 -317 74.6572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">For thicker strokes use even </text><text transform="matrix(1 0 0 1 -317 77.6572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">numbers: 6px, 8px etc.</text><text transform="matrix(1 0 0 1 -317 82.1572)" display="inline" fill="#000000" font-family="'Helvetica-Bold'" font-size="2.4">Remember to expand strokes </text><text transform="matrix(1 0 0 1 -317 85.1572)" display="inline" fill="#000000" font-family="'Helvetica-Bold'" font-size="2.4">before saving as an SVG </text><rect x="-274.5" y="58" display="inline" fill="none" width="35" height="32.5"></rect><text transform="matrix(1 0 0 1 -274.5 60.1572)" display="inline" fill="#000000" font-family="'Helvetica-Bold'" font-size="3">Size</text><text transform="matrix(1 0 0 1 -274.5 65.1572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">Cannot be wider or taller than </text><text transform="matrix(1 0 0 1 -274.5 68.6572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">100px (artboar size)</text><text transform="matrix(1 0 0 1 -274.5 73.6572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">Scale your icon to fill as much of </text><text transform="matrix(1 0 0 1 -274.5 76.6572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">the Safe Area as possible (within </text><text transform="matrix(1 0 0 1 -274.5 79.6572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">the guides)</text><rect x="-232" y="58" display="inline" fill="none" width="35" height="32.5"></rect><text transform="matrix(1 0 0 1 -232 60.1572)" display="inline" fill="#000000" font-family="'Helvetica-Bold'" font-size="3">Ungroup</text><text transform="matrix(1 0 0 1 -232 65.1572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">If your design has more than one </text><text transform="matrix(1 0 0 1 -232 68.1572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">shape, make sure to ungroup.</text><rect x="-188" y="58" display="inline" fill="none" width="35" height="32.5"></rect><text transform="matrix(1 0 0 1 -188 60.1572)" display="inline" fill="#000000" font-family="'Helvetica-Bold'" font-size="3">Save as</text><text transform="matrix(1 0 0 1 -188 65.1572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">Save as .SVG and make sure </text><text transform="matrix(1 0 0 1 -188 68.1572)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.4">“Use Artboards” is checked.</text><text transform="matrix(1.0074 0 0 1 -263.542 30.5938)" display="inline" fill="#000000" font-family="'Helvetica'" font-size="2.5731">100px</text><text transform="matrix(1.0074 0 0 1 -179 39)" display="inline" fill="#000000" font-family="'Helvetica-Bold'" font-size="5.1462">.SVG</text><rect x="-264.514" y="34.815" display="inline" fill="#000000" width="10.261" height="10.185"></rect><rect x="-264.477" y="31.766" display="inline" fill="#000000" width="0.522" height="2.337"></rect><rect x="-254.812" y="31.766" display="inline" fill="#000000" width="0.523" height="2.337"></rect><rect x="-265" y="32.337" display="inline" fill="#000000" width="11.233" height="0.572"></rect><g display="inline"><rect x="-221.805" y="33.844" fill="#000000" width="10.305" height="10.156"></rect><rect x="-214.809" y="28.707" fill="#000000" width="3.308" height="3.261"></rect></g><rect x="-316.5" y="22.5" display="inline" fill="#000000" stroke="#000000" stroke-miterlimit="10" width="30" height="30"></rect><rect x="-274.5" y="22.5" display="inline" fill="#000000" stroke="#000000" stroke-miterlimit="10" width="30" height="30"></rect><rect x="-231.5" y="22.5" display="inline" fill="#000000" stroke="#000000" stroke-miterlimit="10" width="30" height="30"></rect><rect x="-187.5" y="22.5" display="inline" fill="#000000" stroke="#000000" stroke-miterlimit="10" width="30" height="30"></rect></g><g><g><rect x="-93" y="31" fill="#000000" width="9" height="25"></rect><g><rect x="-101" y="39" fill="#000000" width="25" height="9"></rect></g></g><path fill="#000000" d="M-88.474,14.575c-15.698,0-28.425,12.726-28.425,28.425s12.727,28.425,28.425,28.425   c15.7,0,28.426-12.727,28.426-28.425C-60.048,27.301-72.773,14.575-88.474,14.575z M-88.474,63.95   c-11.569,0-20.948-9.38-20.948-20.95c0-11.57,9.379-20.949,20.948-20.949c11.571,0,20.949,9.379,20.949,20.949   C-67.524,54.57-76.902,63.95-88.474,63.95z"></path><path fill="#000000" d="M-101.789,67.8c6.104,9.593,13.314,18.214,13.314,18.214s7.216-8.621,13.317-18.214H-101.789z"></path><g><g><path fill="#000000" d="M85,67.184C85,70.947,81.947,74,78.184,74H21.817C18.053,74,15,70.947,15,67.184V37.817     C15,34.052,18.053,31,21.817,31h56.365C81.947,31,85,34.052,85,37.817V67.184z M82,37.903C82,35.195,79.805,33,77.098,33H20.903     C18.195,33,16,35.195,16,37.903v29.193C16,69.805,18.195,72,20.903,72h56.193C79.805,72,82,69.805,82,67.098V37.903z"></path><path fill="#000000" d="M86,67.213C86,71.514,82.514,75,78.212,75H21.788C17.487,75,14,71.514,14,67.213V37.787     C14,33.486,17.487,30,21.788,30h56.424C82.514,30,86,33.486,86,37.787V67.213z M77.568,73h0.025C80.818,73,83,70.438,83,67.213     v-0.025C83,70.426,80.809,73,77.568,73z M21.391,32C18.166,32,16,34.943,16,38.167v29.046C16,70.438,18.166,73,21.391,73h0.026     C18.178,73,16,70.426,16,67.188V38.193C16,34.955,18.178,32,21.417,32h56.151C80.809,32,83,34.955,83,38.193v-0.027     C83,34.943,80.818,32,77.596,32H21.391z M82,37.933C82,35.761,80.238,34,78.064,34H21.935C19.762,34,18,35.761,18,37.933v29.133     C18,69.239,19.762,71,21.935,71h56.131C80.238,71,82,69.239,82,67.066V37.933z"></path></g></g><g><g><path fill="#000000" d="M16,47v20.238C16,70.575,18.796,73,22.132,73h58.132C83.6,73,86,70.575,86,67.238V47H16z M56,66H18v-3h38     V66z M66.907,51.023l3.055,6.191l6.833,0.992l-4.943,4.818l1.166,6.806l-6.109-3.213l-6.112,3.213l1.168-6.806l-4.944-4.818     l6.834-0.992L66.907,51.023z"></path></g></g><path fill="#000000" d="M190.49,17.665c-0.909-0.62-2.408-0.638-3.332-0.041L156.59,37.391c-0.924,0.597-1.221,1.86-0.66,2.807   l0.051,0.085c0.562,0.946,1.021,2.621,1.021,3.721V82c0,1.1,0.899,2,2,2h18c1.101,0,2-0.9,2-2V60c0-1.1,0.899-2,2-2h13   c1.101,0,2,0.9,2,2v22c0,1.1,0.899,2,2,2h19c1.101,0,2-0.9,2-2V44.676c0-1.1,0.39-2.812,0.864-3.804l0.283-0.592   c0.476-0.992,0.121-2.311-0.789-2.931L190.49,17.665z"></path><path fill="#000000" d="M349,36.426L351.52,28l8.48,9.613C356.325,37.896,349,36.426,349,36.426z"></path><rect x="296.582" y="54.258" fill="#000000" width="4.46" height="6.021"></rect><g><g><path fill="#000000" d="M464,85c-19.3,0-35-15.701-35-35s15.7-35,35-35c19.299,0,35,15.701,35,35S483.299,85,464,85z M464,21.604     c-15.658,0-28.396,12.738-28.396,28.396S448.342,78.396,464,78.396c15.657,0,28.396-12.738,28.396-28.396     S479.657,21.604,464,21.604z"></path></g><g><g><path fill="#000000" d="M455.602,66.292c0.154-0.681,0.385-1.582,0.692-2.703l4.15-15.159c0.045-0.176,0.082-0.352,0.114-0.528      c0.033-0.175,0.051-0.34,0.051-0.494c0-0.9-0.285-1.46-0.855-1.681c-0.571-0.219-1.613-0.362-3.127-0.428v-1.78      c1.266-0.088,3.104-0.269,5.514-0.544c2.411-0.274,4.118-0.511,5.121-0.708l2.88-0.561l-5.239,19.214      c-0.439,1.646-0.735,2.79-0.891,3.428c-0.395,1.627-0.594,2.682-0.594,3.163c0,0.484,0.11,0.798,0.331,0.939      c0.219,0.144,0.461,0.215,0.726,0.215c0.684,0,1.53-0.582,2.543-1.747c1.015-1.164,1.906-2.384,2.678-3.658l1.482,0.956      c-2.2,3.229-3.884,5.404-5.051,6.525c-1.914,1.846-3.959,2.769-6.137,2.769c-1.232,0-2.338-0.378-3.316-1.138      c-0.979-0.757-1.468-1.916-1.468-3.478C455.206,68.348,455.338,67.479,455.602,66.292z M471.475,27.963      c0.879,0.879,1.319,1.945,1.319,3.197s-0.44,2.324-1.319,3.213c-0.879,0.89-1.943,1.335-3.196,1.335      c-1.252,0-2.323-0.445-3.213-1.335c-0.891-0.89-1.335-1.961-1.335-3.213s0.444-2.317,1.335-3.197      c0.89-0.878,1.961-1.318,3.213-1.318S470.596,27.084,471.475,27.963z"></path></g></g></g><g><path fill="#000000" d="M-81.63,169.562c0.071-2.527,0.354-5.067,0.856-7.408c0.077-0.362,0.168-0.168,0.256-1.154h-38.063    c-4.52,0-4.419,3.773-4.419,7.991v5.728c0,4.219-0.1,7.281,4.419,7.281h38.258C-81.291,178-81.744,173.621-81.63,169.562z"></path><path fill="#000000" d="M-104.552,192h-15.203c-3.839,0-3.245,2.518-3.245,6.076v4.832c0,3.559-0.594,6.092,3.245,6.092h15.357    C-105.088,202.2-105.006,196.533-104.552,192z"></path><path fill="#000000" d="M-53,174.719v-5.728c0-4.218-1.416-7.991-6.558-7.991h-18.718c-0.141,0.986-0.271,0.949-0.392,1.438    C-80.12,168.398-79.888,175-78.069,182h18.512C-54.416,182-53,178.938-53,174.719z M-59.808,171.773    c0-4.754,1.443-8.609,2.441-8.609c0.027,0,0.055,0.014,0.082,0.02c-0.697,1.434-0.562,5.27-0.55,8.592    c0.015,3.852-0.147,7.156,0.55,8.59c-0.027,0.007-0.055,0.021-0.082,0.021C-58.364,180.385-59.808,176.529-59.808,171.773z"></path><path fill="#000000" d="M-53.352,212.902L-54,200.812v-0.638l0.648-12.092h-3.688c0,0-38.085,3.149-43.814,3.149    c0,0-1.444-0.246-2.136,1.229l0.99,7.902V201l-0.99,7.523c0.692,1.476,2.136,1.229,2.136,1.229c5.729,0,43.814,3.149,43.814,3.149    H-53.352L-53.352,212.902z M-57.711,210.589v-20.495c0-0.344,0.436-0.621,0.972-0.621c0.537,0,0.974,0.277,0.974,0.621v20.495    c0,0.345-0.437,0.623-0.974,0.623C-57.275,211.212-57.711,210.934-57.711,210.589z"></path></g><path fill="#000000" d="M73.074,173.111l2.658-8.62l8.613,9.937C80.573,174.686,73.074,173.111,73.074,173.111z"></path><g><path fill="#000000" d="M199.176,180.064c-1.24,0.595-1.771,2.09-1.172,3.332c0.6,1.244,2.084,1.771,3.332,1.174    c1.24-0.594,1.771-2.088,1.168-3.332C201.913,179.999,200.424,179.469,199.176,180.064z"></path><rect x="179.744" y="179.379" transform="matrix(0.4319 0.9019 -0.9019 0.4319 276.3029 -60.4864)" fill="#000000" width="12.841" height="19.405"></rect><path fill="#000000" d="M213.586,176.793c-3.332-6.305-9.953-10.605-17.58-10.605c-4.279,0-8.24,1.354-11.482,3.653    c-2.463-2.269-5.75-3.653-9.364-3.653c-6.517,0-11.979,4.508-13.447,10.574c-5.506,0.896-9.711,5.68-9.711,11.441    c0,6.404,5.192,11.594,11.599,11.594c0.812,0,1.598-0.083,2.36-0.242c1.699,7.137,8.106,12.445,15.763,12.445    c5.139,0,9.715-2.395,12.688-6.127c0.525,0.04,1.061,0.068,1.598,0.068c5.504,0,10.483-2.239,14.082-5.854    c0.465,0.059,0.934,0.087,1.414,0.087c6.51,0,11.784-5.276,11.784-11.784C223.286,182.596,219.1,177.781,213.586,176.793z     M203.42,193.064l-25.859,12.375l-9.549-19.948l25.863-12.376l9.975,4.32l2.924,6.111L203.42,193.064z"></path></g><g><path fill="#000000" d="M360.236,181.88c0.775-0.78,0.521-1.554-0.567-1.717l-20.795-3.124c-1.088-0.163-2.365-1.109-2.839-2.102    l-9.35-19.58c-0.475-0.991-1.25-0.991-1.724,0l-9.347,19.58c-0.475,0.992-1.752,1.938-2.84,2.102l-20.797,3.124    c-1.088,0.163-1.345,0.937-0.568,1.717l15.094,15.212c0.775,0.781,1.262,2.309,1.082,3.393l-3.576,21.543    c-0.18,1.086,0.465,1.543,1.43,1.019l18.627-10.125c0.967-0.524,2.549-0.524,3.515,0l18.627,10.125    c0.966,0.524,1.608,0.067,1.431-1.019l-3.576-21.543c-0.181-1.084,0.307-2.61,1.082-3.393L360.236,181.88z M339,192    c0,1.1-0.9,2-2,2h-6c-1.1,0-2,0.9-2,2v6c0,1.1-0.9,2-2,2h-3c-1.1,0-2-0.9-2-2v-6c0-1.1-0.9-2-2-2h-5c-1.1,0-2-0.9-2-2v-2    c0-1.1,0.9-2,2-2h5c1.1,0,2-0.9,2-2v-6c0-1.1,0.9-2,2-2h3c1.1,0,2,0.9,2,2v6c0,1.1,0.9,2,2,2h6c1.1,0,2,0.9,2,2V192z"></path></g><g><polygon fill="#000000" points="447,167 493,167 493,163 488,163 488,160 483,160 483,157 434,157 434,218 438,218 438,162     440,162 440,223 444,223 444,165 447,165   "></polygon><path fill="#000000" d="M447,182v45h49v-45H447z M467.944,214.404c-0.485,1.07-1.157,1.966-2.016,2.687    c-0.857,0.722-1.859,1.257-3.004,1.604s-2.376,0.521-3.693,0.521c-1.096,0-2.228-0.1-3.396-0.299s-2.114-0.484-2.836-0.857    l1.307-4.029c0.646,0.322,1.361,0.585,2.146,0.783c0.783,0.199,1.66,0.299,2.63,0.299c1.542,0,2.718-0.41,3.526-1.231    c0.808-0.821,1.213-1.815,1.213-2.985c0-1.418-0.424-2.34-1.27-3.012c-0.847-0.672-2.178-0.885-3.993-0.885H456v-2.818    l4.451-6.158l2.097-1.52L459.568,197H454v-4h14v2.227l-5.311,7.051L461,203.285v0.188l1.393-0.225    c0.869,0.074,1.609,0.273,2.395,0.598c0.783,0.322,1.423,0.789,2.007,1.398s1.03,1.361,1.379,2.257s0.511,1.929,0.511,3.099    C468.683,212.066,468.43,213.335,467.944,214.404z M490,219h-15v-4h6v-15.146l0.16-2.539l-1.99,2.203l-3.421,2.274l-2.103-2.862    l8.458-6.93H485v23h5V219z"></path><g><path fill="#000000" d="M460.644,177.208c0-0.315-0.115-0.574-0.349-0.772c-0.232-0.197-0.525-0.379-0.876-0.541     c-0.352-0.164-0.733-0.332-1.144-0.506c-0.411-0.173-0.793-0.386-1.145-0.639c-0.352-0.252-0.645-0.566-0.877-0.943     c-0.232-0.375-0.349-0.854-0.349-1.439c0-0.506,0.084-0.943,0.252-1.314c0.169-0.371,0.409-0.681,0.721-0.928     c0.312-0.248,0.681-0.432,1.106-0.55c0.425-0.119,0.899-0.179,1.426-0.179c0.604,0,1.173,0.053,1.708,0.155     c0.534,0.104,0.975,0.255,1.321,0.453l-0.64,1.708c-0.218-0.139-0.544-0.266-0.979-0.379c-0.438-0.113-0.906-0.171-1.411-0.171     c-0.477,0-0.839,0.095-1.092,0.282c-0.252,0.188-0.379,0.44-0.379,0.758c0,0.297,0.115,0.545,0.35,0.742     c0.231,0.197,0.523,0.382,0.876,0.55c0.352,0.168,0.731,0.342,1.144,0.521c0.411,0.178,0.792,0.394,1.145,0.646     c0.352,0.252,0.644,0.563,0.876,0.937c0.231,0.371,0.35,0.834,0.35,1.389s-0.092,1.031-0.274,1.433     c-0.184,0.401-0.44,0.735-0.771,1.003c-0.332,0.268-0.732,0.467-1.203,0.594c-0.471,0.129-0.987,0.193-1.552,0.193     c-0.743,0-1.396-0.068-1.961-0.208s-0.979-0.276-1.247-0.416l0.653-1.737c0.107,0.061,0.25,0.125,0.423,0.193     s0.369,0.133,0.587,0.191c0.218,0.06,0.447,0.109,0.69,0.148c0.241,0.04,0.491,0.061,0.75,0.061c0.604,0,1.065-0.103,1.389-0.306     C460.482,177.934,460.644,177.624,460.644,177.208z"></path><path fill="#000000" d="M467,169.604h6.164v1.812h-4.129v2.421h3.758v1.812h-3.758v2.541h4.203V180H467V169.604z"></path><path fill="#000000" d="M477.723,169.708c0.416-0.079,0.874-0.142,1.374-0.187s0.997-0.066,1.493-0.066     c0.516,0,1.032,0.043,1.552,0.127c0.521,0.084,0.987,0.252,1.403,0.505c0.416,0.252,0.755,0.604,1.019,1.056     c0.262,0.449,0.394,1.037,0.394,1.76c0,0.652-0.114,1.205-0.342,1.654c-0.229,0.451-0.53,0.819-0.906,1.106     s-0.805,0.495-1.284,0.624s-0.979,0.193-1.493,0.193c-0.049,0-0.129,0-0.236,0c-0.109,0-0.224-0.004-0.343-0.008     c-0.117-0.006-0.234-0.013-0.349-0.022c-0.114-0.011-0.195-0.021-0.245-0.029V180h-2.035L477.723,169.708L477.723,169.708z      M480.753,171.208c-0.198,0-0.387,0.008-0.563,0.022c-0.18,0.015-0.322,0.031-0.432,0.052v3.37     c0.039,0.011,0.1,0.021,0.178,0.031c0.079,0.008,0.164,0.017,0.254,0.021c0.089,0.006,0.176,0.008,0.26,0.008s0.146,0,0.186,0     c0.269,0,0.532-0.024,0.795-0.073c0.263-0.051,0.497-0.142,0.705-0.275c0.208-0.133,0.374-0.318,0.498-0.557     c0.123-0.238,0.187-0.55,0.187-0.937c0-0.326-0.061-0.597-0.18-0.81c-0.117-0.213-0.273-0.384-0.468-0.513     c-0.191-0.129-0.413-0.218-0.661-0.269C481.263,171.232,481.01,171.208,480.753,171.208z"></path></g></g><path fill="#000000" d="M69.879,175.907L73.391,163H17.438c-1.891,0-3.438,1.6-3.438,3.555v45.491C14,214,15.548,216,17.438,216   h65.318c1.893,0,3.243-2,3.243-3.954v-34.424C80,178.025,69.879,175.907,69.879,175.907z M57,170h10v3H57V170z M57,176h8v3h-8V176z    M22,170h31v18H22V170z M37,209H22v-3h15V209z M37,203H22v-3h15V203z M37,197H22v-3h15V197z M59,209H41v-3h18V209z M62,185h-5v-3h5   V185z M78,203H41v-3h37V203z M78,197H41v-3h37V197z"></path><g><rect x="332" y="33" fill="#000000" width="6" height="6"></rect><path fill="#000000" d="M345.818,38.864L349,26h-55.178c-1.864,0-3.822,1.849-3.822,3.776v44.861c0,1.928,1.958,3.362,3.822,3.362    h64.414c1.865,0,3.764-1.435,3.764-3.362V40.691C356,41.09,345.818,38.864,345.818,38.864z M321,31h6v5h-6V31z M331,48v6h-6v-6    H331z M316,37h5v5h4v6h-6v-6h-3v3h-5v-5h5V37z M325,53v5h-5v-5H325z M319,49v5h-4v-5H319z M310,31h6v6h-6V31z M297,31h10v10h-10    V31z M307,74h-10V64h10V74z M331,64h10v10h-10l0,0h-12v-5h-4v5h-5V59h-4v-5h-5v-5h-4v-4h14v4h-5v5h5v5h4v6h4v-6h6v6h6 M341,39.768    V54h-5v5h5v-5h-5v5h-5v-5h4v-5h-4v-5h4v-2h-6V31h12V39.768z"></path><rect x="299" y="66" fill="#000000" width="6" height="6"></rect><rect x="319" y="65" fill="#000000" width="6" height="4"></rect><rect x="299" y="33" fill="#000000" width="6" height="5"></rect></g><rect x="333" y="66" fill="#000000" width="6" height="6"></rect></g></svg>