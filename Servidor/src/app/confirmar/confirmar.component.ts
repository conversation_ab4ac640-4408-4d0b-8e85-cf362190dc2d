import {Component, Input, OnInit} from '@angular/core';
import {ClienteService} from "../services/cliente.service";
import {ActivatedRoute} from "@angular/router";
import {ArmazenamentoService} from "../services/armazenamento.service";

@Component({
  selector: 'app-confirmar',
  templateUrl: './confirmar.component.html',
  styleUrls: ['./confirmar.component.scss']
})
export class ConfirmarComponent implements OnInit {
  confirmando = true;
  private idCartao: string;
  public contato: any;
  public mensagemErro: string;
  confirmou = false;

  constructor(private clienteService: ClienteService, private activatedRoute: ActivatedRoute,
              private armazenamentoService: ArmazenamentoService) {
    let params: any = this.activatedRoute.snapshot.params;
    this.idCartao = params.idCartao;
  }

  ngOnInit() {

    this.clienteService.confirmarContato(this.idCartao).then(contato => {
      this.confirmando = false;
      this.confirmou = true;
      this.contato  = contato;

      this.armazenamentoService.salveSemExpirar("idCliente", contato.id);
    }).catch(mensagem => {
      this.confirmando = false;
      this.confirmou = false;
      this.mensagemErro = mensagem;
    })
  }
}
