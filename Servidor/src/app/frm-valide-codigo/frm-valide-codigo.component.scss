.dados_cliente {
  background-image: url('/assets/fidelidade/bg_roxo.png');
  min-height: 300px;
}

.caixa_titulo {
  padding-top:20px;
  text-align: center;
  color: white;
}

.titulo {
  font-size: 32px;
  font-weight: 700;
  display: block;
  line-height: 1.2;
}

.subtitulo {
  font-size: 12px ;
}

.icone {
  display: inline-block;
  fill: white;
  vertical-align: middle;
}

.on .icone.selo {
  fill: #6DB31B;
}

.off .icone.selo {
  fill: #C2C4CC;
}

.sem .icone.selo {
  fill: transparent;
}

.sem .valor {
  display: none;
}

.icone.selo {
  width: 100%;
  text-align: center;
}

.col {
  padding-left: 5px;
  padding-right: 5px;
}


.icone .tam1 {
  width: 11px;
  height: 11px;

}

.cartao {
  background: white;
  margin-left: 10px;
  margin-right: 10px;
  padding: 15px;
}

.cartao.conteudo {
  margin-top: -200px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-right-radius: 45px;
  min-height: 350px;
  box-shadow: 0 4px 10px -2px #E2E3E3;
}

/*
.cartao.conteudo {
  box-shadow: 0 4px 10px -2px #E2E3E3;
  min-height: 190px;
  border-top: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
*/


.cartao.semborda {
  margin-top: 20px;
  border: 0 none;
  background-color: transparent;
  padding: 0;
}

@media screen and (min-width: 550px) {
  .cartao.conteudo, .cartao.semborda  {
    max-width: 500px;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }
}


.imagem_empresa {
  width: 80px;
  height: 80px;
  float: left ;
}

.detalhes_empresa  {
  float:left;
  margin-left: 10px;
  display: inline-block;
  width: calc(100% - 90px);
}

.nome_empresa {
  font-size: 24px;
  color: black;
  font-weight: 500;
  display: block;
  line-height: 1.2;
  max-height: 1.2em;
  overflow: hidden;
}

.endereco {
  font-size: 11px;
}

.whatsapp {
  display: block;
  margin-bottom: 5px;
}

.whatsapp span{
  font-size:12px;
  font-weight: 600;

  color: #199D0F
}

.dados_empresa {
  min-height: 90px;
  overflow: hidden;
}

.linha {
  border-bottom: #EFEFEF solid 1px;
}

.pontuacao {
  margin-top: 15px;
  margin-bottom: 15px;
}
.brinde {
  margin-top: 10px;
}
.valor {
  position: absolute;
  color: white;
  font-size: 20px;
  top: 10px;
  width: 100%;
  text-align: center;
}

.row {
  padding-left: 5px;
  padding-right: 5px;
}

.container_selo {
  position: relative;
}

.brinde {
  text-align: center;
  position: relative;
}

.preco_troca {
  font-weight: 600;
}

.nome_brinde {
  display: inline-block;
  margin-top: 5px;
  font-size: 16px;
  background: #4b4b4b;
  color: white;
  margin-left: 2px;
  padding: 5px 10px 5px 10px;
  border-radius: 50px;
  font-weight: 200;
}

.faltam_selos {
  color: #3e48bc;
  margin-bottom: 10px;
}

.foto_brinde {
  display: block;
  float:none;
  margin:0 auto;
  width: 150px;
  border-radius: 30px;
  margin-top: 5px;

}

.botao {
  border: 1px solid black;
  padding: 15px;
  border-radius: 30px;
  text-align: center;
  font-weight: 500;
  font-size: 14px;
}

.botao.azul {
  border: 1px solid #3B86FF;
  color: #3B86FF;

}

.botao.cinza {
  border: 1px solid #808495;
  color: #808495;
}

.rodape {
  margin-top: 30px;
}

.logo {
  width: 100px;
  margin: 0 auto;
}

.carregando {
  text-align: center;
}

.carrossel {
  padding-bottom: 30px;
  outline: none;
}

.pontos {
  background: #6DB31B;
  color: white;
  text-align: center;
  margin: 15px;
  padding: 4px;
  border-bottom-left-radius: 20px;
  border-top-left-radius: 2px;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 2px;

}

.pontos-interno {
  border: white solid 1px;
  line-height: 1.2em;
  font-size: 40px;
  font-weight: 600;

  border-bottom-left-radius: 20px;
  border-top-left-radius: 2px;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 2px;
}

.pontos-interno small{
  position: absolute;
  top: 5px;
  left: 63px;
}

.icone.estrela {
  position: absolute;
  left: 35px;
  bottom : 25%;

}

.minimo-troca {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  line-height: 1em;
}

.margem-fim {
  margin-bottom: 15px;
}

.container-scroll {
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}

.container-scroll .col {
  margin-left: 5px;
  margin-right: 5px;
}

.container-scroll .caixa_brinde {
  border: 1px solid #E2E3E3;
  border-radius: 5px;

  box-shadow: 0 4px 10px -2px #E2E3E3;
  margin-bottom: 10px;
  padding: 5px;
}


.container-scroll .foto_brinde {
  height: 150px;
  width: unset;
  min-height: 150px;
  margin-left: 5px;
  margin-right: 5px;
}

.nome_brinde_pontos {
  font-size: 15px;
  font-weight: 600;

}

.container-scroll .preco_troca {
  font-size: 11px;
  font-weight: 400;
  line-height: 1em;
}

.container-scroll .preco_troca.nao_atingiu {
  color: #F67682;
}

.container-scroll .preco_troca.atingiu {
  color: #6DB31B;
}

.botao_troca {
  margin-left: 5px;
  margin-right: 5px;
  margin-bottom: 10px;
  border-radius: 20px;
  font-size: 10px;
  line-height: 2.5em;
}

.pode_trocar {
  background: #6DB31B;
  color: white;
}

.container-scroll .faltam_selos {
  border: 1px solid #E2E3E3;
}


.pode_trocar .icone.estrela {
  position: absolute;
  bottom: 11px;
  left: 12px;

}

.container_imagem {
  width: 100%;
  height: 150px;
  display: table;
  margin: 5px 0;
}

.container_imagem div {
  text-align: center;
  vertical-align: middle;
  display: table-cell;
}

.container_imagem div img {
  max-width: 200px;
  max-height: 150px;
  margin: 0 auto;
}

.btn {
  background: #3a44b9;
  font-size: 18px;
}

.btn:hover {
  color: #02c0ce;
}

.erro {
  color: red;
}


.was-validated .form-control:valid, .form-control.is-valid {
  background: none !important;
}

.countdown-grande {
  font-size: 32px;
  font-weight: 500;
}
