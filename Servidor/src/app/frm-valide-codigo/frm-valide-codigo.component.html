<div *ngIf="contato == null" class="mt-2">
  <div class="card">
    <div class="card-body" style="padding: 0.8rem;">
      <label>Seu telefone</label>

      <div id="cardCollpase1" class="collapse show">
        <div>
          <div class="row mt-1">
            <div class="col">
              <h4 class="mt-0"><span style="font-weight: bold;">{{parent.dadosContato.telefone}}</span></h4>
            </div>
          </div> <!-- end row -->
        </div>
      </div> <!-- collapsed end -->
    </div> <!-- end card-body -->
  </div>

  <h2>Verificar Código</h2>

  Informe o código que enviamos para o seu celular

  <form [ngClass]="{'needs-validation': !frmValidarCodigo.submitted, 'was-validated': frmValidarCodigo.submitted}"
        novalidate #frmValidarCodigo="ngForm" (ngSubmit)="onSubmitValidarCodigo()">
    <div class="form-group mb-2" >
      <input [(ngModel)]="codigo" #ctrlCodigo="ngModel" #txtCodigo  type="text" appAutoFocus class="form-control ng-tns-c12-1 ui-inputtext ui-widget ui-state-default ui-corner-all"
             placeholder="______" required name="codigo"/>
      <div class="invalid-feedback">
        <p *ngIf="ctrlCodigo.errors?.required">Código é obrigatório</p>
      </div>
    </div>
    <div class="form-group mb-2 text-center">
      <button class="btn btn-block" type="submit" [disabled]="enviando || (frmValidarCodigo.submitted && mensagemAguarde)"
              [ngClass]="{disabled: enviando || (frmValidarCodigo.submitted && mensagemAguarde)}"> Verificar Código </button>
      <div class="alert alert-danger mt-2" role="alert" *ngIf="mensagemErroEnvio && !frmValidarCodigo.valid" [innerHTML]="mensagemErroEnvio">
        {{mensagemErroEnvio}}
      </div>
      <div class="alert alert-danger mt-2" role="alert" *ngIf="mensagemFalhaEnvio" [innerHTML]="mensagemFalhaEnvio">
        {{mensagemErroEnvio}}
      </div>
    </div>
    <div [hidden]="frmValidarCodigo.submitted && mensagemAguarde">
      Se ainda não possui cartão, visite nossa loja e crie seu cartão agora mesmo!
    </div>
  </form>
</div>
<app-cartao-confirmado *ngIf="contato" [contato]="contato" [confirmou]="contato != null"></app-cartao-confirmado>
