.bg-pattern {
  background-image: url("/assets/fidelidade/zapkit-logo.png");
  background-size: cover;
}

.erro {
  color: red;
}

.linha-caixa {
  display: flex;
  align-items: center !important;
  padding: 10px
}

.caixa_login {
  margin: 0 auto;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 0 solid #f7f7f7;
  border-radius: 10px;
  width: 100%;
  padding:  15px 40px 15px 40px;
  max-width: 500px;

}


::ng-deep body {
  padding-bottom: 0;
}

.imagem_empresa{
  height: 90px;
}


input {
  font-size: 18px;
  font-weight: 500;
}

label {
  font-size: 18px;
  font-weight: 200;
}

.custom-checkbox {
  line-height: 18px;
}

input:focus {
  outline: none !important;
  border:1px solid #3B86FF;
  box-shadow: 0 0 10px #719ECE;}

.btn {
  background: #3a44b9;
  font-size: 18px;
}

.btn:hover {
  color: #d8d8d8;
  background: #2b358d;
}


.custom-control-input:checked ~ .custom-control-label::before {
  color: #3a44b9;
  border-color: #3a44b9;
  background-color: #3a44b9;
}
/*

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }

}
@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }

}
@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }

}
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }

}
.container {
  width: 100%;
  padding-right: 12px;
  padding-left: 12px;
  margin-right: auto;
  margin-left: auto;
}
*/
