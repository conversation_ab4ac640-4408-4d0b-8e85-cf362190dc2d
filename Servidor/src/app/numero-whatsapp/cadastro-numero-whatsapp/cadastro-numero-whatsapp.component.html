<div class="card-body" *ngIf="loading">
  <div class="k-i-loading ml-1 mr-1" style="font-size: 40px;"></div>
</div>

<div *ngIf="empresa">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a routerLink="/superadmin/empresas">Empresas</a></li>
            <li class="breadcrumb-item active">{{empresa.nome}}</li>
          </ol>
        </div>
        <h4 class="page-title">
          <button class="btn btn-outline-blue btn-rounded" (click)="voltar()">
            <i class="fa fa-arrow-left ct-point" ></i>
          </button>&nbsp;&nbsp; <i class="far fa-building"></i>
          <span *ngIf="!empresa?.id"> Nova Empresa </span>
          <span *ngIf="empresa?.id"> {{empresa.nome}} </span>
        </h4>
      </div>
    </div>
  </div>

  <div class="alert alert-success" role="alert" *ngIf="msgSucesso">
    <i class="mdi mdi-check-all mr-2"></i> {{msgSucesso}}
  </div>

  <div class="numeros-container">
    <!-- Header com botão de adicionar -->
    <div class="header-section">
      <div class="header-content">
        <div class="header-info">
          <h5 class="section-title">
            <i class="fab fa-whatsapp text-success mr-2"></i>
            Números WhatsApp
          </h5>
          <p class="section-subtitle">Gerencie os números WhatsApp da sua empresa</p>
        </div>
        <button class="btn-add-modern" (click)="novo()">
          <i class="fe-plus"></i>
          <span>Adicionar Número</span>
        </button>
      </div>
    </div>

    <!-- Grid de números -->
    <div class="numeros-grid">
      <div class="numero-card" *ngFor="let numero of objetos">
        <div class="card-header-modern">
          <div class="numero-info">
            <div class="numero-display">
              <i class="fab fa-whatsapp whatsapp-icon"></i>
              <span class="numero-text">{{numero.whatsapp | tel}}</span>
            </div>

            <!-- Badges modernos -->
            <div class="badges-container">
              <span *ngIf="isPrincipalECampanhas(numero)" class="badge-modern badge-purple">
                <i class="fas fa-crown mr-1"></i>Principal + Campanhas
              </span>
              <span *ngIf="isPrincipal(numero) && !isCampanhas(numero)" class="badge-modern badge-blue">
                <i class="fas fa-crown mr-1"></i>Principal
              </span>
              <span *ngIf="isCampanhas(numero) && !isPrincipal(numero)" class="badge-modern badge-green">
                <i class="fas fa-bullhorn mr-1"></i>Campanhas
              </span>
              <span *ngIf="!isPrincipal(numero) && !isCampanhas(numero)" class="badge-modern badge-gray">
                <i class="fas fa-check mr-1"></i>Ativo
              </span>
            </div>
          </div>

          <!-- Botões de ação -->
          <div class="action-buttons">
            <button class="btn-action btn-edit" (click)="editar(numero)" kendoTooltip title="Editar número">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn-action btn-delete"
                    *ngIf="podeRemover(numero)"
                    (click)="abraDialogRemover(numero)"
                    kendoTooltip title="Remover número">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- Controles de função -->
        <div class="card-body-modern">
          <!-- Radio Principal -->
          <div class="control-group">
            <div class="radio-modern">
              <input id="principal{{numero.id}}" name="principal" type="radio"
                     kendoRadioButton class="radio-input"
                     [(ngModel)]="numeroPrincipal.whatsapp"
                     [value]="numero.whatsapp"
                     (click)="mudarParaPrincipal(numero)"/>
              <label for="principal{{numero.id}}" class="radio-label">
                <div class="radio-content">
                  <i class="fas fa-crown radio-icon"></i>
                  <div class="radio-text">
                    <span class="radio-title">Número Principal</span>
                    <span class="radio-subtitle">Usado para atendimento geral</span>
                  </div>
                  <div class="radio-status">
                    <span *ngIf="isPrincipal(numero)" class="status-active">
                      <i class="fas fa-check-circle"></i>
                    </span>
                  </div>
                </div>
              </label>
            </div>
          </div>

          <!-- Radio Campanhas -->
          <div class="control-group">
            <div class="radio-modern">
              <input id="campanhas{{numero.id}}" name="campanhas" type="radio"
                     kendoRadioButton class="radio-input"
                     [(ngModel)]="numeroCampanhas.whatsapp"
                     [value]="numero.whatsapp"
                     (click)="mudarParaCampanhas(numero)"/>
              <label for="campanhas{{numero.id}}" class="radio-label">
                <div class="radio-content">
                  <i class="fas fa-bullhorn radio-icon"></i>
                  <div class="radio-text">
                    <span class="radio-title">Campanhas de Marketing</span>
                    <span class="radio-subtitle">Usado para envio de campanhas</span>
                  </div>
                  <div class="radio-status">
                    <span *ngIf="isCampanhas(numero)" class="status-active">
                      <i class="fas fa-check-circle"></i>
                    </span>
                  </div>
                </div>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Opção especial: usar principal para campanhas -->
      <div class="special-option" *ngIf="temNumeroCampanhas()">
        <div class="special-card">
          <div class="special-content">
            <div class="radio-modern">
              <input id="campanhasPrincipal" name="campanhas" type="radio"
                     kendoRadioButton class="radio-input"
                     [(ngModel)]="numeroCampanhas.whatsapp"
                     value="PRINCIPAL"
                     (click)="usarPrincipalParaCampanhas()"/>
              <label for="campanhasPrincipal" class="radio-label special-label">
                <div class="radio-content">
                  <i class="fas fa-sync-alt radio-icon special-icon"></i>
                  <div class="radio-text">
                    <span class="radio-title">Usar Número Principal</span>
                    <span class="radio-subtitle">Campanhas usarão o mesmo número do atendimento</span>
                  </div>
                </div>
              </label>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="objetos.length === 0">
        <div class="col-12">
          <div class="alert alert-warning">
            <h4>
              <i class="fas fa-exclamation-triangle"></i>
              Você ainda não tem números cadastrados
            </h4>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<kendo-dialog title="Confirmar a remoção" *ngIf="modalDeletar" (close)="close('cancel')" [minWidth]="250" [width]="450">
  <p style="margin: 30px; text-align: center;">Você tem certeza que deseja apagar a forma de pagamento <strong>{{objetoApagar.whatsapp}}</strong>?</p>
  <kendo-dialog-actions>
    <button kendoButton (click)="close('sim')" [primary]="true">Sim</button>
    <button kendoButton (click)="close('nao')">Não</button>
  </kendo-dialog-actions>
</kendo-dialog>
