<div class="card-body" *ngIf="loading">
  <div class="k-i-loading ml-1 mr-1" style="font-size: 40px;"></div>
</div>

<div *ngIf="empresa">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a routerLink="/superadmin/empresas">Empresas</a></li>
            <li class="breadcrumb-item active">{{empresa.nome}}</li>
          </ol>
        </div>
        <h4 class="page-title">
          <button class="btn btn-outline-blue btn-rounded" (click)="voltar()">
            <i class="fa fa-arrow-left ct-point" ></i>
          </button>&nbsp;&nbsp; <i class="far fa-building"></i>
          <span *ngIf="!empresa?.id"> Nova Empresa </span>
          <span *ngIf="empresa?.id"> {{empresa.nome}} </span>
        </h4>
      </div>
    </div>
  </div>

  <div class="alert alert-success" role="alert" *ngIf="msgSucesso">
    <i class="mdi mdi-check-all mr-2"></i> {{msgSucesso}}
  </div>

  <div class="">
    <button class="k-button btn btn-primary mb-3" (click)="novo()">
      <i class="fe-plus mr-1"></i>
      Novo Número Whatsapp
    </button>

    <div class="row">
      <div class="col-4" *ngFor="let numero of objetos">
        <div class="card">
          <div class="card-body">
            <div>
              <h4 class="mt-0">
                <b>{{numero.whatsapp | tel}}</b>
                <div style="position: absolute;top: 25px;right: 15px;">
                  <button (click)="editar(numero)" class="k-primary k-button">
                    <i class="fa fa-edit" kendoTooltip title="editar"></i>
                  </button>
                  <button kendoTooltip title="remover" class="ml-1 k-button k-grid-remove-command"
                          *ngIf="numeroPrincipal?.id !== numero.id"
                          (click)="abraDialogRemover(numero)"><i class="fa fa-trash"></i></button>
                </div>
              </h4>
              <div class="mt-2">
                <input id="principal{{numero.id}}" name="principal" type="radio"
                       kendoRadioButton  class="k-radio" [(ngModel)]="numeroPrincipal.whatsapp" [value]="numero.whatsapp" (click)="mudarParaPrincipal(numero)"/>
                <label for="principal{{numero.id}}" class="ml-1">
                  <span *ngIf="numeroPrincipal?.id !== numero.id">Definir como principal</span>
                  <span *ngIf="numeroPrincipal?.id === numero.id" class="badge badge-primary">Número Principal</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="objetos.length === 0">
        <div class="col-12">
          <div class="alert alert-warning">
            <h4>
              <i class="fas fa-exclamation-triangle"></i>
              Você ainda não tem números cadastrados
            </h4>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<kendo-dialog title="Confirmar a remoção" *ngIf="modalDeletar" (close)="close('cancel')" [minWidth]="250" [width]="450">
  <p style="margin: 30px; text-align: center;">Você tem certeza que deseja apagar a forma de pagamento <strong>{{objetoApagar.whatsapp}}</strong>?</p>
  <kendo-dialog-actions>
    <button kendoButton (click)="close('sim')" [primary]="true">Sim</button>
    <button kendoButton (click)="close('nao')">Não</button>
  </kendo-dialog-actions>
</kendo-dialog>
