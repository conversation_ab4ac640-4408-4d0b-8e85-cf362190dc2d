import { Component, OnInit } from '@angular/core';
import {EditEvent, GridDataResult, RemoveEvent} from "@progress/kendo-angular-grid";
import {ConstantsService} from "../../fidelidade/ConstantsService";
import {NumeroWhatsapp} from "../../pedidos/objetos/NumeroWhatsapp";
import {NumerosWhatsappService} from "../../services/numeros-whatsapp.service";
import {NovaFormaDePagamentoComponent} from "../../formas-de-pagamento/nova-forma-de-pagamento/nova-forma-de-pagamento.component";
import {NovoNumeroWhatsappComponent} from "../novo-numero-whatsapp/novo-numero-whatsapp.component";
import {DialogService} from "@progress/kendo-angular-dialog";
import {ActivatedRoute, Router} from "@angular/router";
import {EmpresasService} from "../../superadmin/services/empresas.service";

@Component({
  selector: 'app-cadastro-numero-whatsapp',
  templateUrl: './cadastro-numero-whatsapp.component.html',
  styleUrls: ['./cadastro-numero-whatsapp.component.scss']
})
export class CadastroNumeroWhatsappComponent implements OnInit {
  loading: any;
  empresa: any;
  objetos: Array<NumeroWhatsapp> = [];
  carregando: any;
  modalDeletar = false;
  objetoApagar: NumeroWhatsapp;
  numeroPrincipal: NumeroWhatsapp = new NumeroWhatsapp();
  numeroCampanhas: NumeroWhatsapp = new NumeroWhatsapp();
  msgSucesso = '';
  campanhasRadioValue: string = '';

  constructor(private router: Router, private route: ActivatedRoute, private constantsService: ConstantsService, private numerosWhatsappService: NumerosWhatsappService,
              private dialogService: DialogService, private empresasService: EmpresasService) {
    this.loading = true;

    this.empresasService.obtenha(this.route.snapshot.params.idEmpresa).then((resp: any) => {
      this.empresa = resp.data;

      this.loading = false;

      this.carregueNumeros();
    });
  }

  ngOnInit(): void {
  }

  editar(numeroWhatsapp: NumeroWhatsapp) {
    let altura = window.innerHeight - 100;
    const windowRef = this.dialogService.open({
      title: 'Alterar Número Whatsapp ',
      content: NovoNumeroWhatsappComponent,
      minWidth: 250,
      width: window.innerWidth > 600 ? 600 : window.innerWidth,
      maxHeight: altura
    });

    const telaNovaFormaDePagamento: NovoNumeroWhatsappComponent = windowRef.content.instance;

    telaNovaFormaDePagamento.empresa = this.empresa;
    telaNovaFormaDePagamento.windowRef = windowRef;
    telaNovaFormaDePagamento.numeroWhatsapp = Object.assign({}, numeroWhatsapp);

    windowRef.result.subscribe((result: any) => {
      this.carregueNumeros();
    });
  }

  abraDialogRemover(numero: NumeroWhatsapp) {
    this.objetoApagar = numero;

    this.modalDeletar = true;
  }

  onFilter($event: Event) {

  }

  novo() {
    let altura = window.innerHeight - 100;

    const windowRef = this.dialogService.open({
      title: 'Cadastro Número Whatsapp ',
      content: NovoNumeroWhatsappComponent,
      minWidth: 250,
      width: window.innerWidth > 600 ? 600 : window.innerWidth,
      maxHeight: altura
    });

    const telaNovaFormaDePagamento: NovoNumeroWhatsappComponent = windowRef.content.instance;
    telaNovaFormaDePagamento.empresa = this.empresa;
    telaNovaFormaDePagamento.windowRef = windowRef;

    windowRef.result.subscribe((result: any) => {
      this.carregueNumeros();
    });
  }

  close(status: string) {
    if( status === 'sim') {
      this.numerosWhatsappService.removaWhatsapp(this.objetoApagar).then( () => {
        this.objetoApagar = null;
        this.modalDeletar = false;

        this.carregueNumeros();
      });
    } else {
      this.objetoApagar = null;
      this.modalDeletar = false;
    }
  }

  voltar() {
    this.router.navigate(['/superadmin/empresas/' + this.empresa.id]).then( () => {

    });
  }

  mudarParaPrincipal(numeroClicado: NumeroWhatsapp) {
    for( let numero of this.objetos ) {
      numero.principal = false;
    }

    this.numeroPrincipal = numeroClicado;
    this.numeroPrincipal.principal = true;
    this.numerosWhatsappService.marqueNumeroComoPrincipal(this.numeroPrincipal, this.empresa).then( (resposta) => {
      this.msgSucesso = resposta;
    });
  }

  // Métodos de verificação
  isPrincipal(numero: NumeroWhatsapp): boolean {
    return this.numeroPrincipal?.id === numero.id;
  }

  isCampanhas(numero: NumeroWhatsapp): boolean {
    return this.numeroCampanhas?.id === numero.id;
  }

  isPrincipalECampanhas(numero: NumeroWhatsapp): boolean {
    return this.isPrincipal(numero) && this.isCampanhas(numero);
  }

  podeRemover(numero: NumeroWhatsapp): boolean {
    return !this.isPrincipal(numero) && !this.isCampanhas(numero);
  }



  // Métodos de ação para campanhas (com toggle)
  mudarParaCampanhas(numero: NumeroWhatsapp) {
    console.log('mudarParaCampanhas chamado para número:', numero);
    console.log('isCampanhas(numero):', this.isCampanhas(numero));

    // Se já é o número de campanhas, desmarca (toggle)
    if (this.isCampanhas(numero)) {
      this.numeroCampanhas = new NumeroWhatsapp();
      this.campanhasRadioValue = '';
      console.log('Removendo número de campanhas...');
      this.empresasService.removerNumeroWhatsappCampanhas(this.empresa)
        .then((resposta) => {
          console.log('Resposta remover:', resposta);
          this.msgSucesso = 'Campanhas voltarão a usar o número principal';
          this.carregueNumeros();
        })
        .catch((erro) => {
          console.error('Erro ao remover:', erro);
          this.msgSucesso = 'Erro ao remover número de campanhas: ' + erro;
        });
    } else {
      // Se não é o número de campanhas, marca como tal
      this.numeroCampanhas = numero;
      this.campanhasRadioValue = numero.whatsapp;
      console.log('Definindo número para campanhas:', numero.id);
      this.empresasService.definirNumeroWhatsappCampanhas(this.empresa, numero.id)
        .then((resposta) => {
          console.log('Resposta definir:', resposta);
          this.msgSucesso = `Número ${numero.whatsapp} agora é usado para campanhas`;
          this.carregueNumeros();
        })
        .catch((erro) => {
          console.error('Erro ao definir:', erro);
          this.msgSucesso = 'Erro ao definir número para campanhas: ' + erro;
        });
    }
  }

  private carregueNumeros() {
    console.log('carregueNumeros chamado');

    // Primeiro, recarregar a empresa para pegar as mudanças
    this.empresasService.logada().then((empresaAtualizada: any) => {
      console.log('Empresa recarregada:', empresaAtualizada);
      this.empresa = empresaAtualizada;

      this.numerosWhatsappService.obtenhaNumeros(this.empresa).then( (numeros) => {
        console.log('Números carregados:', numeros);
        this.objetos = numeros;

        for( let numero of this.objetos ) {
          if( numero.principal ) {
            this.numeroPrincipal = numero;
          }
        }

        // Identificar número de campanhas da empresa
        console.log('empresa.numeroWhatsappCampanhas:', this.empresa.numeroWhatsappCampanhas);
        if (this.empresa.numeroWhatsappCampanhas) {
          this.numeroCampanhas = this.objetos.find(n => n.id === this.empresa.numeroWhatsappCampanhas.id) || new NumeroWhatsapp();
          this.campanhasRadioValue = this.numeroCampanhas.whatsapp || '';
          console.log('Número de campanhas identificado:', this.numeroCampanhas);
        } else {
          this.numeroCampanhas = new NumeroWhatsapp();
          this.campanhasRadioValue = '';
          console.log('Nenhum número específico para campanhas');
        }
      });
    });
  }
}
