// Container principal
.numeros-container {
  padding: 0;
}

// Header moderno
.header-section {
  margin-bottom: 2rem;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
  }

  .header-info {
    .section-title {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
    }

    .section-subtitle {
      margin: 0.25rem 0 0 0;
      color: #6c757d;
      font-size: 0.9rem;
    }
  }
}

// Botão de adicionar moderno
.btn-add-modern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);

  &:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
  }

  i {
    font-size: 1rem;
  }
}

// Grid de números
.numeros-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

// Card moderno para cada número
.numero-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
}

// Header do card
.card-header-modern {
  padding: 1.5rem;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.numero-info {
  flex: 1;
}

.numero-display {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;

  .whatsapp-icon {
    font-size: 1.5rem;
    color: #25d366;
    margin-right: 0.75rem;
  }

  .numero-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    flex: 1;
  }

  .oculto-icon {
    font-size: 1rem;
    color: #ef6c00;
    margin-left: 0.5rem;
    opacity: 0.8;
    cursor: help;
  }
}

// Badges modernos
.badges-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.badge-modern {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;

  &.badge-purple {
    background: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
  }

  &.badge-blue {
    background: #e8f4fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
  }

  &.badge-green {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
  }

  &.badge-gray {
    background: #f5f5f5;
    color: #616161;
    border: 1px solid #e0e0e0;
  }

  &.badge-orange {
    background: #fff3e0;
    color: #ef6c00;
    border: 1px solid #ffcc02;
  }

  i {
    font-size: 0.7rem;
  }
}

// Botões de ação
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-action {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  cursor: pointer;

  &.btn-edit {
    background: #f8f9fa;
    color: #495057;

    &:hover {
      background: #e9ecef;
      color: #007bff;
    }
  }

  &.btn-delete {
    background: #f8f9fa;
    color: #495057;

    &:hover {
      background: #f5c6cb;
      color: #dc3545;
    }
  }

  i {
    font-size: 0.875rem;
  }
}

// Body do card
.card-body-modern {
  padding: 1.5rem;
}

// Grupos de controle
.control-group {
  margin-bottom: 1rem;

  &:last-child {
    margin-bottom: 0;
  }
}

// Radio buttons modernos
.radio-modern {
  .radio-input {
    display: none;
  }

  .radio-label {
    display: block;
    cursor: pointer;
    margin: 0;
    padding: 1rem;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    background: #ffffff;
    transition: all 0.2s ease;

    &:hover {
      border-color: #007bff;
      background: #f8f9fa;
    }
  }

  .radio-input:checked + .radio-label {
    border-color: #007bff;
    background: #f8f9fa;

    .radio-content {
      .radio-icon {
        color: #007bff;
      }

      .status-active {
        opacity: 1;
      }
    }
  }
}

.radio-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.radio-icon {
  font-size: 1.25rem;
  color: #6c757d;
  transition: color 0.2s ease;
  width: 20px;
  text-align: center;
}

.radio-text {
  flex: 1;

  .radio-title {
    display: block;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
  }

  .radio-subtitle {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
  }
}

.radio-status {
  .status-active {
    color: #28a745;
    font-size: 1.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
  }
}

// Seção especial
.special-option {
  grid-column: 1 / -1;
  margin-top: 1rem;
}

.special-card {
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #dee2e6;
  padding: 1.5rem;

  .special-content {
    .special-label {
      border: none !important;
      background: transparent !important;
      padding: 0 !important;

      &:hover {
        background: rgba(0, 123, 255, 0.05) !important;
        border-radius: 8px;
        padding: 0.5rem !important;
      }
    }

    .special-icon {
      color: #6c757d !important;
    }

    .radio-input:checked + .special-label {
      .special-icon {
        color: #007bff !important;
      }
    }
  }
}

// Estado vazio
.alert-warning {
  border-radius: 12px;
  border: none;
  background: #fff3cd;
  color: #856404;
  padding: 2rem;
  text-align: center;

  h4 {
    margin-bottom: 1rem;
    color: #856404;
  }

  i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
  }
}

// Responsividade
@media (max-width: 768px) {
  .numeros-grid {
    grid-template-columns: 1fr;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .numero-card {
    .card-header-modern {
      flex-direction: column;
      gap: 1rem;
    }

    .action-buttons {
      align-self: flex-end;
    }
  }
}

@media (max-width: 480px) {
  .header-section .header-content {
    padding: 1rem;
  }

  .numero-card {
    .card-header-modern,
    .card-body-modern {
      padding: 1rem;
    }
  }

  .radio-content {
    gap: 0.75rem;
  }

  .radio-text {
    .radio-title {
      font-size: 0.9rem;
    }

    .radio-subtitle {
      font-size: 0.8rem;
    }
  }
}

// Animações suaves
.numero-card,
.btn-add-modern,
.btn-action,
.radio-label {
  will-change: transform;
}

// Melhorias de acessibilidade
.radio-input:focus + .radio-label {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.btn-action:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

// Ícones WhatsApp
.text-success {
  color: #25d366 !important;
}

// Seção informativa
.info-section {
  margin-top: 2rem;
}

.info-card {
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  padding: 1.5rem;

  .info-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;

    .info-title {
      font-weight: 600;
      color: #2c3e50;
      font-size: 1rem;
    }
  }

  .info-content {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
      font-size: 0.9rem;
      color: #495057;

      &:last-child {
        margin-bottom: 0;
      }

      i {
        font-size: 0.85rem;
        width: 16px;
        text-align: center;
      }

      strong {
        color: #2c3e50;
      }
    }
  }
}