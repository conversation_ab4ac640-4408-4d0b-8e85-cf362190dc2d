.badge {
  font-size: 13px;
  padding: 5px 10px;
  max-height: 24px !important;
}

.acoes .btn{
  padding-left: 10px;padding-right: 10px;
}

.campanha {
  border: solid 1px #ccc;
}

// Seção de informações do WhatsApp
.whatsapp-info-modern {
  margin: 1rem 0;
  padding: 1rem;
  background: #f8fff9;
  border-radius: 8px;
  border-left: 4px solid #25d366;
  box-shadow: 0 2px 4px rgba(37, 211, 102, 0.1);
}

.whatsapp-display {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.whatsapp-icon {
  font-size: 1.5rem;
  color: #25d366;
  flex-shrink: 0;
}

.whatsapp-details {
  flex: 1;

  .whatsapp-number {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
  }

  .whatsapp-type {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: help;
    transition: all 0.2s ease;

    &.type-principal {
      background: #e3f2fd;
      color: #1976d2;
      border: 1px solid #bbdefb;
    }

    &.type-campanhas {
      background: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }

    &.type-both {
      background: #f3e5f5;
      color: #7b1fa2;
      border: 1px solid #e1bee7;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

.whatsapp-status {
  color: #28a745;
  font-size: 1.25rem;
  flex-shrink: 0;

  i {
    cursor: help;
  }
}

// Responsividade
@media (max-width: 768px) {
  .whatsapp-info-modern {
    margin: 0.75rem 0;
    padding: 0.75rem;
  }

  .whatsapp-display {
    gap: 0.75rem;
  }

  .whatsapp-details {
    .whatsapp-number {
      font-size: 1rem;
    }

    .whatsapp-type {
      font-size: 0.7rem;
      padding: 0.2rem 0.6rem;
    }
  }

  .whatsapp-icon {
    font-size: 1.25rem;
  }

  .whatsapp-status {
    font-size: 1.1rem;
  }
}
