// Variáveis
$primary-color: #2196f3;
$secondary-color: #607d8b;
$success-color: #4caf50;
$warning-color: #ff9800;
$danger-color: #f44336;
$text-color: #333;
$border-radius: 8px;
$box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

.container-fluid {
  padding: 20px;
}

// Loading
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;

  .k-i-loading {
    font-size: 40px;
    color: $primary-color;
  }
}

// Dashboard Header
.dashboard-header {
  margin-bottom: 1.5rem;

  .page-title {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 500;
    line-height: 1.2;
    
    i {
      color: #3498db;
    }
  }

  .gap-3 {
    gap: 1rem;
  }

  .btn-primary {
    background: $primary-color;
    border: none;
    padding: 8px 16px;
    border-radius: $border-radius;
    transition: all 0.3s ease;

    &:hover {
      background: darken($primary-color, 10%);
      transform: translateY(-1px);
    }
  }

  .search-container {
    flex: 1;
    max-width: 400px;
    margin-left: 16px;

    kendo-textbox {
      width: 100%;
    }
  }
}

// Mesas Grid
.mesas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 12px;
  margin-top: 24px;
}

// Mesa Card
.mesa-card {
  background: white;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .mesa-card-header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .mesa-title {
      margin: 0;
      font-size: 1.25rem;
      color: $text-color;
      display: flex;
      align-items: center;

      i {
        color: $secondary-color;
        font-size: 1rem;
      }
    }

    .mesa-actions {
      display: flex;
      gap: 8px;

      .btn-action {
        background: none;
        border: none;
        padding: 8px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;

        &.edit {
          color: $primary-color;
          &:hover { background: rgba($primary-color, 0.1); }
        }

        &.delete {
          color: $danger-color;
          &:hover { background: rgba($danger-color, 0.1); }
        }
      }
    }
  }

  .mesa-card-body {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

// QR Code Section
.qr-container {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex: 0 0 auto;
  width: 100%;

  .qr-image {
    flex: 0 0 150px;

    img {
      width: 100%;
      height: auto;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .qr-info {
    flex: 1;
    min-width: 0;
    padding-top: 8px;

    h5 {
      margin: 0 0 12px;
      color: $text-color;
      font-size: 1.1rem;
    }

    .download-qr {
      background: $success-color;
      border-color: $success-color;
      padding: 8px 16px;
      font-weight: 500;
      transition: all 0.3s ease;
      width: fit-content;

      i {
        font-size: 1rem;
      }

      &:hover {
        background: darken($success-color, 5%);
        border-color: darken($success-color, 5%);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .link-container {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 12px;
      width: 100%;
      overflow: hidden;

      i {
        color: $success-color;
        margin-top: 4px;
        flex-shrink: 0;
      }

      .link-wrapper {
        flex: 1;
        min-width: 0;
        overflow: hidden;
      }

      .mesa-link {
        color: $primary-color;
        text-decoration: none;
        display: block;
        word-break: break-all;
        overflow-wrap: break-word;
        width: 100%;
        line-height: 1.4;
        font-size: 0.9rem;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// Mesa Details
.mesa-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;

  .detail-item {
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      color: $secondary-color;
      width: 20px;
      flex-shrink: 0;
    }
  }
}

// Status Badges
.status-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  flex: 1;

  &.status-free {
    background: rgba($success-color, 0.1);
    color: $success-color;
    border: 1px solid rgba($success-color, 0.2);
  }

  &.status-busy {
    background: rgba($danger-color, 0.1);
    color: $danger-color;
    border: 1px solid rgba($danger-color, 0.2);
  }

  &.status-closing {
    background: rgba($warning-color, 0.1);
    color: $warning-color;
    border: 1px solid rgba($warning-color, 0.2);
  }

  &.status-success {
    background: rgba($success-color, 0.1);
    color: $success-color;
    border: 1px solid rgba($success-color, 0.2);
  }

  &.status-danger {
    background: rgba($danger-color, 0.1);
    color: $danger-color;
    border: 1px solid rgba($danger-color, 0.2);
  }

  &.status-info {
    background: rgba($primary-color, 0.1);
    color: $primary-color;
    border: 1px solid rgba($primary-color, 0.2);
  }

  &.status-muted {
    background: rgba($secondary-color, 0.1);
    color: $secondary-color;
    border: 1px solid rgba($secondary-color, 0.2);
    font-style: italic;
  }
}

// Empty State
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 48px;
  background: #f5f5f5;
  border-radius: $border-radius;
}

// Confirmation Dialog
.confirmation-message {
  text-align: center;
  margin: 24px;
  color: $text-color;
}

// Responsive Adjustments
@media (max-width: 768px) {
  .mesas-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 16px;

    .search-container {
      margin-left: 0;
      max-width: 100%;
    }
  }

  .qr-container {
    flex-direction: column;
    align-items: center;
    text-align: center;

    .qr-image {
      flex: 0 0 180px;
    }
  }
}
