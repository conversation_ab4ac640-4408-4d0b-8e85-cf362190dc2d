:host {
  display: block;
  padding: 15px;
}

.form-group {
  margin-bottom: 1rem;
}

kendo-listview {
  height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 4px;
}

.cartao-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  cursor: pointer;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  &.selected {
    background-color: rgba(0, 0, 0, 0.08);
  }

  .numero {
    font-weight: 500;
    margin-right: 16px;
    min-width: 100px;
  }

  .nome {
    color: rgba(0, 0, 0, 0.7);
  }
}

.actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  gap: 8px;
}

mat-dialog-actions {
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  
  button {
    margin-left: 0.5rem;
  }
}

.cartao-cliente-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  background: #f8f9fa;

  .search-header {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    .search-box {
      flex: 1;
      position: relative;

      i {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
      }

      input {
        padding-left: 35px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        font-size: 18px;
        letter-spacing: 1px;
        text-align: center;
        width: 100%;
        height: 45px;
        
        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
          border-color: #80bdff;
        }
      }
    }
  }

  .cartoes-container {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
  }

  .cartoes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    padding: 10px;
  }

  .cartao-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    &.selected {
      border: 2px solid #007bff;
      background: #f8f9ff;
    }

    .cartao-codigo {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 500;
      color: #007bff;
      letter-spacing: 1px;

      i {
        font-size: 24px;
        margin-right: 15px;
        color: #007bff;
      }
    }

    .cartao-status {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 12px;
      background: #dc3545;
      color: white;

      &.ativo {
        background: #28a745;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;

    i {
      font-size: 48px;
      margin-bottom: 20px;
      color: #007bff;
    }

    h3 {
      margin-bottom: 10px;
    }

    p {
      margin-bottom: 20px;
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;

    p {
      margin-top: 15px;
    }
  }

  .dialog-footer {
    display: none;
  }
}

:host ::ng-deep {
  .k-dialog-buttongroup {
    // Remove o display: none
  }
} 