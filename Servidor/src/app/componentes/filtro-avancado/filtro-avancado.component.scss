:host {
  position: fixed;
  top: 50px;
  right: 0;
  bottom: 0;
  width: 100%;
  max-width: 700px;
  background: white;
  z-index: 1050;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;

  &.visible {
    transform: translateX(0);
  }
}

.card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  padding-bottom: 80px;
}

.botoes-fixos {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.form-group {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;

  label {
    width: 150px;
    min-width: 150px;
    margin-bottom: 0;
    font-weight: 500;
    color: #666;
  }

  .form-control-wrapper {
    flex: 1;
    display: flex;
    gap: 10px;
    align-items: center;

    .control-item {
      flex: 1;

      &.small {
        flex: 0 0 100px;
      }
    }
  }
}

.checkbox-group {
  display: flex;
  gap: 20px;
  align-items: center;

  .k-checkbox-label {
    margin-bottom: 0;
  }
}

.linha-top {
  border-top: 1px solid #eee;
  padding-top: 20px;
  margin-top: 10px;
}

.page-title {
  padding: 20px 20px 0 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;

  button {
    margin-right: 10px;
  }
}

.atras {
  line-height: 38px;
  color: #666;
  margin-left: 10px;
}

// Ajustes responsivos
@media (max-width: 576px) {
  :host {
    width: 100%;
  }

  .form-group {
    flex-direction: column;
    align-items: flex-start;

    label {
      width: 100%;
      margin-bottom: 0.5rem;
    }

    .form-control-wrapper {
      width: 100%;
    }
  }
}
