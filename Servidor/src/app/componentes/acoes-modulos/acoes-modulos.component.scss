.acoes ::ng-deep .icone  {
  display: inline-block;
  fill: #fff;
  vertical-align: middle;
  svg {
    width: 24px !important;
    height: 24px !important;
  }
}

.botao-fundo:hover {
  cursor: pointer;
  box-shadow: 1px 2px 3px #827b7b;
  filter:brightness(105%);
}


.card.contato {
  >.card-body{
    padding-right: 0;
  }
  .ultima-visita{
    top: 30px;position: relative;
  }
}

.plano-info{
  border-left: 1px solid #e5e5e5;
  margin-top: -25px;
  margin-bottom: -25px;
  padding-left: 0;

  .card-box{
    padding-top: 0;
    padding-bottom: 0;
    box-shadow: none;

    &.plano{
      border-bottom: 1px solid #e5e5e5;
      margin-top: 21px;
      padding-bottom: 21px;
      border-radius: 0;
      padding-left: 20px;
      display: table;
      width: 100%;
    }

    p-dropdown{
      display: inline-block;
      position: relative;
      top: 10px;
      margin-left: 15px;
    }
  }

  .pontos{

    span {
      font-size: 40px;
      font-weight: 500;
      line-height: 0.5em;
      position: relative;
      top: 3px;
    }
  }

  label{
    margin-bottom: 0;
    line-height: 18px;
    font-size: 16px;
  }

  .por-selo{
    .lista-selos{
      position: relative;   top: -10px;     display: block;
    }
    label{
      line-height: 30px;
      width: 56px;
    }
  }
}

.acoes .btn{
  margin-right:15px
}

.fa-whatsapp{
  font-size: 25px;
}

.acoes ::ng-deep .icone.tam1,.icone {
  width: 100% !important;
  height: 25px !important;
  svg {
    width: 25px !important;
    height: 25px !important;
  }
}

.card.contato {
  >.card-body{
    padding: 1rem !important;
  }

  margin-bottom: 0px;
}
.plano-info{
  border: none;
  padding-top:0;
  margin-top: 0;
  margin-bottom:0;

  .card-box{
    &.plano{
      padding-bottom: 30px;
    }

    &.por-selo{
      padding-top: 10px;
    }
    p-dropdown{
      margin-left:0;
    }
  }

}

.acoes .btn{
  padding: 10px;
  float: left;
  width: 100%;
  height: 100%;
  margin-top: 5px;
  margin-right: 0px;
}

.card-box {
  padding: 1rem;
  .lista-selos{
    width: 100%;
    display: table;
    padding-top: 10px;
  }
}

.novo-contato {
  td {
    color: #66a719;
  }
}

.troca {
  td {
    color: #ff3242;
  }
}

td a{
  color: inherit;
}

.tamanho-botao {
  width: 135px;
}

.botao-fundo {
  border: none;
  border-radius: 5px;

  &.verde {
    background: url(/assets/fidelidade/Banner_01.png)
  }
  &.azul {
    background: url(/assets/fidelidade/Banner_02.png)
  }

  &.roxo {
    background: url(/assets/fidelidade/Banner_03.png)
  }

  &.laranja {
    background: url(/assets/fidelidade/Banner_04.png)
  }
}

.quatro_botoes {
  max-width: 664px;
}

.seis_botoes {
  max-width: 996px;
}

.tres_botoes {
  max-width: 498px;
}

.acoes {
  padding-left: 6px;
  padding-right: 6px;
}

.row .col-md {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

@media (max-width: 767px) {
  .padding-left-5 {
    padding-left: 5px;
  }

  .padding-right-5 {
    padding-right: 5px;
  }

}
