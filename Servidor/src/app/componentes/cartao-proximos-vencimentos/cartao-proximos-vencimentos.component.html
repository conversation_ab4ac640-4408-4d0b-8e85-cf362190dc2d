<div class="row">
  <div class="col-2">

  </div>
  <div class="col-10">

    <div class="form-group">
      <label>   Filtrar por período:</label>
      <kendo-dropdownlist  name="periodo" [(ngModel)]="periodo" [data]="periodos" textField="descricao"
                             class="form-control"  (ngModelChange)="busquePontuacaoVencidas()">
      </kendo-dropdownlist>

    </div>
  </div>
</div>



<table class="table table-striped table-sm">
     <thead>
      <tr>
        <th>Data da expiração</th>
        <th  >
          {{ cartao.plano.tipoDeAcumulo }}
        </th>
      </tr>
     </thead>
     <tbody *ngIf="pontuacaoVencer.length==0 && buscou">
        <tr>
          <td colspan="2">
            <p >Não há pontos a expirar para período selecionado</p>
          </td>
        </tr>
     </tbody>
     <tbody>
       <tr *ngFor="let pontuacao of pontuacaoVencer.pontuacoes" class="timeline-sm-item" >
         <td class="horario">
           {{pontuacao.dataVencimento | date: 'dd/MM/yyyy'  }}
         </td>
         <td  >
           <b>{{getPontos(pontuacao.pontos)}}</b>
         </td>
       </tr>
     </tbody>
</table>
