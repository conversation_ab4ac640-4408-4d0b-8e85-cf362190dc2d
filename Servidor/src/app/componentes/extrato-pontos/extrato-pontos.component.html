<div class="list-unstyled timeline-sm">
  <div *ngFor="let acao of acoes" class="timeline-sm-item" >
    <span class="timeline-sm-date horario">{{acao.horarioAbreviado  }}
      <span class="text-muted" *ngIf="acao.ano"><br>{{acao.ano}}</span>
    </span>

    <div class="row"  >
      <div  class="col-1">
        <app-icone-acao [acao]="acao" [exibirDescricao]="false"></app-icone-acao>
      </div>
      <div  class="col-11 text-muted">

        <span>{{acao.descricao}}  </span>
        <div [ngClass]="{'text-success': acao.credito, 'text-danger': acao.debito}">
          <b>
            <span *ngIf="acao.debito">-</span>
            {{acao.acumulado }}</b>
        </div>

      </div>

    </div>

  </div>


</div>

<button class="btn btn-block  btn-rounded  btn-outline-light mt-2" (click)="carregueMais()" *ngIf="!carregouTodos">ver mais</button>
<button class="btn btn-block  btn-rounded  btn-outline-light mt-2"   *ngIf="carregouTodos">isso é tudo</button>


