import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {GridPedidosComponent} from "../../pedidos/grid-pedidos/grid-pedidos.component";
import {EnumFiltroDePedidos} from "../../services/pedidos.service";
import {GrupolojasService} from "../../superadmin/services/grupolojas.service";

declare var moment;


@Component({
  selector: 'app-pedidos-pago-grupo',
  templateUrl: './pedidos-pago-grupo.component.html',
  styleUrls: ['./pedidos-pago-grupo.component.scss']
})
export class PedidosPagoGrupoComponent implements OnInit {
  @ViewChild('gridPedidos', { static: false}) gridPedidos: GridPedidosComponent;
  pedidos: [];
  paginacao = { total: 1000};
  carregando: any = false;
  filtro: any = { inicio: null, fim: null};
  resumoPedidos: any = { qtde: 0, total: 0, totalTaxas: 0 };
  @Input() mesa = false;
  objFiltro: any = {
    q: ''
  };
  usuario = null;
  constructor(private grupolojasService: GrupolojasService) {

  }
  ngOnInit(): void {
    this.filtro.inicio = moment().add(-7, 'd').toDate();
    this.filtro.fim = new Date();

    this.carreguePedidos();
  }
  carreguePedidos(){
    if(this.carregando) return;

    let dtInicio = moment(this.filtro.inicio).format('YYYYMMDD');
    let dtFim = moment(this.filtro.fim).format('YYYYMMDD');

    this.carregando = true;

    let filtro = EnumFiltroDePedidos.pedidos;
    if( this.mesa )
      filtro = EnumFiltroDePedidos.comanda;

    this.grupolojasService.listePedidos(0, this.paginacao.total,
      { apenasEncerrados: true, dtInicio: dtInicio, dtFim: dtFim, q: this.objFiltro.q }, filtro).then( (resposta) => {
      this.setPedidos(resposta.pedidos || []);
      this.gridPedidos.atualizeGridPedidos(this.pedidos,'pagos');
      this.carregando = false;
    }).catch( () => {
      this.carregando = false;
    });
  }

  private setPedidos(resposta: any) {
    this.pedidos = resposta || [];
    this.resumoPedidos.qtde = this.pedidos.length;
    this.resumoPedidos.totalTaxas = this.pedidos.reduce( (valor, pedido: any) => valor + pedido.taxaEntrega, 0);
    this.resumoPedidos.total =  this.pedidos.reduce((valor, pedido: any) => valor + pedido.total, 0);
    this.resumoPedidos.total -=    this.resumoPedidos.totalTaxas;

  }

  onFilter($event: any) {
    this.carreguePedidos();
  }


}
