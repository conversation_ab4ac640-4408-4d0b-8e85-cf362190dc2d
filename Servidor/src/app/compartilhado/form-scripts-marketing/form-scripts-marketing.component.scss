.card-title {
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

textarea {
  min-height: 80px;
}

.script-section {
  border-left: 4px solid #e9ecef;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.script-section:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.facebook-section {
  border-left-color: #1877F2;
}

.facebook-section .card-header {
  background-color: rgba(24, 119, 242, 0.1);
}

.google-section {
  border-left-color: #4285F4;
}

.google-section .card-header {
  background-color: rgba(66, 133, 244, 0.1);
}

.script-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.info-icon {
  cursor: pointer;
  color: #6c757d;
  transition: color 0.2s ease;
}

.info-icon:hover {
  color: #007bff;
}

.example-image {
  max-width: 100%;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.card-description {
  color: #6c757d;
  margin-bottom: 1.5rem;
}

.header-section {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}
