<div class="header-section mb-4">
  <h2 class="font-weight-bold">
    <i class="mdi mdi-tag-multiple mr-2 text-success"></i>
    Configuração de Ferramentas de Marketing Digital
  </h2>
  <p class="text-muted">
    Configure os códigos de rastreamento e pixels para suas campanhas de marketing e análise de dados.
    Estes scripts ajudam a medir o desempenho do seu site e a eficácia das suas campanhas publicitárias.
  </p>
</div>

<form [ngClass]="{'needs-validation': !frmScriptsMarketing.submitted, 'was-validated': frmScriptsMarketing.submitted}"
      novalidate #frmScriptsMarketing="ngForm" (ngSubmit)="onSubmitScriptsMarketing()">

  <!-- Navegação por abas moderna -->
  <ul class="nav nav-tabs nav-tabs-modern nav-fill mb-0" role="tablist">
    <li class="nav-item">
      <a class="nav-link active" data-toggle="tab" href="#tab-facebook" role="tab" aria-controls="tab-facebook" aria-selected="true">
        <i class="mdi mdi-facebook text-facebook mr-2"></i>
        <span>Facebook</span>
      </a>
    </li>
    <li class="nav-item">
      <a class="nav-link" data-toggle="tab" href="#tab-google" role="tab" aria-controls="tab-google" aria-selected="false">
        <i class="mdi mdi-google text-google mr-2"></i>
        <span>Google</span>
      </a>
    </li>
  </ul>

  <!-- Conteúdo das abas com layout moderno -->
  <div class="tab-content tab-content-modern border border-top-0 rounded-bottom p-4">
    <!-- Aba do Facebook -->
    <div class="tab-pane fade show active" id="tab-facebook" role="tabpanel">
      <div class="facebook-section">
        <!-- Pixel do Facebook -->
        <div class="form-group mb-3">
          <div class="mb-3 d-flex align-items-center">
            <div class="icon-circle bg-facebook mr-3">
              <i class="mdi mdi-facebook text-white"></i>
            </div>
            <div>
              <h5 class="mb-0">{{ scriptInfos.pixelFacebook.title }}</h5>
              <small class="text-muted">Configure seu ID de Pixel para rastreamento</small>
            </div>
            <i class="mdi mdi-information-outline ml-2 info-icon"
              (click)="abrirModal('pixelFacebook')"
              title="Clique para mais informações"></i>
          </div>

          <div class="input-group input-group-modern">
            <input type="text" class="form-control" autocomplete="off"
                  name="pixelFacebook" [(ngModel)]="empresa.pixelFacebook"
                  [placeholder]="scriptInfos.pixelFacebook.placeholder" appAutoFocus [autoFocus]="true">
          </div>
          <small class="form-text text-muted mt-1">
            {{ scriptInfos.pixelFacebook.helpText }}
            <a [href]="scriptInfos.pixelFacebook.learnMoreLink" target="_blank" class="text-facebook">Saiba mais</a>
          </small>
        </div>

        <hr class="my-3">

        <!-- Token API Conversões -->
        <div class="form-group mb-3">
          <div class="mb-3 d-flex align-items-center">
            <div class="icon-circle bg-facebook mr-3">
              <i class="mdi mdi-key-variant text-white"></i>
            </div>
            <div>
              <h5 class="mb-0">{{ scriptInfos.accessTokenAPIConversoes.title }}</h5>
              <small class="text-muted">Envie eventos diretamente do servidor para o Facebook</small>
            </div>
            <i class="mdi mdi-information-outline ml-2 info-icon"
              (click)="abrirModal('accessTokenAPIConversoes')"
              title="Clique para mais informações"></i>
          </div>

          <div class="input-group input-group-modern">
            <textarea class="form-control" autocomplete="off"
                      name="accessTokenAPIConversoes" [(ngModel)]="empresa.accessTokenAPIConversoes"
                      [placeholder]="scriptInfos.accessTokenAPIConversoes.placeholder" rows="3"></textarea>
          </div>
          <small class="form-text text-muted mt-1">
            {{ scriptInfos.accessTokenAPIConversoes.helpText }}
            <a [href]="scriptInfos.accessTokenAPIConversoes.learnMoreLink" target="_blank" class="text-facebook">Saiba mais</a>
          </small>

          <div class="mt-3 p-3 bg-facebook-light rounded shadow-sm">
            <div class="d-flex align-items-center mb-3">
              <div class="icon-circle bg-facebook mr-3" style="width: 36px; height: 36px;">
                <i class="mdi mdi-flash text-white"></i>
              </div>
              <h6 class="mb-0 text-facebook font-weight-bold">Testar configuração</h6>
            </div>
            <div class="input-group input-group-modern">
              <input type="text" class="form-control" autocomplete="off"
                    name="codigoTesteAPIConversoes" [(ngModel)]="empresa.codigoTesteAPIConversoes"
                    placeholder="Código de teste API Conversões">
              <div class="input-group-append">
                <button (click)="testarToken()" class="btn btn-facebook" type="button">
                  <i class="mdi mdi-play-circle-outline mr-1"></i> Testar Token
                </button>
              </div>
            </div>
            <small class="form-text text-muted mt-1">
              O código de teste não é salvo e é usado apenas para testar a conversão no Facebook.
            </small>
          </div>

          <p class="alert alert-danger mt-3 animate__animated animate__fadeIn" role="alert" *ngIf="mensagemErro">
            <i class="mdi mdi-alert-circle mr-2"></i>
            <span>{{mensagemErro}}</span>
          </p>
        </div>
      </div>
    </div>

    <!-- Aba do Google -->
    <div class="tab-pane fade" id="tab-google" role="tabpanel">
      <div class="google-section">
        <!-- Google Analytics -->
        <div class="form-group mb-3">
          <div class="mb-3 d-flex align-items-center">
            <div class="icon-circle bg-google mr-3">
              <i class="mdi mdi-chart-line text-white"></i>
            </div>
            <div>
              <h5 class="mb-0">{{ scriptInfos.analytics.title }}</h5>
              <small class="text-muted">Rastreie e analise o comportamento dos usuários</small>
            </div>
            <i class="mdi mdi-information-outline ml-2 info-icon"
              (click)="abrirModal('analytics')"
              title="Clique para mais informações"></i>
          </div>

          <div class="input-group input-group-modern">
            <input type="text" class="form-control" autocomplete="off"
                  name="analytics" [(ngModel)]="empresa.analytics"
                  [placeholder]="scriptInfos.analytics.placeholder">
          </div>
          <small class="form-text text-muted mt-1">
            {{ scriptInfos.analytics.helpText }}
            <a [href]="scriptInfos.analytics.learnMoreLink" target="_blank" class="text-google">Saiba mais</a>
          </small>
        </div>

        <hr class="my-3">

        <!-- Google Tag Manager -->
        <div class="form-group mb-3">
          <div class="mb-3 d-flex align-items-center">
            <div class="icon-circle bg-google mr-3">
              <i class="mdi mdi-tag-multiple text-white"></i>
            </div>
            <div>
              <h5 class="mb-0">{{ scriptInfos.gtm.title }}</h5>
              <small class="text-muted">Gerencie todas as suas tags em um só lugar</small>
            </div>
            <i class="mdi mdi-information-outline ml-2 info-icon"
              (click)="abrirModal('gtm')"
              title="Clique para mais informações"></i>
          </div>

          <div class="input-group input-group-modern">
            <input type="text" class="form-control" autocomplete="off"
                  name="gtm" [(ngModel)]="empresa.gtm"
                  [placeholder]="scriptInfos.gtm.placeholder">
          </div>
          <small class="form-text text-muted mt-1">
            {{ scriptInfos.gtm.helpText }}
            <a [href]="scriptInfos.gtm.learnMoreLink" target="_blank" class="text-google">Saiba mais</a>
          </small>
        </div>

        <hr class="my-3">

        <!-- GTAG -->
        <div class="form-group mb-3">
          <div class="mb-3 d-flex align-items-center">
            <div class="icon-circle bg-google mr-3">
              <i class="mdi mdi-code-tags text-white"></i>
            </div>
            <div>
              <h5 class="mb-0">{{ scriptInfos.gtag.title }}</h5>
              <small class="text-muted">Implementação moderna para produtos Google</small>
            </div>
            <i class="mdi mdi-information-outline ml-2 info-icon"
              (click)="abrirModal('gtag')"
              title="Clique para mais informações"></i>
          </div>

          <div class="input-group input-group-modern">
            <input type="text" class="form-control" autocomplete="off"
                  name="gtag" [(ngModel)]="empresa.gtag"
                  [placeholder]="scriptInfos.gtag.placeholder">
          </div>
          <small class="form-text text-muted mt-1">
            {{ scriptInfos.gtag.helpText }}
            <a [href]="scriptInfos.gtag.learnMoreLink" target="_blank" class="text-google">Saiba mais</a>
          </small>
        </div>
      </div>
    </div>
  </div>

  <!-- Mensagem de sucesso -->
  <div class="alert alert-success alert-dismissible fade show mt-4 animate__animated animate__fadeIn" *ngIf="mensagemSucesso" role="alert">
    <i class="mdi mdi-check-all mr-2"></i> {{mensagemSucesso}}
    <button type="button" class="close" data-dismiss="alert" aria-label="Close" (click)="fecheMensagemSucesso()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <!-- Botão de salvar -->
  <div class="text-center mt-4">
    <button type="submit" class="btn btn-primary btn-lg waves-effect waves-light px-5 shadow-sm" [disabled]="salvando">
      <i class="mdi mdi-content-save mr-1"></i>
      <i class="k-icon k-i-loading mr-1" *ngIf="salvando"></i>
      Salvar Configurações
    </button>
  </div>
</form>

<!-- Modais de informação -->
<div class="modal fade" [class.show]="modalAberto !== null" [style.display]="modalAberto !== null ? 'block' : 'none'" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content shadow-lg" *ngIf="modalAberto">
      <div class="modal-header">
        <h5 class="modal-title d-flex align-items-center">
          <img [src]="scriptInfos[modalAberto].icon" alt="Script Icon" class="script-icon mr-2">
          {{ scriptInfos[modalAberto].title }}
        </h5>
        <button type="button" class="close" (click)="fecharModal()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p class="lead">{{ scriptInfos[modalAberto].description }}</p>

        <div class="card mb-3 shadow-sm" *ngIf="scriptInfos[modalAberto].exampleImage">
          <div class="card-header">
            <h6 class="mb-0 font-weight-bold">Onde encontrar</h6>
          </div>
          <div class="card-body text-center">
            <img [src]="scriptInfos[modalAberto].exampleImage" alt="Example" class="img-fluid example-image rounded">
          </div>
        </div>

        <div class="card mb-3 shadow-sm">
          <div class="card-header">
            <h6 class="mb-0 font-weight-bold">Como funciona</h6>
          </div>
          <div class="card-body">
            <p *ngIf="modalAberto === 'pixelFacebook'">
              O Pixel do Facebook é um trecho de código JavaScript que você adiciona ao seu site. Ele permite que você rastreie conversões, otimize anúncios, crie públicos para remarketing e entenda como os visitantes interagem com seu site após verem seus anúncios no Facebook.
            </p>
            <p *ngIf="modalAberto === 'accessTokenAPIConversoes'">
              A API de Conversões do Facebook permite que você envie eventos diretamente dos seus servidores para o Facebook, sem depender de cookies do navegador. Isso é especialmente útil com as crescentes restrições de privacidade nos navegadores. O token é necessário para autenticar suas solicitações.
            </p>
            <p *ngIf="modalAberto === 'analytics'">
              O Google Analytics coleta dados sobre como os usuários interagem com seu site, incluindo páginas visitadas, tempo gasto, origem do tráfego e muito mais. Esses dados são processados e apresentados em relatórios detalhados que ajudam a entender o comportamento dos usuários.
            </p>
            <p *ngIf="modalAberto === 'gtm'">
              O Google Tag Manager é um sistema de gerenciamento de tags que permite adicionar e atualizar tags de marketing, como Google Analytics, Facebook Pixel e outros, sem precisar modificar o código do site. Você configura as tags no GTM e ele as executa no seu site conforme necessário.
            </p>
            <p *ngIf="modalAberto === 'gtag'">
              O gtag.js (Global Site Tag) é uma biblioteca de marcação unificada do Google que simplifica a implementação de vários produtos Google. Ele substitui as bibliotecas individuais anteriores e fornece uma maneira consistente de enviar dados para diferentes produtos Google.
            </p>
          </div>
        </div>

        <div class="card shadow-sm">
          <div class="card-header">
            <h6 class="mb-0 font-weight-bold">Dicas de implementação</h6>
          </div>
          <div class="card-body">
            <ul class="pl-3">
              <li *ngIf="modalAberto === 'pixelFacebook'" class="mb-2">Certifique-se de que o Pixel está instalado em todas as páginas do seu site.</li>
              <li *ngIf="modalAberto === 'pixelFacebook'" class="mb-2">Configure eventos personalizados para rastrear ações específicas, como compras, cadastros ou visualizações de produtos.</li>
              <li *ngIf="modalAberto === 'accessTokenAPIConversoes'" class="mb-2">Use a API de Conversões em conjunto com o Pixel para maximizar a precisão do rastreamento.</li>
              <li *ngIf="modalAberto === 'accessTokenAPIConversoes'" class="mb-2">Envie eventos importantes do servidor, como compras concluídas, para garantir que sejam registrados mesmo com bloqueadores de cookies.</li>
              <li *ngIf="modalAberto === 'analytics'" class="mb-2">Configure metas para rastrear conversões importantes, como vendas ou cadastros.</li>
              <li *ngIf="modalAberto === 'analytics'" class="mb-2">Use filtros para excluir tráfego interno e obter dados mais precisos.</li>
              <li *ngIf="modalAberto === 'gtm'" class="mb-2">Organize suas tags em pastas e use convenções de nomenclatura claras.</li>
              <li *ngIf="modalAberto === 'gtm'" class="mb-2">Teste suas tags no modo de visualização antes de publicá-las.</li>
              <li *ngIf="modalAberto === 'gtag'" class="mb-2">Certifique-se de que o código gtag.js está no cabeçalho de todas as páginas do seu site.</li>
              <li *ngIf="modalAberto === 'gtag'" class="mb-2">Use o mesmo código gtag.js para diferentes produtos Google para evitar duplicação.</li>
            </ul>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <a [href]="scriptInfos[modalAberto].learnMoreLink" target="_blank" class="btn btn-success rounded-pill">
          <i class="mdi mdi-open-in-new mr-1"></i> Documentação oficial
        </a>
        <button type="button" class="btn btn-secondary rounded-pill" (click)="fecharModal()">Fechar</button>
      </div>
    </div>
  </div>
</div>
<div class="modal-backdrop fade show" *ngIf="modalAberto !== null"></div>

<!-- CSS para o novo layout moderno -->
<style>
  /* Gradiente de marca */
  .bg-gradient-primary {
    background: linear-gradient(135deg, #4e73df 0%, #6f42c1 100%);
  }

  .text-white-75 {
    color: rgba(255, 255, 255, 0.75);
  }

  /* Círculos de ícones */
  .icon-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-circle i {
    font-size: 22px;
  }

  /* Melhorias nas abas */
  .nav-tabs-modern {
    border-bottom: none;
  }

  .nav-tabs-modern .nav-link {
    border: 1px solid #dee2e6;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .nav-tabs-modern .nav-link:not(.active):hover {
    background-color: rgba(78, 115, 223, 0.05);
  }

  .nav-tabs-modern .nav-link.active {
    border-top: 3px solid #4e73df;
    background-color: #fff;
  }

  /* Conteúdo das abas */
  .tab-content-modern {
    background-color: #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 1.25rem !important;
  }

  /* Input groups modernos */
  .input-group-modern .form-control {
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .input-group-modern .form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.15);
  }

  .input-group-modern .input-group-append .btn {
    border-radius: 4px;
    margin-left: 8px;
  }

  /* Botões arredondados e melhorados */
  .btn {
    transition: all 0.2s ease;
  }

  .rounded-pill {
    border-radius: 50rem !important;
  }

  .btn:hover {
    transform: translateY(-1px);
  }

  .btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
  }

  .btn-primary:hover {
    background-color: #3a5fc9;
    border-color: #3a5fc9;
  }

  /* Modal melhorado */
  .modal-content {
    border-radius: 8px;
    overflow: hidden;
  }

  .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .card:hover {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.07) !important;
  }

  .info-icon {
    cursor: pointer;
    color: #4e73df;
    font-size: 18px;
    transition: all 0.2s ease;
  }

  .info-icon:hover {
    transform: scale(1.2);
  }

  .script-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }

  /* Bordas personalizadas */
  .border-facebook {
    border-left: 4px solid #1877F2 !important;
  }

  .border-google {
    border-left: 4px solid #4285F4 !important;
  }

  /* Cores personalizadas para Facebook */
  .bg-facebook {
    background-color: #1877F2 !important;
  }

  .text-facebook {
    color: #1877F2 !important;
  }

  .btn-facebook {
    background-color: #1877F2 !important;
    border-color: #1877F2 !important;
    color: white !important;
  }

  .btn-outline-facebook {
    color: #1877F2 !important;
    border-color: #1877F2 !important;
  }

  /* Cores personalizadas para Google */
  .bg-google {
    background-color: #4285F4 !important;
  }

  .text-google {
    color: #4285F4 !important;
  }

  .btn-google {
    background-color: #4285F4 !important;
    border-color: #4285F4 !important;
    color: white !important;
  }

  .btn-outline-google {
    color: #4285F4 !important;
    border-color: #4285F4 !important;
  }

  /* Animações */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  .animate__animated {
    animation-duration: 0.5s;
  }

  .animate__fadeIn {
    animation-name: fadeIn;
  }

  .bg-facebook-light {
    background-color: rgba(24, 119, 242, 0.05);
  }

  /* Campos mais próximos */
  .form-group small.form-text {
    margin-top: 0.25rem !important;
  }
</style>
