<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <!-- Header -->
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">CRM</a></li>
            <li class="breadcrumb-item active">Assistente WhatsApp</li>
          </ol>
        </div>
        <h4 class="page-title">
          <i class="fe-message-square"></i> Assistente de Vendas - WhatsApp
        </h4>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <!-- Card de Informações do Contato -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fe-user"></i> Informações do Contato
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="d-flex align-items-center">
                <div class="avatar-sm bg-primary rounded-circle me-3">
                  <span class="avatar-title">
                    {{ contato.nome.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <div>
                  <h5 class="mb-1">{{ contato.nome }}</h5>
                  <p class="text-muted mb-0">
                    <i class="fe-phone"></i> {{ contato.telefone }}
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="text-center" *ngIf="contato.empresa">
                <i class="fe-briefcase text-muted"></i>
                <span class="text-muted ms-1">{{ contato.empresa }}</span>
              </div>
            </div>
            <div class="col-md-4">
              <div class="text-end">
                <span class="badge badge-success">
                  <i class="fe-clock"></i> Online agora
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>


      <!-- Card de Contexto da Conversa -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fe-message-circle"></i> Contexto da Conversa
          </h5>
        </div>
        <div class="card-body">
          <div class="bg-light p-3 rounded">
            <h6 class="mb-2">
              <i class="fe-file-text text-primary"></i> Resumo automático:
            </h6>
            <p class="mb-0 text-muted">{{ contextoResumo }}</p>
          </div>

          <!-- Seção de mensagens capturadas para debug -->
          <div class="mt-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h6 class="mb-0">
                <i class="fe-message-square text-info"></i>
                Mensagens da Conversa
                <span *ngIf="mensagensCapturadas.length > 0" class="badge badge-info">
                  {{ mensagensCapturadas.length }}
                </span>
              </h6>
              <div>
                <button class="btn btn-outline-warning btn-sm me-2" (click)="simularMensagens()">
                  <i class="fe-zap"></i> Simular
                </button>
                <button class="btn btn-outline-danger btn-sm" (click)="limparMensagens()">
                  <i class="fe-trash-2"></i> Limpar
                </button>
              </div>
            </div>

            <!-- Estado: Capturando mensagens -->
            <div *ngIf="capturandoMensagens && mensagensCapturadas.length === 0"
                 class="text-center p-4 bg-light rounded">
              <i class="fe-loader rotating text-primary" style="font-size: 2rem;"></i>
              <p class="mt-2 mb-0 text-muted">Aguardando mensagens do WhatsApp...</p>
              <small class="text-muted">Mude de conversa para capturar as mensagens</small>
            </div>

            <!-- Estado: Sem mensagens -->
            <div *ngIf="!capturandoMensagens && mensagensCapturadas.length === 0"
                 class="text-center p-4 bg-light rounded">
              <i class="fe-inbox text-muted" style="font-size: 2rem;"></i>
              <p class="mt-2 mb-0 text-muted">Nenhuma mensagem capturada</p>
              <button class="btn btn-sm btn-primary mt-2" (click)="simularMensagens()">
                Simular Mensagens de Teste
              </button>
            </div>

            <!-- Lista de mensagens -->
            <div *ngIf="mensagensCapturadas.length > 0"
                 class="messages-debug"
                 style="max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px; background: #f8f9fa;">
              <div *ngFor="let msg of mensagensCapturadas; let i = index"
                   class="message-item mb-2"
                   style="padding: 8px; background: white; border-radius: 5px; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                <div class="d-flex justify-content-between align-items-start">
                  <strong [ngClass]="{'text-primary': msg.remetente === 'Eu', 'text-success': msg.remetente !== 'Eu'}">
                    <i [class]="msg.remetente === 'Eu' ? 'fe-user' : 'fe-message-circle'"></i>
                    {{ msg.remetente }}:
                  </strong>
                  <small class="text-muted">{{ msg.horario }}</small>
                </div>
                <div class="mt-1">{{ msg.texto }}</div>
                <div class="d-flex justify-content-between">
                  <small class="text-muted">Tipo: {{ msg.tipo }}</small>
                  <small class="text-muted">#{{ i + 1 }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Card de Assistente de Resposta (Unificado) -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fe-zap"></i> Assistente de Vendas SPIN
          </h5>
        </div>
        <div class="card-body">
          <!-- Seção 1: Configuração da Sugestão -->
          <div class="config-section mb-4 p-3 bg-light rounded">
            <!-- Toggle Modo Rapport -->
            <div class="d-flex justify-content-between align-items-center mb-3">
              <div>
                <h6 class="mb-0">
                  <i class="fe-target text-primary"></i> Modo de Geração
                </h6>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input"
                       type="checkbox"
                       id="modoRapportSwitch"
                       [(ngModel)]="modoRapport"
                       (change)="toggleModoRapport()">
                <label class="form-check-label" for="modoRapportSwitch">
                  <strong [class.text-primary]="modoRapport">
                    <i class="fe-heart"></i> Modo Rapport/Atratividade
                  </strong>
                </label>
              </div>
            </div>

            <!-- Configurações Modo Normal (SPIN) -->
            <div *ngIf="!modoRapport" class="row align-items-center">
              <!-- Fase SPIN -->
              <div class="col-md-8">
                <label class="form-label fw-bold mb-2">
                  <i class="fe-target text-primary"></i> Fase da Venda (SPIN Selling):
                </label>
                <div class="btn-group d-flex flex-wrap" role="group">
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="getFaseBadgeClass('situacao')"
                          (click)="mudarFaseSpin('situacao')"
                          data-bs-toggle="tooltip"
                          title="Entender o contexto atual do cliente">
                    <i class="fe-help-circle"></i> Situação
                  </button>
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="getFaseBadgeClass('problema')"
                          (click)="mudarFaseSpin('problema')"
                          data-bs-toggle="tooltip"
                          title="Identificar dores e dificuldades">
                    <i class="fe-alert-triangle"></i> Problema
                  </button>
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="getFaseBadgeClass('implicacao')"
                          (click)="mudarFaseSpin('implicacao')"
                          data-bs-toggle="tooltip"
                          title="Explorar impactos dos problemas">
                    <i class="fe-trending-down"></i> Implicação
                  </button>
                  <button type="button"
                          class="btn flex-fill mb-1"
                          [ngClass]="getFaseBadgeClass('necessidade')"
                          (click)="mudarFaseSpin('necessidade')"
                          data-bs-toggle="tooltip"
                          title="Demonstrar valor da solução">
                    <i class="fe-check-circle"></i> Necessidade
                  </button>
                </div>
                <small class="text-muted d-block mt-1">
                  <i class="fe-info"></i> Selecione a fase atual da conversa para gerar sugestões mais precisas
                </small>
              </div>

              <!-- Tom da Conversa -->
              <div class="col-md-4">
                <label class="form-label fw-bold mb-2">
                  <i class="fe-mic text-primary"></i> Tom:
                </label>
                <select class="form-select" [(ngModel)]="tipoTom">
                  <option value="formal">Formal</option>
                  <option value="informal">Informal</option>
                  <option value="tecnico">Técnico</option>
                </select>
              </div>
            </div>

            <!-- Configurações Modo Rapport -->
            <div *ngIf="modoRapport" class="row align-items-center">
              <div class="col-12">
                <label class="form-label fw-bold mb-2">
                  <i class="fe-message-circle text-primary"></i> Tipo de Abordagem Outbound:
                </label>
                <div class="btn-group d-flex flex-wrap" role="group">
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="tipoAbordagem === 'direta' ? 'btn btn-primary' : 'btn btn-outline-primary'"
                          (click)="mudarTipoAbordagem('direta')"
                          data-bs-toggle="tooltip"
                          title="Abordagem direta e objetiva, focada em resultados">
                    <i class="fe-arrow-right"></i> Direta
                  </button>
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="tipoAbordagem === 'indireta' ? 'btn btn-primary' : 'btn btn-outline-primary'"
                          (click)="mudarTipoAbordagem('indireta')"
                          data-bs-toggle="tooltip"
                          title="Abordagem mais suave, criando conexão primeiro">
                    <i class="fe-smile"></i> Indireta
                  </button>
                  <button type="button"
                          class="btn flex-fill mb-1"
                          [ngClass]="tipoAbordagem === 'consultiva' ? 'btn btn-primary' : 'btn btn-outline-primary'"
                          (click)="mudarTipoAbordagem('consultiva')"
                          data-bs-toggle="tooltip"
                          title="Abordagem consultiva, focada em entender necessidades">
                    <i class="fe-users"></i> Consultiva
                  </button>
                </div>
                <small class="text-muted d-block mt-1">
                  <i class="fe-info"></i> Escolha o estilo de abordagem para gerar mensagens que despertem interesse e engajamento
                </small>
              </div>
            </div>
          </div>

          <!-- Seção 2: Botão de Gerar -->
          <div class="text-center mb-4">
            <button class="btn btn-primary btn-lg shadow"
                    [disabled]="carregando || (!modoRapport && mensagensCapturadas.length === 0)"
                    (click)="gerarSugestao()">
              <span *ngIf="!carregando && !modoRapport">
                <i class="fe-cpu"></i> Gerar Sugestão de Resposta
              </span>
              <span *ngIf="!carregando && modoRapport">
                <i class="fe-heart"></i> Gerar Mensagem de Rapport
              </span>
              <span *ngIf="carregando && !modoRapport">
                <i class="fe-loader rotating"></i> Analisando conversa...
              </span>
              <span *ngIf="carregando && modoRapport">
                <i class="fe-loader rotating"></i> Criando mensagem atrativa...
              </span>
            </button>
            <div *ngIf="!modoRapport && mensagensCapturadas.length === 0" class="mt-2">
              <small class="text-warning">
                <i class="fe-alert-circle"></i> Capture mensagens primeiro ou use o botão simular
              </small>
            </div>
            <div *ngIf="modoRapport" class="mt-2">
              <small class="text-info">
                <i class="fe-info"></i> Mensagens otimizadas para trabalho outbound e engajamento inicial
              </small>
            </div>
          </div>

          <!-- Erro -->
          <div *ngIf="erro" class="alert alert-danger" role="alert">
            <i class="fe-alert-triangle"></i>
            {{ erro }}
          </div>

          <!-- Sugestão gerada -->
          <div *ngIf="sugestao && !carregando" class="sugestao-container">
            <label class="form-label">
              <i class="fe-edit-3"></i> Sugestão gerada:
              <span class="badge badge-info ms-2">
                Confiança: {{ (sugestao.confianca * 100) | number:'1.0-0' }}%
              </span>
            </label>

            <textarea class="form-control mb-3"
                      rows="4"
                      [(ngModel)]="sugestao.texto"
                      placeholder="Sugestão aparecerá aqui..."></textarea>

            <!-- Ações -->
            <div class="d-flex justify-content-center gap-2">
              <button class="btn btn-success"
                      (click)="usarSugestao()">
                <i class="fe-copy"></i> Copiar
              </button>
              <button class="btn btn-outline-primary"
                      (click)="editarSugestao()">
                <i class="fe-edit"></i> Editar
              </button>
              <button class="btn btn-outline-secondary"
                      (click)="regenerarSugestao()"
                      [disabled]="carregando">
                <i class="fe-refresh-cw"></i> Regenerar
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Card de Ações Rápidas -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fe-zap"></i> Ações Rápidas
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6 mb-2">
              <button class="btn btn-outline-primary btn-sm w-100">
                <i class="fe-calendar"></i> Agendar Follow-up
              </button>
            </div>
            <div class="col-md-6 mb-2">
              <button class="btn btn-outline-primary btn-sm w-100">
                <i class="fe-file-text"></i> Ver Proposta
              </button>
            </div>
            <div class="col-md-6 mb-2">
              <button class="btn btn-outline-info btn-sm w-100">
                <i class="fe-help-circle"></i> Dicas SPIN
              </button>
            </div>
            <div class="col-md-6 mb-2">
              <button class="btn btn-outline-secondary btn-sm w-100">
                <i class="fe-bar-chart-2"></i> Histórico do Lead
              </button>
            </div>
          </div>

          <!-- Notas privadas -->
          <div class="mt-3">
            <label class="form-label">
              <i class="fe-edit"></i> Notas privadas:
            </label>
            <textarea class="form-control"
                      rows="3"
                      placeholder="Digite aqui observações importantes sobre o cliente..."></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
