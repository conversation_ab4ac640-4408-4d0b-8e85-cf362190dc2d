import { Component, OnInit, HostListener } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LeadService } from '../services/lead.service';
import { SugestoesService } from '../services/sugestoes.service';

@Component({
  selector: 'app-crm-whatsapp-assistant',
  templateUrl: './crm-whatsapp-assistant.component.html',
  styleUrls: ['./crm-whatsapp-assistant.component.scss']
})
export class CrmWhatsappAssistantComponent implements OnInit {
  // Dados do contato
  telefone: string = '';
  contato: any = {
    nome: '',
    telefone: '',
    empresa: ''
  };

  // Estado da conversa
  faseSpin: 'situacao' | 'problema' | 'implicacao' | 'necessidade' = 'situacao';
  contextoResumo: string = '';

  // Mensagens capturadas do WhatsApp
  mensagensCapturadas: any[] = [];
  capturandoMensagens: boolean = false;

  // Sugestão de resposta
  sugestao: any = null;
  carregando: boolean = false;
  erro: string | null = null;

  // Configurações
  tipoTom: 'formal' | 'informal' | 'tecnico' = 'formal';
  produto: string = 'Sistema de Gestão PromoKit';

  // Modo rapport/atratividade
  modoRapport: boolean = false;
  tipoAbordagem: 'direta' | 'indireta' | 'consultiva' = 'consultiva';

  constructor(
    private route: ActivatedRoute,
    private leadService: LeadService,
    private sugestoesService: SugestoesService
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.telefone = params['telefone'];
      this.carregarDadosContato();
      this.carregarContextoConversa();
    });

    // Indicar que está pronto para receber mensagens
    this.capturandoMensagens = true;
  }

  carregarDadosContato(): void {
    // Simular carregamento de dados do contato
    // TODO: Integrar com API real
    this.contato = {
      nome: 'João Silva',
      telefone: this.telefone,
      empresa: 'Empresa ABC Ltda'
    };
  }

  carregarContextoConversa(): void {
    // Simular contexto da conversa
    // TODO: Capturar do WhatsApp Web via content script
    this.contextoResumo = 'Cliente interessado em sistema de gestão. Mencionou dificuldades com controle de estoque e vendas. Tem 3 lojas físicas.';
  }

  async gerarSugestao(): Promise<void> {
    this.carregando = true;
    this.erro = null;

    try {
      // Se está em modo rapport, gerar mensagem de rapport
      if (this.modoRapport) {
        await this.gerarMensagemRapport();
        return;
      }

      // Verificar se temos mensagens capturadas
      if (this.mensagensCapturadas.length === 0) {
        console.log('[WhatsApp Assistant] Nenhuma mensagem capturada, usando mensagens de exemplo');
        // Se não há mensagens, usar algumas de exemplo para teste
        this.mensagensCapturadas = [
          {
            texto: 'Olá, gostaria de saber mais sobre o sistema',
            remetente: 'Cliente',
            horario: new Date().toLocaleTimeString('pt-BR'),
            tipo: 'text'
          }
        ];
      }

      // Preparar contexto para gerar sugestão
      const contexto = {
        telefone: this.telefone,
        mensagens: this.mensagensCapturadas,
        etapaFunil: this.mapearFaseSpinParaEtapa(this.faseSpin),
        tomConversa: this.tipoTom === 'formal' ? 'Consultivo' :
                     this.tipoTom === 'informal' ? 'Empático' : 'Técnico',
        produto: this.produto
      };

      // Chamar serviço de sugestões com IA
      console.log('[WhatsApp Assistant] Gerando sugestão com contexto:', contexto);
      const inicio = Date.now();
      this.sugestoesService.gerarSugestoesComIA(contexto).subscribe(
        (sugestoes) => {
          const tempoResposta = Date.now() - inicio;
          console.log('[WhatsApp Assistant] Resposta recebida:', sugestoes);

          if (sugestoes && sugestoes.length > 0) {
            this.sugestao = {
              texto: sugestoes[0].texto,
              confianca: (sugestoes[0] as any).confianca || 0.85,
              faseSpin: this.faseSpin,
              timestamp: new Date()
            };

            // Salvar analytics do uso
            this.salvarUsoSugestao(tempoResposta, false);
          } else {
            // Fallback para sugestão mockada
            this.sugestao = {
              texto: this.gerarSugestaoMockada(),
              confianca: 0.6,
              faseSpin: this.faseSpin,
              timestamp: new Date()
            };
          }
          this.carregando = false;
        },
        (erro) => {
          console.error('Erro ao gerar sugestão:', erro);
          // Em caso de erro, usar sugestão mockada
          this.sugestao = {
            texto: this.gerarSugestaoMockada(),
            confianca: 0.5,
            faseSpin: this.faseSpin,
            timestamp: new Date()
          };
          this.carregando = false;
        }
      );
    } catch (error) {
      this.erro = 'Erro ao gerar sugestão. Tente novamente.';
      console.error('Erro ao gerar sugestão:', error);
      this.carregando = false;
    }
  }

  private async simularChamadaIA(): Promise<void> {
    // Simula delay da API
    return new Promise(resolve => setTimeout(resolve, 2000));
  }

  private gerarSugestaoMockada(): string {
    const sugestoesPorFase = {
      situacao: `Oi ${this.contato.nome}! Obrigado por entrar em contato. Para entender melhor como posso ajudar sua empresa, você poderia me contar como vocês fazem o controle de estoque atualmente nas 3 lojas?`,
      problema: `Entendo sua preocupação, ${this.contato.nome}. Essas dificuldades com estoque são bem comuns. Vocês já enfrentaram situações de ruptura de produtos ou excesso de estoque parado?`,
      implicacao: `Imagino que isso deve impactar bastante o resultado das lojas, ${this.contato.nome}. Quanto tempo a equipe perde por dia fazendo esse controle manual? E como isso afeta as vendas quando um produto está em falta?`,
      necessidade: `Perfeito, ${this.contato.nome}! Se conseguíssemos automatizar esse processo e dar visibilidade em tempo real do estoque das 3 lojas, qual seria o impacto para vocês? Gostaria de ver como o PromoKit pode resolver exatamente essas questões?`
    };

    return sugestoesPorFase[this.faseSpin];
  }

  mudarFaseSpin(novaFase: 'situacao' | 'problema' | 'implicacao' | 'necessidade'): void {
    this.faseSpin = novaFase;
    this.sugestao = null; // Limpa sugestão anterior
  }

  usarSugestao(): void {
    if (this.sugestao?.texto) {
      // Tentar copiar usando a API moderna de clipboard
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(this.sugestao.texto)
          .then(() => {
            console.log('Sugestão copiada com sucesso!');
            this.mostrarFeedback('Sugestão copiada para área de transferência!', 'success');
            // Salvar que a sugestão foi usada
            this.salvarUsoSugestao(0, true);
          })
          .catch((err) => {
            console.error('Erro ao copiar com clipboard API:', err);
            // Tentar método alternativo
            this.copiarTextoFallback(this.sugestao.texto);
          });
      } else {
        // Usar método alternativo se clipboard API não estiver disponível
        this.copiarTextoFallback(this.sugestao.texto);
      }
    }
  }

  // Método alternativo para copiar texto
  private copiarTextoFallback(texto: string): void {
    try {
      // Criar elemento textarea temporário
      const textarea = document.createElement('textarea');
      textarea.value = texto;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      textarea.style.left = '-9999px';

      document.body.appendChild(textarea);
      textarea.select();
      textarea.setSelectionRange(0, 99999); // Para mobile

      const sucesso = document.execCommand('copy');
      document.body.removeChild(textarea);

      if (sucesso) {
        console.log('Sugestão copiada com método fallback!');
        this.mostrarFeedback('Sugestão copiada para área de transferência!', 'success');
        this.salvarUsoSugestao(0, true);
      } else {
        console.error('Falha ao copiar texto');
        this.mostrarFeedback('Erro ao copiar. Selecione e copie manualmente.', 'error');
      }
    } catch (err) {
      console.error('Erro no método fallback:', err);
      this.mostrarFeedback('Erro ao copiar. Selecione e copie manualmente.', 'error');
    }
  }

  // Mostrar feedback temporário
  private mostrarFeedback(mensagem: string, tipo: 'success' | 'error'): void {
    // Por enquanto, apenas log. Pode ser substituído por toast/snackbar
    console.log(`[${tipo}] ${mensagem}`);

    // Feedback visual temporário
    const feedbackEl = document.createElement('div');
    feedbackEl.textContent = mensagem;
    feedbackEl.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      padding: 12px 24px;
      background: ${tipo === 'success' ? '#28a745' : '#dc3545'};
      color: white;
      border-radius: 4px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
      z-index: 9999;
      animation: fadeIn 0.3s ease-in;
    `;

    document.body.appendChild(feedbackEl);

    // Remover após 3 segundos
    setTimeout(() => {
      feedbackEl.style.opacity = '0';
      feedbackEl.style.transition = 'opacity 0.3s ease-out';
      setTimeout(() => document.body.removeChild(feedbackEl), 300);
    }, 3000);
  }

  editarSugestao(): void {
    // TODO: Implementar modal de edição ou tornar textarea editável
    console.log('Modo edição ativado');
  }

  async regenerarSugestao(): Promise<void> {
    await this.gerarSugestao();
  }

  getFaseBadgeClass(fase: string): string {
    return this.faseSpin === fase ? 'badge badge-primary' : 'badge badge-light';
  }

  // Listener para receber mensagens do content script
  @HostListener('window:message', ['$event'])
  handleMessage(event: MessageEvent): void {
    if (event.data) {
      // Receber informações do contato selecionado
      if (event.data.tipo === 'SELECIONOU_CONTATO' && event.data.payload) {
        const payload = event.data.payload;
        this.contato.nome = payload.nome || 'Sem nome';
        this.contato.telefone = payload.telefone || this.telefone;
      }

      // Receber contexto da conversa com mensagens
      if (event.data.tipo === 'CONTEXTO_CONVERSA') {
        this.capturandoMensagens = false;

        if (event.data.nome) {
          this.contato.nome = event.data.nome;
        }

        if (event.data.mensagens && Array.isArray(event.data.mensagens)) {
          this.mensagensCapturadas = event.data.mensagens;
          // Criar resumo automático das mensagens
          this.criarResumoConversa();
        }
      }
    }
  }

  // Criar resumo da conversa baseado nas mensagens
  private criarResumoConversa(): void {
    if (this.mensagensCapturadas.length > 0) {
      const ultimasMensagens = this.mensagensCapturadas
        .slice(-3)
        .map(m => `${m.remetente}: ${m.texto}`)
        .join(' | ');

      this.contextoResumo = `Últimas mensagens: ${ultimasMensagens}`;
    }
  }

  // Método para simular recebimento de mensagens (debug)
  simularMensagens(): void {
    const mensagensSimuladas = [
      {
        texto: 'Olá, gostaria de saber mais sobre o sistema',
        remetente: 'Cliente',
        horario: '10:30:15',
        tipo: 'text'
      },
      {
        texto: 'Oi! Claro, posso te ajudar. O que você gostaria de saber?',
        remetente: 'Eu',
        horario: '10:31:20',
        tipo: 'text'
      },
      {
        texto: 'Estamos tendo problemas com controle de estoque',
        remetente: 'Cliente',
        horario: '10:32:45',
        tipo: 'text'
      },
      {
        texto: 'Quantas lojas vocês possuem?',
        remetente: 'Eu',
        horario: '10:33:10',
        tipo: 'text'
      },
      {
        texto: 'Temos 3 lojas físicas',
        remetente: 'Cliente',
        horario: '10:33:55',
        tipo: 'text'
      }
    ];

    this.mensagensCapturadas = mensagensSimuladas;
    this.capturandoMensagens = false;
    this.criarResumoConversa();
  }

  // Método para limpar todas as mensagens
  limparMensagens(): void {
    this.mensagensCapturadas = [];
    this.contextoResumo = '';
    this.capturandoMensagens = true;
  }

  // Mapear fase SPIN para etapa do funil
  private mapearFaseSpinParaEtapa(fase: string): string {
    const mapeamento: Record<string, string> = {
      'situacao': 'Prospecção',
      'problema': 'Qualificação',
      'implicacao': 'Objeção',
      'necessidade': 'Fechamento'
    };
    return mapeamento[fase] || 'Prospecção';
  }

  // Salvar uso da sugestão para analytics
  private salvarUsoSugestao(tempoResposta: number, usada: boolean): void {
    if (!this.sugestao) return;

    this.sugestoesService.salvarUsoSugestao({
      telefone: this.telefone,
      faseSpin: this.faseSpin,
      sugestaoGerada: this.sugestao.texto,
      sugestaoUsada: usada,
      tempoResposta: tempoResposta
    }).subscribe(
      () => console.log('Uso de sugestão salvo'),
      (erro) => console.error('Erro ao salvar uso de sugestão:', erro)
    );
  }

  // Gerar mensagem de rapport/atratividade
  async gerarMensagemRapport(): Promise<void> {
    try {
      // Contexto específico para rapport
      const contextoRapport = {
        telefone: this.telefone,
        nomeContato: this.contato.nome,
        empresa: this.contato.empresa,
        tipoAbordagem: this.tipoAbordagem,
        produto: this.produto,
        modoRapport: true,
        ultimaMensagem: this.mensagensCapturadas.length > 0
          ? this.mensagensCapturadas[this.mensagensCapturadas.length - 1].texto
          : null
      };

      console.log('[WhatsApp Assistant] Gerando mensagem de rapport:', contextoRapport);

      // Chamar serviço para gerar mensagem de rapport
      this.sugestoesService.gerarMensagemRapport(contextoRapport).subscribe(
        (resposta) => {
          if (resposta && resposta.texto) {
            this.sugestao = {
              texto: resposta.texto,
              confianca: resposta.confianca || 0.9,
              faseSpin: 'rapport',
              tipoAbordagem: this.tipoAbordagem,
              timestamp: new Date()
            };
          } else {
            // Fallback para mensagem de rapport mockada
            this.sugestao = {
              texto: this.gerarRapportMockado(),
              confianca: 0.7,
              faseSpin: 'rapport',
              tipoAbordagem: this.tipoAbordagem,
              timestamp: new Date()
            };
          }
          this.carregando = false;
        },
        (erro) => {
          console.error('Erro ao gerar mensagem de rapport:', erro);
          // Usar fallback
          this.sugestao = {
            texto: this.gerarRapportMockado(),
            confianca: 0.6,
            faseSpin: 'rapport',
            tipoAbordagem: this.tipoAbordagem,
            timestamp: new Date()
          };
          this.carregando = false;
        }
      );
    } catch (error) {
      console.error('Erro ao gerar mensagem de rapport:', error);
      this.erro = 'Erro ao gerar mensagem. Tente novamente.';
      this.carregando = false;
    }
  }

  // Gerar mensagem de rapport mockada
  private gerarRapportMockado(): string {
    const mensagensRapport = {
      direta: [
        `Oi ${this.contato.nome || 'você'}! 👋 Vi que sua empresa está crescendo - isso é ótimo! Tenho uma solução que pode ajudar a escalar ainda mais rápido. Posso te mostrar em apenas 5 minutos?`,
        `${this.contato.nome || 'Olá'}, notei que empresas como a sua estão economizando até 3h por dia com nossa solução. Que tal descobrir como fazer o mesmo? 🚀`,
        `Oi ${this.contato.nome || 'você'}! Separei 3 cases de sucesso de empresas similares à sua. Vale a pena dar uma olhada - pode ser um divisor de águas! Posso enviar?`
      ],
      indireta: [
        `Oi ${this.contato.nome || 'você'}! Como estão as coisas por aí? 😊 Vi algumas novidades interessantes no mercado de ${this.contato.empresa ? 'seu segmento' : 'gestão'} que podem te interessar...`,
        `${this.contato.nome || 'Olá'}, espero que esteja tendo um ótimo dia! 🌟 Estava pensando em alguns desafios comuns que empresas enfrentam nessa época do ano. Como vocês estão lidando com isso?`,
        `Oi! Acabei de ler um artigo sobre tendências em ${this.contato.empresa ? 'seu mercado' : 'gestão empresarial'} e lembrei de você. Algumas insights bem interessantes! Gostaria de compartilhar?`
      ],
      consultiva: [
        `${this.contato.nome || 'Olá'}, percebi que muitas empresas do seu porte estão buscando formas de otimizar processos. Qual tem sido seu maior desafio operacional atualmente?`,
        `Oi ${this.contato.nome || 'você'}! Tenho ajudado empresas a resolver questões específicas de gestão. Curioso(a) para saber: qual área da sua operação consome mais tempo hoje?`,
        `${this.contato.nome || 'Olá'}, uma pergunta rápida: se pudesse resolver apenas UM problema na sua empresa hoje, qual seria? Talvez eu possa ajudar com algumas ideias...`
      ]
    };

    const mensagens = mensagensRapport[this.tipoAbordagem];
    return mensagens[Math.floor(Math.random() * mensagens.length)];
  }

  // Toggle modo rapport
  toggleModoRapport(): void {
    this.modoRapport = !this.modoRapport;
    this.sugestao = null; // Limpa sugestão anterior
  }

  // Mudar tipo de abordagem
  mudarTipoAbordagem(tipo: 'direta' | 'indireta' | 'consultiva'): void {
    this.tipoAbordagem = tipo;
    if (this.modoRapport && this.sugestao) {
      // Regenerar se já tem uma sugestão de rapport
      this.gerarMensagemRapport();
    }
  }
}
