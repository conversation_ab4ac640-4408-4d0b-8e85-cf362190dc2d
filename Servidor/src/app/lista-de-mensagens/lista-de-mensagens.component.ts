import {AfterViewInit, Component, OnD<PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {MensagemService} from "../services/mensagem.service";
import {GridComponent, GridDataResult, PageChangeEvent} from "@progress/kendo-angular-grid";
import {debounceTime, distinctUntilChanged, map, switchMap} from "rxjs/operators";
import {from, Subject, Subscription} from "rxjs";

declare var moment;

@Component({
  selector: 'app-lista-de-mensagens',
  templateUrl: './lista-de-mensagens.component.html',
  styleUrls: ['./lista-de-mensagens.component.scss']
})
export class ListaDeMensagensComponent implements OnInit, AfterViewInit, OnDestroy {
  public buttonCount = 5;
  public info = true;
  public type: 'numeric' | 'input' = 'numeric';
  public pageSizes = false;
  public previousNext = true;

  mensagens: any;
  total: any;
  loading = false;
  public gridDataResult: GridDataResult;

  page = 0;
  tamanhoPagina = 10;
  @ViewChild('grid', {static: true})
  public grid: GridComponent;
  checked: any;

  opcoesPeriodo = [
    {id: 1, nome: 'Último Dia', dias: 1},
    {id: 7, nome: 'Última Semana', dias: 7},
    {id: 30, nome: 'Último Mês', dias: 30},
    {id: 90, nome: 'Último Trimestre', dias: 90},
    {id: 180, nome: 'Último Semestre', dias: 180},
    {id: 365, nome: 'Último Ano', dias: 365}
  ];

  periodoFiltro = this.opcoesPeriodo.find(p => p.id === 30); // Último Mês como padrão

  statusPossiveisMensagens = [
    {id: 'ENVIADA', nome: 'Enviada'},
    {id: 'ENTREGUE', nome: 'Entregue'},
    {id: 'LIDA', nome: 'Lida'},
    {id: 'NOVA', nome: 'Pendentes'},
    {id: 'NAO_ENVIADA', nome: 'Não Enviada'},
    {id: 'NAO_TEM_WHATSAPP', nome: 'Não tem Whatsapp'},
    {id: 'ENVIANDO', nome: 'Enviando'}
  ];
  statusNaoSelecionado: any = {
    id: null, nome: 'Todos'
  };
  statusFiltro: any;
  filtroNome: any;

  public keyUp = new Subject<KeyboardEvent>();
  subscription: Subscription;
  resumo: any = [];

  constructor(private mensagemService: MensagemService) {
  }

  ngOnInit() {
    // Configurar o Moment.js para português
    moment.locale('pt-br');
  }

  carregue($event: PageChangeEvent) {
    this.page = $event.skip;
    this.loading = true;

    this.monteFiltro();
  }

  envieNovamente(mensagem: any) {
    mensagem.enviando = true;

    this.mensagemService.envieNovamente(mensagem).then((resposta) => {
      mensagem.enviando = false;
      mensagem.status = 'NOVA';
    }).catch(erro => {
      mensagem.enviando = false;
    });
  }

  ngAfterViewInit(): void {
    this.subscription = this.keyUp.pipe(
      debounceTime(700),
      map((event: any) => event.target.value),
      distinctUntilChanged()).subscribe((dados) => {
        this.monteFiltro();
    });

    this.loading = true;
    this.monteFiltro();
  }

  monteFiltro() {
    const root = { logic: 'and', filters: [], ...this.grid.filter };

    if (this.statusFiltro && this.statusFiltro.id) {
      root.filters.push({
        field: 'status',
        operator: 'eq',
        value: this.statusFiltro.id
      });
    }

    if (this.filtroNome) {
      root.filters.push({
        field: 'nome',
        operator: 'eq',
        value: this.filtroNome
      });
    }

    if (this.periodoFiltro) {
      const dataInicio = moment()
        .subtract(this.periodoFiltro.dias, 'days')
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss');

      console.log('Filtro de data:', {
        dias: this.periodoFiltro.dias,
        dataInicio: dataInicio
      });

      root.filters.push({
        field: 'dataInicio',
        operator: 'gte',
        value: dataInicio
      });
    }

    this.loading = true;
    this.mensagemService.obtenhaMensagensObservable(this.page, root).subscribe((respMensagens) => {
      this.loading = false;
      if (!respMensagens || !respMensagens.sucesso) {
        return
      }

      const objMensagens = respMensagens.data;
      this.loading = false;
      this.mensagens = objMensagens.mensagens;

      // Processar as mensagens para adicionar a diferença de tempo
      this.mensagens.forEach(mensagem => {
        if (mensagem.horario && mensagem.horarioModificacao) {
          mensagem.diferencaTempo = this.calcularDiferencaTempo(mensagem.horario, mensagem.horarioModificacao);
        } else {
          mensagem.diferencaTempo = 'N/A';
        }
      });

      this.total = objMensagens.total;
      this.gridDataResult = {
        data: this.mensagens,
        total: this.total
      };

      this.mensagemService.obtenhaResumo(root).then((respResumo) => {
        this.resumo = respResumo;
      });
    });
  }

  calcularDiferencaTempo(horarioInicial: string, horarioFinal: string): string {
    if (!horarioInicial || !horarioFinal) {
      return 'N/A';
    }
    
    const inicio = moment(horarioInicial);
    const fim = moment(horarioFinal);
    
    // Usando recursos do Moment.js para formatação de duração
    return moment.duration(fim.diff(inicio)).humanize();
  }

  obtenhaClasseTempo(diferencaTempo: string): string {
    if (!diferencaTempo || diferencaTempo === 'N/A') {
      return 'tempo-normal';
    }

    // Extrair números do texto retornado pelo moment.js
    const texto = diferencaTempo.toLowerCase();
    
    // Verificar se contém "minuto" ou "minutos"
    if (texto.includes('minuto')) {
      const minutos = parseInt(texto.match(/\d+/)?.[0] || '0');
      
      if (minutos >= 3) {
        return 'tempo-critico'; // vermelho
      } else if (minutos >= 1) {
        return 'tempo-alerta'; // amarelo
      }
    }
    
    // Verificar se contém "hora" ou "horas" - sempre vermelho
    if (texto.includes('hora')) {
      return 'tempo-critico';
    }
    
    // Verificar se contém "dia" ou "dias" - sempre vermelho
    if (texto.includes('dia')) {
      return 'tempo-critico';
    }
    
    // Segundos ou tempos muito pequenos - verde (rápido)
    return 'tempo-rapido';
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
