import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ConversasChatbotComponent } from './conversas-chatbot.component';

describe('ConversasChatbotComponent', () => {
  let component: ConversasChatbotComponent;
  let fixture: ComponentFixture<ConversasChatbotComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ConversasChatbotComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ConversasChatbotComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
