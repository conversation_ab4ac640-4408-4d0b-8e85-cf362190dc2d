import {Component, OnInit, ViewChild} from '@angular/core';
import {ClienteService} from "../services/cliente.service";
import {DomSanitizer, SafeResourceUrl} from "@angular/platform-browser";
import {WindowScrollingService} from "../window-scrolling.service";
import {DeviceDetectorService} from "ngx-device-detector";

@Component({
  selector: 'app-site-empresa',
  templateUrl: './site-empresa.component.html',
  styleUrls: ['./site-empresa.component.scss']
})
export class SiteEmpresaComponent implements OnInit {
  empresaLogada = "fibo";
  empresa: any = {};
  carregou = false;
  estaAberto: boolean;
  descricaoHorario: string;
  urlMaps: SafeResourceUrl;
  exibirSlideShow = false;
  fotosDaEmpresa: string[];
  objetosFoto: any[];
  exibirSlidesItens = false;
  produtoSelecionado: any;

  diasDaSemana = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
  mensagemErro: string;
  possuiInstagram: boolean;
  postsInstagram: any[];
  alturaSlide = '400px';

  constructor(private clienteService: ClienteService, private sanitizer: DomSanitizer,
              private windowScrolling: WindowScrollingService, private dectorDevice: DeviceDetectorService) {
    this.alturaSlide = this.dectorDevice.isMobile() ? '300px' : '500px';
  }

  ngOnInit() {
    this.clienteService.obtenhaEmpresa().then((resposta) => {
      this.empresa = resposta.empresa;

      this.carregueInformacoesDeHorarioDeFuncionamento();

      this.urlMaps = this.sanitizer.bypassSecurityTrustResourceUrl(this.empresa.linkMaps);
      this.atualizeArraysFotos();

      this.clienteService.obtenhaImagensInstagram().then((respInstagram) => {
        this.possuiInstagram = true;
        this.postsInstagram = respInstagram.posts;
        this.carregou = true;
      }).catch(erroInstagram => {
        this.possuiInstagram = false;
        this.carregou = true;
        console.log(erroInstagram);

      });
    }).catch(erroEmpresa => {
      this.carregou = true;
      console.log(this.mensagemErro);
      this.mensagemErro = "Não foi possível carregar a empresa. Erro: " + erroEmpresa;
    });
  }


  abrirWhatsapp() {
    window.location.href = "http://wa.me/55" + this.empresa.numeroWhatsapp.whatsapp
  }

  abrirGoogleMaps() {
    window.location.href = this.empresa.linkMaps;
  }

  private carregueInformacoesDeHorarioDeFuncionamento() {
    this.estaAberto = this.empresa.estaAberta;

    if(!this.empresa.horariosHoje || this.empresa.horariosHoje.length === 0) {
      this.descricaoHorario = "Fechado";
      return;
    }

    this.descricaoHorario = this.diasDaSemana[this.empresa.horariosHoje[0].diaDaSemana];
    let descricoesHorario = [];

    for(let i = 0; i < this.empresa.horariosHoje.length; i++) {
      let horarioHoje = this.empresa.horariosHoje[i];
      if(!horarioHoje.funciona) {
        descricoesHorario.push("Fechado");
        break;
      }
      descricoesHorario.push(horarioHoje.horarioAbertura.substr(0, 5)  + " - " + horarioHoje.horarioFechamento.substr(0, 5) )
    }
    this.descricaoHorario += " " + descricoesHorario.join(" | ");
  }

  fecharSlides() {
    this.exibirSlideShow = false;
    this.atualizeArraysFotos()
    this.windowScrolling.enable();
  }

  exibirSlide(posicao: number) {
    this.windowScrolling.disable();

    for(let i = 0; i < posicao; i++)
      this.arrayRotate();

    setTimeout(() => {
      this.exibirSlideShow = true;
    }, 0);

  }

  arrayRotate() {
    this.fotosDaEmpresa.push(this.fotosDaEmpresa.shift());
    this.objetosFoto.push(this.objetosFoto.shift());
  }

  private atualizeArraysFotos() {
    this.fotosDaEmpresa = this.empresa.ambiente.map(foto => {
      return {
        url: "https://www.promokit.com.br/images/empresa/" + foto.link,
        titulo: foto.titulo
      };
    });

    this.objetosFoto = this.empresa.ambiente.map(foto => foto);
  }

  fecharSlidesItens() {
    this.windowScrolling.enable();
    this.produtoSelecionado = null;
  }

  exibaProdutoSelecionado(produto: any) {
    this.windowScrolling.disable();
    this.produtoSelecionado = produto;
  }
}
