import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SiteEmpresaRoutingModule } from './site-empresa-routing.module';
import {SiteEmpresaComponent} from "./site-empresa.component";
import {ClienteComponent} from "../cliente/cliente.component";
import {InlineSVGModule} from "ng-inline-svg";
import {NgxMaskModule} from "ngx-mask";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {ConfirmarComponent} from "../confirmar/confirmar.component";
import {FidelidadeModule} from "../fidelidade/fidelidade.module";
import {CountdownModule} from "ngx-countdown";
import {MaskedTextBoxModule, NumericTextBoxModule} from "@progress/kendo-angular-inputs";
import {FrmValideCodigoComponent} from "../frm-valide-codigo/frm-valide-codigo.component";
import {CartaoConfirmadoComponent} from "../cliente/cartao-confirmado/cartao-confirmado.component";
import {SiteEmpresaRouterComponent} from "./site-empresa-router.component";
import {CartaoPontosComponent} from "../cliente/cartao-pontos/cartao-pontos.component";
import {CartaoSeloComponent} from "../cliente/cartao-selo/cartao-selo.component";
import {RegrasComponent} from "../regras/regras.component";
import {CartaoCashbackComponent} from "../cliente/cartao-cashback/cartao-cashback.component";
import {CartaoConsumoSeloComponent} from "../cliente/cartao-consumo-selo/cartao-consumo-selo.component";
import {ExtratoPontosComponent} from "../componentes/extrato-pontos/extrato-pontos.component";
import {CartaoProximosVencimentosComponent} from "../componentes/cartao-proximos-vencimentos/cartao-proximos-vencimentos.component";
import {DropDownListModule} from "@progress/kendo-angular-dropdowns";
import {MeusCartoesComponent} from "../cliente/meus-cartoes/meus-cartoes.component";
import {ScrollViewModule} from "@progress/kendo-angular-scrollview";
import {CompartilhadoModule} from "../compartilhado/compartilhado.module";
import {DatePickerModule} from "@progress/kendo-angular-dateinputs";
import {TabStripModule} from "@progress/kendo-angular-layout";

@NgModule({
    declarations: [
        SiteEmpresaComponent,
        ClienteComponent,
        ConfirmarComponent,
        FrmValideCodigoComponent, ExtratoPontosComponent, CartaoProximosVencimentosComponent,
        CartaoSeloComponent, CartaoPontosComponent, CartaoCashbackComponent,
        CartaoConfirmadoComponent, CartaoConsumoSeloComponent, MeusCartoesComponent,
        SiteEmpresaRouterComponent, RegrasComponent
    ],
    imports: [
        CommonModule,
        ScrollViewModule,
        SiteEmpresaRoutingModule,
        InlineSVGModule,
        FormsModule,
        CountdownModule,
        DropDownListModule,
        ReactiveFormsModule,
        NgxMaskModule,
        FidelidadeModule,
        MaskedTextBoxModule,
        NumericTextBoxModule,
        CompartilhadoModule,
        DatePickerModule,
        TabStripModule
    ]
})
export class SiteEmpresaModule { }
