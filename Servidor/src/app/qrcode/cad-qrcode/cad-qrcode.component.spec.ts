import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CadQrcodeComponent } from './cad-qrcode.component';

describe('CadQrcodeComponent', () => {
  let component: CadQrcodeComponent;
  let fixture: ComponentFixture<CadQrcodeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CadQrcodeComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CadQrcodeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
