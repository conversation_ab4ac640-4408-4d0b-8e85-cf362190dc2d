import { Injectable } from '@angular/core';
import {ServerService} from "./ServerService";
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class TradutorMensagensBotService extends ServerService {
  constructor(private httpCliente: HttpClient) {
    super(httpCliente);
  }

  public obtenhaTraducaoMensagemBot(template: string): Promise<any> {
    return this.obtenha('/tradutor-mensagem-bot/obtenha' , {t: template} );
  }

  salveTraducaoMensagemBot(traducaoMensagemBot: any): Promise<any> {
    return this.salve('/tradutor-mensagem-bot', traducaoMensagemBot);
  }

  obtenhaMensagens() {
    return this.obtenha('/tradutor-mensagem-bot/mensagens', {});
  }

  ativarBot(): Promise<any> {
    return this.facaPost('/tradutor-mensagem-bot/ativar', {});
  }

  desativarBot() {
    return this.facaPost('/tradutor-mensagem-bot/desativar', {});
  }
}
