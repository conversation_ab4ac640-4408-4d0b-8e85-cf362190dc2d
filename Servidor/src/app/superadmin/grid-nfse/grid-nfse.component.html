<h4 _ngcontent-mki-c5="" class="page-title">
  <i class="fa fa-file-invoice"></i>
  Notas Fiscais de Serviço
</h4>

<div class="mt-2"></div>


<div class="card">

  <div class="card-body">
    <kendo-grid  [kendoGridBinding]="faturas" >

  <kendo-grid-column title=""  [width]="50" >
    <ng-template kendoGridCellTemplate let-fatura let-rowIndex="rowIndex">
      <span class="text-muted ">{{rowIndex + 1}}</span>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column title="Id Fatura" >
    <ng-template kendoGridCellTemplate let-fatura  >
      <span class="text-warning"><b>{{fatura.id }}</b></span>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column title="Empresa" >
    <ng-template kendoGridCellTemplate let-fatura  >
      <span>{{fatura.empresa.nome}}</span>
    </ng-template>
  </kendo-grid-column>
    <kendo-grid-column title="Discriminação" >
      <ng-template kendoGridCellTemplate let-fatura  >
        <span>{{fatura.descricaoLancamento}}</span>
      </ng-template>
    </kendo-grid-column>
      <kendo-grid-column title="Data pagamento" >
        <ng-template kendoGridCellTemplate let-fatura  >
          <span>{{fatura.dataPagamento}}</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Valor" >
    <ng-template kendoGridCellTemplate let-fatura>
      <span class="font-weight-bold">{{fatura.valor  | currency: "BRL"}} </span>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column title="Status" field="status">
    <ng-template kendoGridCellTemplate let-fatura  >
      <span class="badge" [ngClass]="{'badge-primary': !fatura.tarefa || fatura.tarefa.status === 'Nova',
                                      'badge-warning': fatura.tarefa && fatura.tarefa.status === 'EmProcessamento',
                                      'badge-danger': fatura.tarefa && fatura.tarefa.status === 'Erro',
                                      'badge-success': fatura.tarefa && fatura.tarefa.status === 'Concluida'}" >
        {{fatura.tarefa ? fatura.tarefa.status : 'Nova'}}
      </span><br/>

      <span class="text-danger" *ngIf="fatura.mensagem || (fatura.tarefa && fatura.tarefa.status === 'Erro')"> {{fatura.tarefa
      && fatura.tarefa.mensagem ? fatura.tarefa.mensagem : fatura.mensagem}}
      </span>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column title=""  >
    <ng-template kendoGridCellTemplate let-fatura let-rowIndex="rowIndex">

      <!--  <button class="btn btn-success"  *ngIf="!fatura.pagamento" (click)="gereBoleto(fatura)" [disabled]="gerandoBoleto" title="gerar boleto da fatura" kendoTooltip>
          Gerar <i class="fa fa-barcode"></i>
        </button> -->

      <button class="btn btn-success btn-xs ml-3  float-left" [disabled]="enviando"  (click)="enviarNota(fatura)"
              *ngIf="(!fatura.tarefa || fatura.tarefa.status === 'Nova') || fatura.tarefa.status === 'Erro'"
              title="Enviar nota" kendoTooltip> <i *ngIf="!enviando" class="k-icon k-i-arrow-up btn-small"></i>
                                                <i *ngIf="enviando" class="k-i-loading k-icon mr-1" ></i>
      </button>

      <button class="btn btn-info btn-xs ml-3  "   (click)="verNota(fatura.tarefa)"
              *ngIf="fatura.tarefa && fatura.tarefa.status === 'Concluida'  "
              title="Ver nota" kendoTooltip>
        Ver nota

      </button>


    </ng-template>
  </kendo-grid-column>
</kendo-grid>
  </div>
</div>
