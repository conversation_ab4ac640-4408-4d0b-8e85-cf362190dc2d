import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { UploadMultiplasImagensComponent } from './upload-multiplas-imagens.component';

describe('UploadMultiplasImagensComponent', () => {
  let component: UploadMultiplasImagensComponent;
  let fixture: ComponentFixture<UploadMultiplasImagensComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ UploadMultiplasImagensComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UploadMultiplasImagensComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
