import {Component, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {RelatorioRecebimentosComponent} from "../../componentes/relatorio-recebimentos/relatorio-recebimentos.component";



@Component({
  selector: 'app-tela-resumo-financeiro-ecletica',
  templateUrl: './tela-resumo-financeiro-ecletica.component.html',
  styleUrls: ['./tela-resumo-financeiro-ecletica.component.scss']
})
export class TelaResumoFinanceiroEcleticaComponent implements OnInit {
  @ViewChild('relatorioRecebimentos', { static: true}) relatorioRecebimentos: RelatorioRecebimentosComponent;
  constructor(private activatedRoute: ActivatedRoute) { }

  ngOnInit(): void {

  }
}
