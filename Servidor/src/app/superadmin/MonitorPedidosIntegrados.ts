import {Injectable} from "@angular/core";
import {TelaImprimirPedido} from "../fidelidade/TelaImprimirPedido";
import {EnumFiltroDePedidos, PedidosService} from "../services/pedidos.service";
import {AutorizacaoService} from "../services/autorizacao.service";
import {GrupolojasService} from "./services/grupolojas.service";
import {ConstantsService} from "../fidelidade/ConstantsService";
import {ImpressaoService} from "../services/impressao.service";
import {ArmazenamentoService} from "../services/armazenamento.service";
import {DeviceDetectorService} from "ngx-device-detector";
import {BehaviorSubject, timer} from "rxjs";

declare var $;
const segundosTimer = 60;
@Injectable()
export class MonitoradorPedidosIntegrados {
  public novosPedidos = [];
  public pedidos = [];
  public isMobile: boolean;
  public resumoPedidos: any = new BehaviorSubject<any>(null);  //
  public resumoPedidos$ = this.resumoPedidos.asObservable();
  beep;
  timerPedidos;
  ultimoPedido: any;
  tarefaAlerta: any;
  constructor(  protected pedidosService: PedidosService, private autorizacaoService: AutorizacaoService,
                private     grupolojasService: GrupolojasService,
                private constantsService: ConstantsService, impressao: ImpressaoService,
                private armazenamentoService: ArmazenamentoService,
                private detectorDevice: DeviceDetectorService) {

    this.isMobile = this.detectorDevice.isMobile();

    this.resumoPedidos.next ({ totalNovo: 0, mesas: []})
  }

 async inicieMonitoramento() {
    if ( this.timerPedidos)  return;

    this.beep = document.getElementById("beep");
    if( this.beep){
      this.beep.load();

      await this.inicieMonitoramentoPedidos();
    } else {
      setTimeout( () => { this.inicieMonitoramento()}, 1000);
    }

  }


  private async inicieMonitoramentoPedidos() {

    let resposta: any = await this.pedidosService.listeUltimoPedidosIntegradoErros(segundosTimer    , []);

    if(resposta){
      this.ultimoPedido = resposta.ultimo;
      if(!this.timerPedidos){
        this.timerPedidos =  timer(2000, 1000 * segundosTimer  ).subscribe( () => {
          this.carregueNovosPedidos()
        });
      }
    }
  }

  carregueNovosPedidos(){
    // console.log ("Carregando novos pedidos " + new Date())
    let filtro: any = {   ultimoPedido: this.ultimoPedido ? this.ultimoPedido.id : null }

    this.pedidosService.listeIntegradosComErro(0, 100, filtro).then( async (novosPedidos) => {
      if(novosPedidos){
        let tocarAlterta = false;

        novosPedidos.forEach((pedido: any) => {
          if(this.chegouNovoPedido(pedido))
            tocarAlterta = true;
        });

        if(tocarAlterta)
          this.inicieAlerta();
      }


    }).catch( () => {

    });
  }

  chegouNovoPedido(pedido: any){
    //as vezes sever cai, pode encavalar request.
    let pedidoExistente =  this.novosPedidos.find((item: any) => item.id === pedido.id);

    if(pedidoExistente) return false;

    if(!this.ultimoPedido  || this.ultimoPedido.id < pedido.id)
      this.ultimoPedido = pedido;

    this.novosPedidos.push(pedido);
    this.pedidos.push(pedido);

    return true;

  }

  pareMonitoramento() {
    if(this.timerPedidos)
      this.timerPedidos.unsubscribe();
  }

  inicieAlerta(){
    if( this.tarefaAlerta) return;
    $("#alertaNovo").modal();
    $("#alertaNovo").on("hidden.bs.modal",   () => {
      this.pareAlerta();
    });

    this.tarefaAlerta =  timer(0, 1500  ).subscribe( () => {
      this.toqueAlerta();
    } )
  }

  pareAlerta(){
    this.beep.pause();
    this.beep.currentTime = 0;

    if(this.tarefaAlerta)
      this.tarefaAlerta.unsubscribe();

    delete this.tarefaAlerta;
    // window.scrollTo(0, document.body.scrollHeight);
  }

  toqueAlerta(){
    this.beep.play().then( () => {
    }).catch (e => {
      console.log('nao tocou alerta: ')
      console.log(e)
    });

  }

  fecheEZerePedidosNovos(){
    this.novosPedidos = [];
    this.fecheModalAlerta();
  }

  fecheModalAlerta(){
    $("#alertaNovo").modal('hide');
  }



}
