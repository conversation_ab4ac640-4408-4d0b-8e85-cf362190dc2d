import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ModulosService } from '../../../services/modulos.service';

@Component({
  selector: 'app-form-modulo',
  templateUrl: './form-modulo.component.html',
  styleUrls: ['./form-modulo.component.scss']
})
export class FormModuloComponent implements OnInit {
  modulo: any = {
    nome: '',
    valorMensalidade: 0,
    valorAtivacao: 0,
    descricao: ''
  };

  edicao = false;
  carregando = false;
  salvando = false;
  mensagemErro = '';
  mensagemSucesso = '';

  constructor(
    private modulosService: ModulosService,
    private router: Router,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id && id !== 'novo') {
      this.edicao = true;
      this.carregueModulo(Number(id));
    }
  }

  carregueModulo(id: number): void {
    this.carregando = true;
    this.limparMensagens();
    
    this.modulosService.selecioneModulo(id).then((modulo: any) => {
      this.modulo = modulo || {};
      this.carregando = false;
    }).catch((erro) => {
      console.error('Erro ao carregar módulo:', erro);
      this.carregando = false;
      this.mensagemErro = 'Erro ao carregar módulo. Tente novamente.';
      setTimeout(() => {
        this.router.navigate(['/superadmin/modulos']);
      }, 3000);
    });
  }

  onSalvar(): void {
    this.limparMensagens();
    
    if (!this.validarFormulario()) {
      return;
    }

    this.salvando = true;

    const operacao = this.edicao
      ? this.modulosService.atualizeModulo(this.modulo)
      : this.modulosService.insiraModulo(this.modulo);

    operacao.then((resposta: any) => {
      this.salvando = false;
      this.mensagemSucesso = 'Módulo salvo com sucesso!';
      setTimeout(() => {
        this.router.navigate(['/superadmin/modulos']);
      }, 2000);
    }).catch((erro) => {
      this.salvando = false;
      console.error('Erro ao salvar módulo:', erro);
      this.mensagemErro = 'Erro ao salvar módulo. Verifique os dados e tente novamente.';
    });
  }

  validarFormulario(): boolean {
    if (!this.modulo.nome || this.modulo.nome.trim() === '') {
      this.mensagemErro = 'O campo Nome é obrigatório.';
      return false;
    }

    if (this.modulo.valorMensalidade < 0) {
      this.mensagemErro = 'O Valor da Mensalidade deve ser maior ou igual a zero.';
      return false;
    }

    if (this.modulo.valorAtivacao < 0) {
      this.mensagemErro = 'O Valor da Ativação deve ser maior ou igual a zero.';
      return false;
    }

    return true;
  }

  limparMensagens(): void {
    this.mensagemErro = '';
    this.mensagemSucesso = '';
  }

  onCancelar(): void {
    this.router.navigate(['/superadmin/modulos']);
  }
}
