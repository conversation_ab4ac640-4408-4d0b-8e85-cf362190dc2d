.scroll{
  max-height: 400px;
  overflow-y: scroll;
  overflow-x: hidden;
}

fieldset {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 1rem;

  legend {
    width: auto;
    padding: 0 10px;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 500;
    color: #495057;
    background: #fff;
    float: none;
  }
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #495057;
}

.form-control {
  &:disabled {
    background-color: #e9ecef;
    opacity: 0.8;
  }
}

.text-warning {
  font-size: 0.875rem;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
  margin-top: 1rem;
}

.config-fiscal-container {
  padding: 15px;
}

.config-fiscal-title {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 20px;
  color: #333;
}

.ipi-section {
  background-color: #f8f9fa;
  border-radius: 5px;
  padding: 15px;
  margin-top: 20px;
}

.ipi-title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 15px;
  color: #495057;
}

.form-check-label {
  font-weight: normal;
}

kendo-numerictextbox.form-control {
  width: 100%;
  height: auto;
}

kendo-dropdownlist.form-control {
  width: 100%;
  height: auto;
}


form {
  max-width: 100%;
  padding: 10px !important;
  overflow-x: hidden;
}

.form-control {
  max-width: 100%;
}

kendo-grid {
  max-width: 100%;
  overflow-x: auto;
}

@media (max-width: 768px) {
  .modal-footer {
    flex-direction: column;
    gap: 10px;

    button {
      width: 100%;
      margin: 0 !important;
    }
  }

  .k-grid {
    font-size: 14px;
  }

  .coluna_menor {
    width: 80px !important;
  }

  .k-grid img {
    max-width: 60px;
    height: auto;
  }

  .k-combobox {
    width: 100% !important;
  }
}
