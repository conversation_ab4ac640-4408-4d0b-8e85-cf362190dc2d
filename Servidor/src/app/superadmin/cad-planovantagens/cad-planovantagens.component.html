<div class="modal-content">
  <div class="modal-header">
    <h4 class="modal-title" id="myModalLabel">
       Vantagens do plano <b>{{plano.nome}}</b>
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="fecheModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div  class="modal-body">

    <div *ngIf="!adicionarVantagem && !vantagemExcluir" class="lista-vantagens">
      <h5 class="float-left">Vangatens: </h5>

      <button class="btn btn-blue float-right" (click)="adicionarNovaVantagem()"   >
        Adicionar vantagem
      </button>

      <div class="clearfix mb-3"></div>

      <p class="text-muted" *ngIf="!plano.vantagens?.length">Nenhuma vantagem vinculada ao plano</p>

      <kendo-sortable #sortable [data]="plano.vantagens"
            [navigatable]="true"
            [animation] = "true"
            (dragOver)="onDragOver($event)"
            (navigate)="onNavigate($event)"
            (dragEnd)="onDragEnd($event)"
            activeItemClass="active">

      <ng-template let-item="item" >
        <div class="btn btn-block  btn-outline-dark mb-1"   style="    width: 75%;cursor: move;display: inline-block;" >
          <span class="btn-label" >{{item.ordem }}</span>
          <span class="abreviar">  {{item.vantagem.descricao}}</span>
        </div>

        <div class="d-inline ml-2">
          <kendo-switch [(ngModel)]="item.disponivel" name="ativo" id="ativo" (click)="atualizeVantagemDisponivel(item)"  ></kendo-switch>
        </div>

        <i class="fa fa-trash text-danger fa-lg  ml-2 cpointer" (click)="excluirVantagem(item)"></i>



      </ng-template>

    </kendo-sortable>


    </div>

    <div *ngIf="adicionarVantagem" >
      <div class="form-group mt-4"  >
        <label  >Novas vantagens adicionar   </label>
        <kendo-multiselect id="vantagens" name="vantagens"   [(ngModel)]="novasVantagens" [data]="vantagensNaoAdicionadas"
                           [filterable]="true" [allowCustom]="false"     class="form-control"
                           (ngModelChange)="aletrouVantagem($event)" placeholder="Selecione um ou mais vantagens"
                           [kendoDropDownFilter]="filterSettings"     [valueField]="'id'" [textField]="'descricao'">
        </kendo-multiselect>

        <div class="invalid-feedback"  >
          Selecione pelo menos uma vantagem.
        </div>
      </div>

      <button class="btn btn-success " (click)="salveNovasVantagens()" [disabled]="salvando">Salvar</button>
      <button class="btn btn-light " (click)="cancelarNovaVantagem()">Cancelar</button>
    </div>

    <div *ngIf="vantagemExcluir" class="mt-4">
      <p>Confirma exclusão da vantagem "<b>{{vantagemExcluir.vantagem.descricao}}</b>" ? </p>

      <button class="btn btn-danger " (click)="excluaVantagem()" [disabled]="salvando">Excluir</button>
      <button class="btn btn-light " (click)="cancelarExcluir()">Cancelar</button>

    </div>

  </div>
</div>
