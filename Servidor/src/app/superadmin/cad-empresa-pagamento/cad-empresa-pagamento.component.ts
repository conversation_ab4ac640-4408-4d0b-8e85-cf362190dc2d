import {Component, OnInit, ViewChild} from '@angular/core';
import {NgForm} from "@angular/forms";
import {ClienteService} from "../../services/cliente.service";
import {EmpresasService} from "../services/empresas.service";
import {ContratoService} from "../services/contrato.service";
import {DropDownFilterSettings} from "@progress/kendo-angular-dropdowns";

interface SubscriptionItem {
  $id: any;
  quantidade: number;
  descricao: string;
  valor: number;
  total: number;
  recorrente: boolean;
}

@Component({
  selector: 'app-cad-empresa-pagamento',
  templateUrl: './cad-empresa-pagamento.component.html',
  styleUrls: ['./cad-empresa-pagamento.component.scss']
})
export class CadEmpresaPagamentoComponent implements OnInit {
  @ViewChild('frm') form: NgForm;
  taxaAdesaoPadrao = 200;
  empresa: any = {};
  responsavel: any = {};
  contrato: any = {plano: {}, taxaAdesao: this.taxaAdesaoPadrao };
  planos: any = [];
  mensagemErro: any;
  salvando: any;
  formasDePagamento: any = ['cartao-credito', 'boleto', 'pix'];
  diasVencimento: any = [];
  assinatura: any = {};
  fatura: any = { taxaAdesao: this.taxaAdesaoPadrao};
  buscandoCEP: boolean;
  negociarValor: any;
  naoPossuiCnpj: boolean;
  gerarAssinatura: boolean;
  gerarFatura: boolean;
  buscandoEmpresa: boolean;
  empresaJaTemContrato: boolean;
  enderecoCompleto: any = {}
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: 'contains'
  };
  public items: SubscriptionItem[] = [];
  constructor(private clienteService: ClienteService, private empresasService: EmpresasService,
              private contratoService: ContratoService) { }

  ngOnInit(): void {
    let hoje = new Date(),
      diaMaximo = 31;

    for (let dia = 1; dia <= diaMaximo; dia++) {
      if (dia >= hoje.getDate() || ( hoje.getDate() > diaMaximo && dia <= 5 ))
        this.diasVencimento.push(dia);
    }

    this.clienteService.obtenhaPlanoEmpresariaisAdmin().then(resp => {
      this.planos = resp;
    })
  }

  async salveEmpresa(){
    if(!this.empresa.cnpj)
       this.empresa.responsavel = this.responsavel;

    this.empresa.enderecoCompleto = this.enderecoCompleto;

    let resposta = await  this.empresasService.salve(this.empresa).catch(erroSalvar => {
      this.mensagemErro = erroSalvar;
      this.salvando = false;
    });

    if(resposta && resposta.id) {
      this.empresa.id = resposta.id;
      if(resposta.idResponsavel) this.responsavel.id = resposta.idResponsavel;
    }

  }

  async salveContrato(){

    this.contrato.valorNegociado = this.obtenhaValorNegociado();

    let resposta = await this.contratoService.salveContrato(this.empresa, this.contrato).catch(erroSalvar => {
      this.mensagemErro = erroSalvar;
      this.salvando = false;
    });

    if(resposta)
      this.contrato.id = resposta.id;
  }

  async onSubmit() {
    if(this.salvando) return;
    delete this.mensagemErro;

    if(this.form.valid){
      this.salvando = true;
      await this.salveEmpresa();
      if(!this.empresa.id) return;

      if(this.gerarAssinatura)
        await this.gereAssinaturaDaEmpresa();

      if(this.gerarFatura)
        await this.gereFaturaAdesao();

    }
  }

  async gereFaturaAdesao(){

    this.contratoService.crieFaturaAdesao(this.empresa, this.fatura.taxaAdesao, this.fatura.formaDePagamento).then(resposta => {
      this.fatura.codigo = resposta.codigo;
      this.fatura.url = resposta.url;
    }).catch((erro) => {
      this.salvando = false;
      this.mensagemErro =  'falha ao criar fatura de adesão: ' + erro;
    })
  }

  async gereAssinaturaDaEmpresa(){
    await this.salveContrato();

    if(!this.mensagemErro){
      if(this.contrato.id){
        this.contratoService.crieAssinaturaNova(this.empresa, this.contrato.formaDePagamento, this.items).then(assinatura => {
          this.assinatura = assinatura;
        }).catch((erro) => {
          this.salvando = false;
          this.mensagemErro =  'falha ao criar contrato: ' + erro;
        })
      }
    }

  }



  alterou($event: Event) {
    if( this.empresa.cep.length < 8 ) {
      return;
    }

    this.buscandoCEP = true;

    this.clienteService.busquePorCEP(this.unmask(this.empresa.cep)).then( (resposta) => {
      this.buscandoCEP = false;
      if(resposta.localidade){
        this.setEndereco(resposta)
      }


    }).catch( () => {
      this.buscandoCEP = false;
    });
  }

  setEndereco(resposta: any){
    let logradouro = resposta.logradouro;
    let bairro = resposta.bairro;
    const localidade = resposta.localidade;
    const nomeCidade = localidade.split('/')[0];
    const sigla = localidade.split('/')[1];

    this.enderecoCompleto = {
      bairro: bairro,
      logradouro: logradouro,
      cidade: nomeCidade,
      estado: sigla,
      localidade: String(`${nomeCidade} - ${sigla}`)
    }

  }

  obtenhaValorPrimeiroPagamento() {
    let valorPlano = this.contrato.plano.valor;

    let totalItens = this.items.reduce((soma: number, item: any) => soma + item.total, 0)

    return valorPlano + totalItens;
  }

  obtenhaValorPagamentosRecorrentes() {
    let valorPlano = this.contrato.plano.valor;

    let totalRecorrentes =
      this.items.filter((item: any) => item.recorrente).reduce((soma: number, item: any) => soma + item.total, 0)

    return valorPlano + totalRecorrentes;
  }

  obtenhaValorNegociado() {
    let valorPlano = this.contrato.plano.valor;

    let totalDescontos =
      this.items.filter((item: any) => item.recorrente && item.total < 0).reduce((soma: number, item: any) => soma + item.total, 0)

    return valorPlano + totalDescontos;
  }


  exibaValoresNegociar() {
    this.negociarValor = true;
  }

  cadastrarResponsavel() {
    this.naoPossuiCnpj = true;
  }

  cadastrarCNPJ() {
    this.naoPossuiCnpj = false;
  }

  alterouCnpj() {

   if(this.empresa.cnpj.length === 14){
     this.buscandoEmpresa = true;
      this.empresasService.obtenhaEmpresaCnpj(this.empresa.cnpj).then( (empresas: any) => {
        if(empresas && empresas.length){
          this.empresa = empresas[0];
          if(this.empresa.enderecoCompleto){
            this.enderecoCompleto = Object.assign({}, this.empresa.enderecoCompleto)
            this.enderecoCompleto.localidade = String(`${this.empresa.enderecoCompleto.cidade.nome} - ${this.empresa.enderecoCompleto.estado.sigla}`)
          }

          this.contratoService.obtenhaContrato(this.empresa).then( (contrato: any) => {
            if(contrato && contrato.id) {

              if(contrato.assinatura  ){
                this.empresaJaTemContrato = true;

              } else {
                this.contrato = contrato;
                this.contrato.plano = this.planos.find( plano => plano.id === contrato.plano.id);


                if(this.contrato.plano)
                  this.selecionouPlano()
              }

            }
            this.buscandoEmpresa = false;
          })
        } else {
          this.buscandoEmpresa = false;
        }
      }).catch( erro => {
        this.buscandoEmpresa = false;
      })
   }
  }

  selecioneGerarAssinatura() {
    this.gerarAssinatura = true;


  }

  selecioneGerarFatura() {
    this.gerarFatura = true;
  }

  unmask(val) {
    return val ? val.replace(/\D+/g, '') : val;
  }

  selecionouPlano() {
    this.setParcelamenetoPadrao();
    this.items = []

    if(this.contrato.plano){
      this.contrato.limiteContatosNegociado = this.contrato.plano.limiteContatos
      this.addItemTaxaPadraoAdesao();
      this.addItemDesconto();
    }

  }

  voltar() {
    history.back();
  }

  cancele() {
    this.voltar()
  }

  gerarNovoContrato() {
    delete this.empresa.id;
    delete this.empresa.dominio;
    this.empresaJaTemContrato = false;
  }

  alterouFormasPagamento(){
    if(this.pagarNoCartao()){
      this.contrato.parcelar = true;
      this.setParcelamenetoPadrao();
    }
  }

  setParcelamenetoPadrao(){
    if(this.contrato.plano  )
      this.contrato.numeroParcelas = this.contrato.plano.intervalo || 1

  }

  pagarNoCartao() {
    return this.contrato.formaDePagamento && this.contrato.formaDePagamento.indexOf('cartao-credito') >= 0
  }

  public addItem() {
    this.items.push({$id: this.generateId(), quantidade: 1, descricao: '', valor: 0, total: 0, recorrente: true });
  }

  addItemTaxaPadraoAdesao(){
    this.items.push({ $id: this.generateId(), quantidade: 1, descricao: 'Taxa de Adesão',
      valor: this.taxaAdesaoPadrao, total:  this.taxaAdesaoPadrao, recorrente: false });
  }

  // Gera um ID único para cada item
  private generateId(): any {
    return Math.random().toString(36).substr(2, 9);
  }

  addItemDesconto(){
    let valorDescontoPadrao = this.contrato.plano.descontoPadrao || 0;

    if(valorDescontoPadrao){
      let descricao = `Desconto ${this.contrato.plano.periodoTexto || ''}`.trim()

      this.items.push({ $id: this.generateId(), quantidade: 1, descricao: descricao, valor: -valorDescontoPadrao,
        total:   -valorDescontoPadrao, recorrente: true });
    }


  }

  public removeItem(index: number) {
    this.items.splice(index, 1);
  }

  public updateTotal(item: SubscriptionItem) {
    item.total = item.quantidade * item.valor;
  }
}
