.drawer-backdrop {
  position: fixed;
  top: 64px; /* altura do header */
  left: 0;
  width: 100vw;
  height: calc(100vh - 64px);
  background: rgba(0,0,0,0.5);
  z-index: 1040;
  backdrop-filter: blur(2px);
}

.drawer-lateral {
  position: fixed;
  top: 64px; /* altura do header */
  right: 0;
  width: 420px;
  max-width: 100vw;
  height: calc(100vh - 64px);
  background: #fff;
  z-index: 1050;
  box-shadow: -2px 0 15px rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
  animation: drawerIn 0.3s cubic-bezier(0.4,0,0.2,1);
  overflow: hidden;
}

.drawer-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(0,0,0,0.1);
  background-color: #f8f9fa;
}

.drawer-body {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.drawer-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0,0,0,0.1);
  background-color: #f8f9fa;
  display: flex;
  justify-content: flex-end;
}

.form-card {
  background: #fff;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #495057;
}

.form-control:focus {
  border-color: #7266ba;
  box-shadow: 0 0 0 0.2rem rgba(114, 102, 186, 0.15);
}

@keyframes drawerIn {
  from { right: -440px; opacity: 0; }
  to { right: 0; opacity: 1; }
}

.drawer-lateral .close {
  font-size: 1.6rem;
  line-height: 1;
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  color: #6c757d;
  transition: color 0.2s;
}

.drawer-lateral .close:hover {
  color: #343a40;
}

.btn {
  transition: all 0.3s;
}

.btn-primary {
  background-color: #7266ba;
  border-color: #7266ba;
}

.btn-primary:hover, .btn-primary:focus {
  background-color: #6558b1;
  border-color: #6558b1;
}

.btn-light:hover {
  background-color: #e2e6ea;
}

@media (max-width: 500px) {
  .drawer-lateral {
    width: 100vw;
    min-width: 0;
  }
}

// Estilos dos botões de ação
.btn-action {
  background-color: rgba(74, 111, 220, 0.1);
  color: #4A6FDC;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.btn-edit {
  background-color: rgba(74, 111, 220, 0.1);
  color: #4A6FDC;

  &:hover {
    background-color: rgba(74, 111, 220, 0.2) !important;
  }
}

.btn-delete {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;

  &:hover {
    background-color: rgba(220, 53, 69, 0.2) !important;
  }
}

// Estilo do botão de adicionar CEP
.btn-add-cep {
  height: 48px;
  padding: 0 24px;
  border-radius: 12px;
  border: none;
  background: linear-gradient(135deg, #5e72e4 0%, #4A6FDC 100%);
  color: white;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(74, 111, 220, 0.2);
  transition: all 0.3s ease;

  .fe-plus-circle {
    font-size: 18px;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(74, 111, 220, 0.3);
    background: linear-gradient(135deg, #6a80ee 0%, #5a7de4 100%);
  }

  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(74, 111, 220, 0.3);
  }
}

// Remover a animação dos ícones de validação mas manter os ícones
.was-validated .form-control:valid,
.was-validated .form-control:invalid {
  background-position: right calc(0.375em + 0.1875rem) center !important;
  transition: none !important;
  animation: none !important;
}

.form-control:valid,
.form-control:invalid {
  background-image: none;
}

.was-validated .form-control:valid {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  border-color: #28a745;
  padding-right: calc(1.5em + 0.75rem);
  background-repeat: no-repeat;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='0 0 12 12'%3e%3cpath d='M10.293 1.293a1 1 0 011.414 0l.001.001a1 1 0 010 1.414L8.414 6l3.294 3.293a1 1 0 01.001 1.414 1 1 0 01-1.415.001L7 7.414l-3.293 3.294a1 1 0 01-1.414-.001 1 1 0 010-1.414L5.586 6 2.293 2.707a1 1 0 010-1.414 1 1 0 011.414-.001L7 4.586l3.293-3.293z'/%3e%3c/svg%3e");
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-repeat: no-repeat;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

