import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { CadTipoDePontuacaoComponent } from './cad-tipo-de-pontuacao.component';

describe('CadTipoDePontuacaoComponent', () => {
  let component: CadTipoDePontuacaoComponent;
  let fixture: ComponentFixture<CadTipoDePontuacaoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ CadTipoDePontuacaoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CadTipoDePontuacaoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
