import { Component, OnInit } from '@angular/core';
import {EmpresasService} from "../../services/empresas.service";
import {CatalogosService} from "../../services/catalogos.service";
import {ModalKendo} from "../../../lib/ModalKendo";

@Component({
  selector: 'app-tela-importar-catalogo-empresa',
  templateUrl: './tela-importar-catalogo-empresa.component.html',
  styleUrls: ['./tela-importar-catalogo-empresa.component.scss']
})
export class TelaImportarCatalogoEmpresaComponent extends ModalKendo implements OnInit {
  lojas: any[] = [];
  lojaSelecionada: any = null;
  textoConfirmacao = '';
  resumo: any = { totalCategorias: 0, totalProdutos: 0, categorias: [] };
  carregando = false;
  empresa: any = {};
  carregandoResumo = false;
  importando = false;
  importou = false;
  erroImportacao: string;

  // Controle de passos
  passoAtual = 1;
  totalPassos = 3;

  constructor(private empresasService: EmpresasService, private empresaService: EmpresasService,
              private catalogosService: CatalogosService) {
    super()
  }

  ngOnInit() {
    this.carregarLojasRede();
  }

  async carregarLojasRede() {
    try {
      this.carregando = true;
      this.erroImportacao = null; // Limpar erros anteriores

      let resposta: any;

      if(this.empresa.grupoDaLoja)
        resposta = await   this.empresaService.listeTodasEmpresasGrupoLoja() ;
      else
        resposta = await   this.empresasService.listeTodasEmpresasRede();

      this.lojas = resposta.empresas || [];
      this.lojas = this.lojas.filter((item: any) => item.id !== this.empresa.id);

      this.lojas.sort((a, b) => {
        const dataA = new Date(a.catalogo.atualizacao).getTime();
        const dataB = new Date(b.catalogo.atualizacao).getTime();
        return dataB - dataA; // ordem decrescente
      });

    } catch (erro) {
      console.error('Erro ao carregar lojas:', erro);
      this.erroImportacao = 'Erro ao carregar lojas da rede: ' + (erro?.message || erro);
    } finally {
      this.carregando = false;
    }
  }

  async carregarResumo(loja: any) {
    if (!loja) return;

    try {
      this.carregandoResumo = true;
      this.erroImportacao = null; // Limpar erros anteriores

      this.resumo = await this.catalogosService.obtenhaResumoProdutosLoja(loja.catalogo.id);

    } catch (erro) {
      console.error('Erro ao carregar resumo:', erro);
      this.erroImportacao = 'Erro ao carregar resumo do catálogo: ' + (erro?.message || erro);
      // Reset do resumo em caso de erro
      this.resumo = { totalCategorias: 0, totalProdutos: 0, categorias: [] };
    } finally {
      this.carregandoResumo = false;
    }
  }

  async importar() {
    if (!this.digitouImportar()) {
      this.erroImportacao = 'Digite "' + this.getTextoImportar() + '" para confirmar a operação';
      return;
    }

    if (!this.lojaSelecionada) {
      this.erroImportacao = 'Selecione uma loja para importar o catálogo';
      return;
    }

    try {
      this.importando = true;
      this.erroImportacao = null;

      // Se há produtos na loja atual, limpar primeiro
      if (this.resumo.quantidadeProdutosDaLoja) {
        await this.empresaService.limpeCatalogoDaLoja(true);
      }

      // Importar catálogo da loja selecionada
      await this.catalogosService.importeCatalogoDe(this.lojaSelecionada).catch((err) => {
         throw err;
      });

      this.importou = true;

      // Limpar dados para próxima importação (se houver)
      this.textoConfirmacao = '';

    } catch (erro) {
      console.error('Erro na importação:', erro);
      this.erroImportacao = 'Falha na importação: ' + (erro?.message || erro);
    } finally {
      this.importando = false;
    }
  }

  formatarData(data: string) {
    if (!data) return '';
    return new Date(data).toLocaleDateString('pt-BR');
  }

  formatarHora(data: Date) {
    if (!data) return '';
    return data.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  onSelecionarLoja(lojaSelecionada: any) {
    if (lojaSelecionada) {
      // Limpar dados anteriores
      this.resumo = { totalCategorias: 0, totalProdutos: 0, categorias: [] };
      this.textoConfirmacao = '';
      this.erroImportacao = null;

      // Carregar resumo da nova loja
      this.carregarResumo(lojaSelecionada);
    }
  }

  digitouImportar(): boolean {
    return this.textoConfirmacao &&
           this.textoConfirmacao.trim().toUpperCase() === this.getTextoImportar().toUpperCase();
  }

  getTextoImportar(): string {
    if (this.resumo.quantidadeProdutosDaLoja > 0) {
      return 'REMOVER E IMPORTAR';
    }
    return 'IMPORTAR';
  }

  /**
   * Função de tracking para otimizar a renderização da lista de categorias
   */
  trackByCategoria(index: number, categoria: any): any {
    return categoria?.id || categoria?.nome || index;
  }

  /**
   * Verifica se pode prosseguir para o próximo passo
   */
  podeImportar(): boolean {
    return this.lojaSelecionada &&
           this.resumo.totalProdutos > 0 &&
           this.digitouImportar() &&
           !this.importando;
  }

  /**
   * Navegação entre passos
   */
  proximoPasso() {
    if (this.podeAvancar()) {
      this.passoAtual++;
      this.erroImportacao = null; // Limpar erros ao avançar
    }
  }

  passoAnterior() {
    if (this.podeVoltar()) {
      this.passoAtual--;
      this.erroImportacao = null; // Limpar erros ao voltar
    }
  }

  irParaPasso(passo: number) {
    if (passo >= 1 && passo <= this.totalPassos) {
      this.passoAtual = passo;
      this.erroImportacao = null;
    }
  }

  /**
   * Validações de navegação
   */
  podeAvancar(): boolean {
    switch (this.passoAtual) {
      case 1:
        return this.lojaSelecionada !== null && !this.carregando;
      case 2:
        return this.resumo.totalProdutos > 0 && !this.carregandoResumo;
      case 3:
        return false; // Último passo, não pode avançar
      default:
        return false;
    }
  }

  podeVoltar(): boolean {
    return this.passoAtual > 1 && !this.importando;
  }

  podeClicarPasso(passo: number): boolean {
    switch (passo) {
      case 1:
        return true; // Sempre pode voltar ao primeiro passo
      case 2:
        return this.lojaSelecionada !== null; // Só pode ir ao passo 2 se tem loja selecionada
      case 3:
        return this.lojaSelecionada !== null && this.resumo.totalProdutos > 0; // Só pode ir ao passo 3 se tem dados
      default:
        return false;
    }
  }

  /**
   * Estados dos passos para o stepper
   */
  getStatusPasso(passo: number): string {
    if (passo < this.passoAtual) {
      return 'completed';
    } else if (passo === this.passoAtual) {
      return 'active';
    } else {
      return 'pending';
    }
  }

  /**
   * Reseta o estado da tela para uma nova importação
   */
  resetarTela() {
    this.lojaSelecionada = null;
    this.textoConfirmacao = '';
    this.resumo = { totalCategorias: 0, totalProdutos: 0, categorias: [] };
    this.erroImportacao = null;
    this.importou = false;
    this.importando = false;
    this.carregandoResumo = false;
    this.passoAtual = 1;
  }

  feche() {
    this.fecheModal({ importou: this.importou });
  }
}
