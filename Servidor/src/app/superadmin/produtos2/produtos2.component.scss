.sim {
  color: #6db31b;
  font-weight: bold;
}

.nao {
  color: red;
  font-weight: bold;
}

input.busca {
  width: 380px;
}

.riscado{
  text-decoration: line-through;
}

.mudar-ordem{
  position: absolute; top:1px; left: -4px;

  .fa-chevron-up{
    position: relative;top: -5px;
    font-size: 1.3em;
  }

  .fa-chevron-down{
    position: relative;top: 7px; left: -16px;
    font-size: 1.3em;
  }

  &.desabilitado{
    .fa-chevron-up, .fa-chevron-down{
      color: #ccc;
    }
  }
}

.ordem{
  .btn{
       padding: 0;
  }
}

:host ::ng-deep .gridProdutos {
  border: none;
}

:host ::ng-deep .gridProdutos td {
  border-width: 0;
}

:host ::ng-deep .gridProdutos .k-hierarchy-cell, :host ::ng-deep .k-hierarchy-col {
  display: none;
}

:host ::ng-deep .k-grid-content {
  overflow-y: hidden;
}

.botoes .btn {
  margin-left: 5px;
}

.botoes .btn:first-child {
  margin-left: 0px;
}

.comandos {
  text-align: right;
}




.categoria {
  background: #fff;
  padding: 5px;
  padding-left: 20px;
  width: 100%;
  box-shadow: none;
  position: sticky;
  top: 205px;z-index: 8;
  border: solid 1px #ececec;
  border-bottom: solid 1px #dedbdb;
  border-radius: 0px;
}

@media only screen and (max-width: 600px) {
  .hidden-phone {
    display: none;
  }

  input.busca {
    width: 100%;
  }
}

::ng-deep .gridProdutos .k-detail-cell {
  position: relative;
}


::ng-deep .gridProdutos .k-detail-cell::before {
  content: " ";
  background: #ccc;
  width: 1px;
  position: absolute;
  height: calc(100% - 10px);
  left: 27px;
  top: 5px;
}

::ng-deep .gridProdutos tbody .k-detail-row {
  background: #fff;
}

::ng-deep .gridProdutos tbody .k-detail-row:hover {
  background: #fff;
}

.k-button:disabled {
  pointer-events: auto;
}

.btn:disabled {
  pointer-events: auto;
}
.k-disabled {
  pointer-events: auto;
}


.card-sticky{
  width: 100%;
  box-shadow: none;
  position: sticky;
  top: 70px;
  z-index: 9;
  padding: 10px;
  background: #fff;
  border: solid 1px #f1f1f1;
  border-radius: 0px;
}
