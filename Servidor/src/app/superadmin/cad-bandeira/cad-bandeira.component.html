<kendo-dialog-titlebar (close)="fecheModal()"  >
  <h4 class="modal-title" id="myModalLabel" > {{bandeira.id ? 'Alterar' : "Nova" }} Bandeira  </h4>

</kendo-dialog-titlebar>

<div   class="modal-body">
  <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"  novalidate #frm="ngForm"
        (ngSubmit)="salveBandeira()">
    <div class="row">
      <div class="col-7">
        <div class="form-group mb-3">
          <label for="nome">Nome</label>
          <input type="text" class="form-control" autocomplete="off"
                 id="nome" name="nome" [(ngModel)]="bandeira.nome" #nome="ngModel"
                 placeholder="Nome da bandeira (visa, mastercard, etc)" value="" required appAutoFocus [autoFocus]="true">
          <div class="invalid-feedback">
            <p *ngIf="nome.errors?.required">Nome é obrigatório</p>
          </div>
        </div>
      </div>

      <div class="col-5">
        <div class="form-group mb-3   "  >
          <label  >Tipo</label><br>

          <kendo-dropdownlist class="form-control" name="tipo" required
                              [data]="tipos" #tipo="ngModel"
                              [filterable]="true"
                              [(ngModel)]="bandeira.tipo" >
          </kendo-dropdownlist>
          <div class="invalid-feedback">
            <p *ngIf="tipo.errors?.required">Tipo é obrigatório</p>
          </div>

        </div>
      </div>

      <div class="col-12">
        <label  >Icone bandeira</label><br>

        <kendo-upload id="foto" name="foto"
                      (select)="selecionouArquivo($event)"   (success)="successUpload($event)"
                      (error)="onErroUpload($event)"   [saveUrl]="uploadUrl"  required
                      [multiple]="false"
                      [autoUpload]="true"
                      [restrictions]="restricoes" >
          <kendo-upload-messages
            select="Selecione..."
            uploadSelectedFiles="Enviar imagem"
            clearSelectedFiles="Limpar">
          </kendo-upload-messages>

        </kendo-upload>
        <div class="invalid-feedback">
          <p *ngIf="!bandeira.imagem">Icone é obrigatório</p>
        </div>
        <div *ngIf=" this.bandeira.imagem || this.bandeira.imagemWeb">
          <app-bandeira-logo [bandeira]=" this.bandeira"></app-bandeira-logo>
        </div>
      </div>

    </div>

    <div class="alert alert-danger alert-dismissible fade show mt-2" *ngIf="mensagemErro" role="alert">
      <i class="mdi mdi-check-all mr-2"></i> {{mensagemErro}}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close" (click)="fecheMensagemErro()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="mt-2">
      <button type="submit" class="btn btn-primary waves-effect waves-light" [disabled]="salvando"  >

        Salvar</button>
      <button type="button" class="btn btn-light waves-effect"
              (click)="fecheModal()">Cancelar</button>
    </div>

  </form>
</div>
