import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {GruposDeLojasService} from "../../services/grupos-de-lojas.service";

@Component({
  selector: 'app-lista-grupo-lojas',
  templateUrl: './lista-grupo-lojas.component.html',
  styleUrls: ['./lista-grupo-lojas.component.scss']
})
export class ListaGrupoLojasComponent implements OnInit {
  gruposDeLojas: any;
  carregando = false;

  constructor(private router: Router, private gruposDeLojasService: GruposDeLojasService) { }

  ngOnInit(): void {
    this.carregando = true;
    this.gruposDeLojasService.listeGrupos().then( (grupos) => {
      this.carregando = false;
      this.gruposDeLojas = grupos;
    }).catch( erro => {
      this.carregando = false;
    });
  }

  novoGrupoDeLojas() {
    this.router.navigateByUrl( '/superadmin/grupos-de-lojas/novo');
  }

  editarGrupoDeLojas(grupoDeLojas: any) {
    this.router.navigateByUrl( '/superadmin/grupos-de-lojas/' + grupoDeLojas.id);
  }
}
