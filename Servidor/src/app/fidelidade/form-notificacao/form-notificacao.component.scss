.form-container {
  max-width: 1024px;
  margin: 0 auto;

  @media (max-width: 767.98px) {
    padding: 0;
  }
}

.form-header {
  display: flex;
  align-items: center;
  padding: 1.5rem 0rem;


  i {
    font-size: 2rem;
    margin-right: 1rem;
    color: #3498db;
  }

  h4 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #212529;
  }

  @media (max-width: 767.98px) {
    padding: 8px;
    margin-bottom: 8px;
  }
}

.card {
  border: 1px solid rgba(0,0,0,.125);
  box-shadow: 0 2px 4px rgba(0,0,0,.05);
  border-radius: 0.5rem;

  .card-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding: 0;
  }

  .card-body {
    padding: 1.5rem;
  }

  @media (max-width: 767.98px) {
    border: none;
    box-shadow: none;
    border-radius: 0;
    border-top: 1px solid #e9ecef;
    margin-bottom: 0;

    .card-title {
      margin-bottom: 1rem;
      font-size: 1rem;
      padding: 0;
    }

    .card-body {
      padding: 8px;
    }
  }
}

.config-card {
  .form-group {
    margin-bottom: 1rem;

    label {
      font-weight: 500;
      color: #495057;
      margin-bottom: 0.5rem;
    }

    small {
      color: #6c757d;
    }
  }

  @media (max-width: 767.98px) {
    margin-bottom: 8px;
  }
}

.message-editor {
  position: relative;

  .editor-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 1rem;

    @media (max-width: 767.98px) {
      margin-bottom: 8px;
      justify-content: flex-start;
    }
  }

  textarea {
    width: 100%;
    resize: vertical;
    min-height: 120px;
    padding: 8px;

    @media (max-width: 767.98px) {
      border: 1px solid #e9ecef;
    }
  }

  .message-footer {
    @media (max-width: 767.98px) {
      margin-top: 4px;
      padding: 0 8px;
    }
  }
}

.card-body {
  @media (max-width: 767.98px) {
    padding: 8px !important;
  }
}

.form-group {
  margin-bottom: 1rem;

  @media (max-width: 767.98px) {
    margin-bottom: 8px;
  }
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;

  @media (max-width: 767.98px) {
    margin-top: 1rem;
    padding: 8px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    position: sticky;
    bottom: 0;
    z-index: 1000;
  }
}

.input-group {
  .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
    color: #495057;
  }

  @media (max-width: 767.98px) {
    .form-control {
      height: 38px;
    }
  }
}

// Ajustes para os switches do Kendo
kendo-switch {
  margin-left: 1rem;

  @media (max-width: 767.98px) {
    margin-left: 8px;
  }
}

// Estilos para o Menu de Opções
.menu-templates {
  .template-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.75rem;
  }

  .btn-template {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.2s ease;
    color: #495057;

    i {
      font-size: 1.5rem;
      color: #25D366;
      margin-bottom: 0.5rem;
    }

    span {
      font-size: 0.9rem;
      font-weight: 500;
    }

    &:hover {
      background: #25D366;
      border-color: #25D366;
      color: white;
      transform: translateY(-2px);

      i {
        color: white;
      }
    }
  }
}

.menu-options {
  margin-bottom: 1.5rem;
}

.option-item {
  margin-bottom: 1rem;

  .option-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;

    &:hover {
      background-color: #f8f9fa;
    }

    .option-number {
      width: 28px;
      height: 28px;
      min-width: 28px;
      background: #25D366;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.875rem;
      font-weight: 600;
    }

    .option-text {
      flex: 0 1 auto;
      min-width: 100px;
      max-width: calc(100% - 150px);
      width: auto;
      font-weight: 500;
      color: #495057;
      border: none;
      background: transparent;
      padding: 0.5rem;
      font-size: 0.95rem;
      cursor: text;

      &:focus {
        outline: none;
        background: white;
      }
    }

    .option-status {
      position: absolute;
      right: 90px;
      top: 50%;
      transform: translateY(-50%);
      color: #ffc107;
      font-size: 1.1rem;
      display: flex;
      align-items: center;
      cursor: help;

      i {
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .option-actions {
      position: absolute;
      right: 0.75rem;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      gap: 0.5rem;
      background: white;
      padding-left: 0.5rem;

      .btn-link {
        padding: 0.25rem;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;

        i {
          transition: transform 0.2s ease;
          font-size: 0.875rem;

          &.expanded {
            transform: rotate(180deg);
          }
        }

        &:hover {
          color: #25D366;

          &.text-danger {
            color: #dc3545;
          }
        }
      }
    }

    input {
      flex: 1;
      border: none;
      padding: 0.5rem;
      font-size: 0.95rem;
      background: transparent;

      &:focus {
        outline: none;
      }
    }
  }

  .option-response {
    display: none;
    margin-top: 0.5rem;

    &.expanded {
      display: block;
    }

    .response-editor {
      .editor-header {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 0.5rem;
      }

      textarea {
        width: 100%;
        min-height: 120px;
        padding: 0.75rem;
        font-size: 0.95rem;
        line-height: 1.5;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        resize: vertical;
        background: white;

        &:focus {
          border-color: #25D366;
          box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.15);
        }
      }
    }
  }

  &.new-option {
    .option-header {
      border-style: dashed;
      border-color: #25D366;
      border-width: 2px;
      background-color: rgba(37, 211, 102, 0.03);
      position: relative;

      &:hover {
        background-color: rgba(37, 211, 102, 0.05);
      }

      .new-option-hint {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        opacity: 0.7;
        pointer-events: none;

        i {
          font-size: 1rem;
        }

        @media (max-width: 767.98px) {
          display: none;
        }
      }

      input {
        &::placeholder {
          color: #25D366;
          opacity: 0.7;
        }

        &:focus {
          &::placeholder {
            opacity: 0;
          }
        }
      }
    }
  }
}

.template-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;

  .template-chip {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;

    i {
      margin-right: 0.5rem;
      color: #25D366;
    }

    &:hover {
      background: #25D366;
      border-color: #25D366;
      color: white;

      i {
        color: white;
      }
    }
  }
}

.menu-title-input {
  margin-bottom: 1.5rem;

  input {
    border-left: 3px solid #25D366;
    font-size: 1rem;
    padding: 0.75rem;
  }
}

.option-text, textarea {
  &.is-invalid {
    border: 1px solid #dc3545;
    background-color: #fff8f8;
  }
}

.invalid-feedback {
  display: block;
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
