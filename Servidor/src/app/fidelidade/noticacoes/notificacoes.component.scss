.notifications-wrapper {
  min-height: calc(100vh - 60px);
  background-color: #fff;
  padding: 1.5rem;
}

.page-title-right {
  float: right;
  margin-bottom: 0.5rem;

  .breadcrumb {
    margin: 0;
    padding: 0;
    background: transparent;
    font-size: 0.875rem;
  }
}

.page-title {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  color: #212529;

  i {
    font-size: 1.25rem;
    margin-right: 0.5rem;
    vertical-align: -1px;
  }
}

.notifications-header {
  padding: 1.5rem 1.5rem 1rem;

  .d-flex {
    align-items: center;
  }

  .page-title {
    font-size: 1.75rem;
    color: #212529;
    margin: 0 0 0 1rem;
    font-weight: 600;
    line-height: 1;
  }

  i {
    font-size: 1.75rem;
    color: #212529;
    display: flex;
  }

  small {
    font-size: 0.9rem;
    color: #6c757d;
    display: block;
    margin-top: 0.25rem;
  }
}

.notifications-container {
  padding: 0 1.5rem;
  background: white;

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .k-i-loading {
      font-size: 3rem;
      color: #25D366;
    }
  }
}

.notifications-sidebar {
  background: #fff;
  border-right: 1px solid #e9ecef;
  height: 100%;
  padding: 1.5rem;

  .search-box {
    position: relative;
    margin-bottom: 1rem;
    
    i {
      position: absolute;
      left: 1rem;
      top: 50%;
      transform: translateY(-50%);
      color: #6c757d;
      font-size: 0.9rem;
      z-index: 2;
    }
    
    input {
      width: 100%;
      padding: 0.75rem 2.5rem 0.75rem 2.5rem;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      background: white;
      font-size: 0.85rem;
      transition: all 0.2s ease;
      
      &:focus {
        outline: none;
        border-color: #2196f3;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.15);
      }
      
      &::placeholder {
        color: #adb5bd;
      }
    }

    .clear-search-btn {
      position: absolute;
      right: 2rem;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      color: #6c757d;
      font-size: 0.9rem;
      z-index: 3;
      padding: 0.25rem;
      border-radius: 4px;
      transition: all 0.2s ease;
      
      &:hover {
        color: #495057;
        background-color: #f8f9fa;
      }
      
      &:active {
        transform: translateY(-50%) scale(0.95);
      }
    }
  }

  .status-filters {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .filter-title {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
      
      i {
        margin-right: 0.5rem;
        color: #6c757d;
        font-size: 0.9rem;
      }
      
      span {
        font-size: 0.85rem;
        font-weight: 600;
        color: #495057;
      }
    }

    .filter-buttons {
      display: flex;
      gap: 0.5rem;
      
      .filter-btn {
        flex: 1;
        padding: 0.5rem 0.75rem;
        border: 1px solid #e9ecef;
        background: #fafbfc;
        color: #6c757d;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        transition: all 0.2s ease;
        
        i {
          margin-right: 0.4rem;
          font-size: 0.75rem;
        }
        
        &:hover {
          background: #f8f9fa;
          border-color: #dee2e6;
          color: #495057;
        }
        
        &.active {
          background: #e3f2fd;
          border-color: #2196f3;
          color: #1976d2;
          font-weight: 600;
        }
      }
    }

    .search-indicator {
      margin-top: 0.75rem;
      padding: 0.5rem;
      background: #f8f9fa;
      border-radius: 6px;
      border-left: 3px solid #2196f3;
      
      i {
        margin-right: 0.5rem;
        font-size: 0.8rem;
      }
      
      small {
        font-size: 0.75rem;
        font-weight: 500;
      }
    }
  }

  // Legenda
  .status-legend {
    margin-top: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .legend-title {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
      
      i {
        margin-right: 0.5rem;
        color: #6c757d;
        font-size: 0.9rem;
      }
      
      span {
        font-size: 0.85rem;
        font-weight: 600;
        color: #495057;
      }
    }

    .legend-items {
      .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
        font-size: 0.8rem;
        color: #6c757d;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        i {
          margin-right: 0.5rem;
          font-size: 0.7rem;
          width: 12px;
        }
      }
    }
  }
}

.nav-section {
  margin-bottom: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
  background: white;
  border: 1px solid #e9ecef;

  &:last-child {
    margin-bottom: 0;
  }

  // Cores temáticas para cada categoria - versão minimalista
  &.fidelidade-section {    
    .nav-section-header {
      .section-title i {
        color: #f39c12;
      }
    }
  }

  &.pedidos-section {    
    .nav-section-header {
      .section-title i {
        color: #28a745;
      }
    }
  }

  &.sdr-section {
    .nav-section-header {
      .section-title i {
        color: #6f42c1;
      }
    }
  }

  &.entregas-section {
    border-left: 4px solid #dc3545;
    
    .nav-section-header {
      .section-title i {
        color: #dc3545;
      }
    }
  }

  &.templates-section {
    border-left: 4px solid #6f42c1;
    
    .nav-section-header {
      .section-title i {
        color: #6f42c1;
      }
    }
  }

  .nav-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem 1.25rem 1rem 1.25rem;
    margin-bottom: 0;
    background: #fafbfc;
    border-bottom: 1px solid #e9ecef;

    .section-title {
      display: flex;
      align-items: center;

      i {
        font-size: 1.3rem;
        margin-right: 0.75rem;
        width: 26px;
        text-align: center;
      }

      .title-content {
        display: flex;
        flex-direction: column;

        .category-name {
          font-size: 1rem;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 0.8px;
          line-height: 1.2;
          color: #212529;
        }

        .category-description {
          font-size: 0.75rem;
          font-weight: 500;
          margin-top: 0.2rem;
          font-style: italic;
          color: #6c757d;
        }
      }
    }

    .section-badges {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;

      .badge {
        font-size: 0.6    rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-weight: 600;
        text-align: center;
        width: 100%;
        box-sizing: border-box;

        &.badge-success {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }

        &.badge-danger {
          background: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        }
      }
    }
  }

  .nav-items {
    background: #ffffff;
    padding: 0.75rem 0.5rem 1rem 0.5rem;
    
    .nav-item {
      display: flex;
      align-items: center;
      padding: 0.875rem 0.75rem 0.875rem 1rem;
      color: #495057;
      border-radius: 6px;
      margin-bottom: 0.5rem;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid transparent;
      background: #ffffff;
      position: relative;
      border-left: 2px solid transparent;
      border-bottom: 1px solid #f1f3f4;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: #f8f9fa;
        border-color: #e9ecef;
        border-left-color: #dee2e6;
        border-bottom-color: #e9ecef;
        transform: translateX(1px);
      }

      &.active {
        background: rgba(53, 152, 219, 0.08);
        border-color: rgba(53, 152, 219, 0.15);
        border-left-color: #3498db;
        border-bottom-color: rgba(53, 152, 219, 0.2);
        color: #3498db;
        font-weight: 500;
        transform: translateX(2px);

        .notification-icon i {
          color: #3498db;
        }

        .notification-content {
          .notification-name {
            color: #3498db;
            font-weight: 600;
          }
        }
      }

      &.disabled-notification {
        background: #f8f9fa;
        border-color: #e9ecef;
        border-left-color: #e9ecef;
        border-bottom-color: #e9ecef;
        opacity: 0.75;
        
        &:hover {
          background: #e9ecef;
          border-left-color: #dee2e6;
          border-bottom-color: #dee2e6;
          transform: translateX(1px);
        }

        .notification-content {
          .notification-name {
            color: #6c757d;
          }
        }

        // Quando inativa está selecionada, mantém o estilo ativo
        &.active {
          background: rgba(53, 152, 219, 0.08);
          border-color: rgba(53, 152, 219, 0.15);
          border-left-color: #3498db;
          border-bottom-color: rgba(53, 152, 219, 0.2);
          color: #3498db;
          opacity: 1;
          transform: translateX(2px);

          .notification-icon i {
            color: #3498db;
          }

          .notification-content {
            .notification-name {
              color: #3498db;
              font-weight: 600;
            }
          }

          &:hover {
            background: rgba(53, 152, 219, 0.08);
            transform: translateX(2px);
          }
        }
      }

      &.template-item {
        background: rgba(111, 66, 193, 0.05);
        border-color: rgba(111, 66, 193, 0.15);

        &:hover {
          background: rgba(111, 66, 193, 0.1);
          border-color: rgba(111, 66, 193, 0.25);
        }

        &.active {
          background: rgba(111, 66, 193, 0.1);
          border-color: #6f42c1;
          color: #6f42c1;

          .notification-icon i {
            color: #6f42c1;
          }
        }
      }

      .notification-icon {
        width: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 1rem;
          transition: all 0.2s ease;
        }
      }

      .notification-content {
        flex: 1;
        margin-left: 0.75rem;

        .notification-name {
          font-size: 0.875rem;
          font-weight: 500;
          display: block;
          margin-bottom: 0.25rem;
          line-height: 1.4;
          color: #495057;
        }

        .notification-status {
          .status-badge {
            display: inline-flex;
            align-items: center;
            font-size: 0.65rem;
            padding: 0.1rem 0.35rem;
            border-radius: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;

            i {
              margin-right: 0.2rem;
              font-size: 0.55rem;
            }

            &.active {
              background: #d4edda;
              color: #155724;
              border: 1px solid #c3e6cb;

              i {
                color: #28a745;
              }
            }

            &.inactive {
              background: #f8d7da;
              color: #721c24;
              border: 1px solid #f5c6cb;

              i {
                color: #dc3545;
              }
            }
          }
        }
      }

      .notification-indicator {
        margin-left: 0.5rem;

        i {
          font-size: 0.8rem;
          color: #ffc107;
        }
      }
    }

    // Mensagem de lista vazia
    .empty-list-message {
      padding: 1.5rem 1rem;
      text-align: center;
      color: #6c757d;
      background: #f8f9fa;
      border-radius: 6px;
      margin: 0.5rem;
      margin-left: 0.75rem;

      i {
        font-size: 1.5rem;
        margin-bottom: 0.75rem;
        color: #adb5bd;
      }

      .empty-message-content {
        font-size: 0.8rem;
        line-height: 1.4;
        
        .btn {
          font-size: 0.75rem;
          padding: 0.4rem 0.8rem;
          
          i {
            margin-right: 0.3rem;
            font-size: 0.7rem;
          }
        }
      }
    }
  }
}

.notifications-content {
  background: white;
  padding: 1.5rem;
  min-height: 600px;
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;

  .notification-title {
    display: flex;
    align-items: center;

    i {
      font-size: 2rem;
      margin-right: 1rem;
      color: #3498db;
    }

    h4 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: #212529;
    }
  }

  .notification-actions {
    .btn {
      padding: 0.5rem 1rem;
      font-size: 0.9rem;

      i {
        margin-right: 0.5rem;
      }

      &.btn-outline-primary {
        border-color: #3498db;
        color: #3498db;

        &:hover {
          background-color: #3498db;
          color: white;
        }
      }

      &.btn-primary {
        background-color: #3498db;
        border-color: #3498db;

        &:hover {
          background-color: darken(#3498db, 5%);
        }
      }
    }
  }
}

// Ajustes para responsividade
@media (max-width: 768px) {
  .notifications-sidebar {
    border-right: none;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
  }

  .notifications-content {
    padding: 1rem;
  }

  .notification-header {
    flex-direction: column;
    align-items: flex-start;

    .notification-title {
      margin-bottom: 1rem;
    }
  }
}

// Media query específica para tablets grandes e desktops pequenos/médios (992px - 1400px)
@media (min-width: 992px) and (max-width: 1400px) {
  .notifications-sidebar {
    padding: 1rem;
    
    .search-box {
      margin-bottom: 0.75rem;
      
      input {
        padding: 0.5rem 2rem 0.5rem 2rem;
        font-size: 0.8rem;
      }
    }

    .status-filters {
      margin-bottom: 1rem;
      padding: 0.75rem;
      
      .filter-title {
        margin-bottom: 0.5rem;
        
        span {
          font-size: 0.8rem;
        }
      }
      
      .filter-buttons {
        gap: 0.25rem;
        
        .filter-btn {
          padding: 0.4rem 0.5rem;
          font-size: 0.75rem;
          
          i {
            margin-right: 0.25rem;
            font-size: 0.7rem;
          }
        }
      }
    }

    .status-legend {
      margin-top: 1.5rem;
      padding: 0.75rem;
      
      .legend-title {
        margin-bottom: 0.5rem;
        
        span {
          font-size: 0.8rem;
        }
      }
      
      .legend-items {
        .legend-item {
          font-size: 0.75rem;
          margin-bottom: 0.4rem;
        }
      }
    }
  }

  .nav-section {
    margin-bottom: 1rem;
    
    .nav-section-header {
      padding: 1rem 1rem 0.75rem 1rem;
      
      .section-title {
        i {
          font-size: 1.1rem;
          margin-right: 0.5rem;
          width: 22px;
        }
        
        .title-content {
          .category-name {
            font-size: 0.9rem;
            letter-spacing: 0.5px;
          }
          
          .category-description {
            font-size: 0.7rem;
            margin-top: 0.1rem;
          }
        }
      }
      
      .section-badges {
        gap: 0.15rem;
        
        .badge {
          font-size: 0.55rem;
          padding: 0.2rem 0.4rem;
          border-radius: 8px;
          
          // Layout mais compacto - badges em linha
          &.badge-success,
          &.badge-danger {
            white-space: nowrap;
            min-width: auto;
          }
        }
      }
    }

    .nav-items {
      padding: 0.5rem 0.25rem 0.75rem 0.25rem;
      
      .nav-item {
        padding: 0.7rem 0.5rem 0.7rem 0.75rem;
        margin-bottom: 0.4rem;
        
        .notification-icon {
          width: 20px;
          
          i {
            font-size: 0.9rem;
          }
        }
        
        .notification-content {
          margin-left: 0.5rem;
          
          .notification-name {
            font-size: 0.8rem;
            margin-bottom: 0.2rem;
            line-height: 1.3;
          }
          
          .notification-status {
            .status-badge {
              font-size: 0.6rem;
              padding: 0.08rem 0.3rem;
              border-radius: 8px;
              
              i {
                margin-right: 0.15rem;
                font-size: 0.5rem;
              }
            }
          }
        }
        
        .notification-indicator {
          margin-left: 0.3rem;
          
          i {
            font-size: 0.75rem;
          }
        }
      }
      
      .empty-list-message {
        padding: 1rem 0.75rem;
        margin: 0.25rem;
        margin-left: 0.5rem;
        
        i {
          font-size: 1.25rem;
          margin-bottom: 0.5rem;
        }
        
        .empty-message-content {
          font-size: 0.75rem;
          
          .btn {
            font-size: 0.7rem;
            padding: 0.35rem 0.7rem;
          }
        }
      }
    }
  }

  .search-summary {
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    
    .search-summary-header {
      margin-bottom: 0.5rem;
      
      span {
        font-size: 0.8rem;
      }
    }
    
    .search-summary-content {
      font-size: 0.75rem;
      
      .total-results,
      .no-filtered-results {
        margin-bottom: 0.4rem;
      }
      
      .results-breakdown {
        margin: 0.4rem 0;
        font-size: 0.7rem;
      }
      
      .action-buttons {
        margin-top: 0.5rem;
        
        .btn {
          font-size: 0.7rem;
          padding: 0.35rem 0.7rem;
        }
      }
    }
  }

  .no-results-global {
    margin-bottom: 0.75rem;
    padding: 1rem 0.75rem;
    
    .no-results-content {
      i {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
      }
      
      .no-results-text {
        strong {
          font-size: 0.85rem;
        }
        
        small {
          font-size: 0.7rem;
        }
      }
    }
  }

  .category-separator {
    margin: 1rem 0 0.75rem 0;
    padding: 0 0.75rem;
    
    span {
      font-size: 0.75rem;
    }
    
    .categories-indicator {
      margin-top: 0.2rem;
      font-size: 0.65rem;
    }
  }
}

@media (max-width: 767.98px) {
  .notifications-sidebar {
    min-height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 0;
  }

  .notifications-content {
    min-height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 0;
  }

  .notification-form {
    padding: 0;
  }

  .btn-light {
    padding: 8px 16px;
    font-size: 14px;
    margin: 8px;

    i {
      margin-right: 8px;
    }
  }

  .card-body {
    padding: 0;
  }

  .notification-header {
    padding: 8px;
    margin-bottom: 8px;
  }

  .nav-section {
    margin-bottom: 8px;
  }

  .search-box {
    margin: 8px;
  }

  .page-title {
    margin: 8px;
  }

  .page-title-right {
    margin: 8px;
  }
}

.templates-content {
  .notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;

    .notification-title {
      display: flex;
      align-items: center;
      gap: 1rem;

      i {
        font-size: 1.5rem;
        color: #3498db;
      }

      h4 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #212529;
      }
    }

    .notification-actions {
      .btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
      }
    }

    @media (max-width: 767.98px) {
      padding: 8px;
      margin-bottom: 8px;
    }
  }
}

// Resumo global de busca
.search-summary {
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  border-left: 4px solid #2196f3;

  .search-summary-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    
    i {
      margin-right: 0.5rem;
      color: #2196f3;
      font-size: 0.9rem;
    }
    
    span {
      font-size: 0.85rem;
      font-weight: 600;
      color: #495057;
    }
  }

  .search-summary-content {
    font-size: 0.8rem;
    color: #6c757d;
    
    .total-results,
    .no-filtered-results {
      margin-bottom: 0.5rem;
      
      strong {
        color: #495057;
      }
    }
    
    .available-results {
      font-size: 0.75rem;
      color: #6c757d;
      font-style: italic;
    }
    
    .results-breakdown {
      margin: 0.5rem 0;
      font-size: 0.75rem;
      color: #6c757d;
    }
    
    .action-buttons {
      margin-top: 0.75rem;
      
      .btn {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
        border-radius: 6px;
        
        i {
          margin-right: 0.3rem;
          font-size: 0.7rem;
        }
      }
    }
  }
}

// Mensagem global de nenhum resultado
.no-results-global {
  margin-bottom: 1rem;
  padding: 1.5rem 1rem;
  text-align: center;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  .no-results-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    i {
      font-size: 2rem;
      margin-bottom: 0.75rem;
      color: #adb5bd;
    }
    
    .no-results-text {
      strong {
        display: block;
        font-size: 0.9rem;
        color: #495057;
        margin-bottom: 0.25rem;
      }
      
      small {
        font-size: 0.75rem;
        color: #6c757d;
      }
    }
  }
}

// Separador de categorias
.category-separator {
  margin: 1.5rem 0 1rem 0;
  padding: 0 1rem;
  text-align: left;

  span {
    font-size: 0.8rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    padding-right: 1rem;
  }

  &.templates-separator {
    margin-top: 2rem;
  }

  .categories-indicator {
    display: block;
    margin-top: 0.3rem;
    font-size: 0.7rem;
    color: #6c757d;
    font-weight: normal;
    text-transform: none;
    letter-spacing: 0;
    font-style: italic;

    &:after {
      display: none;
    }
  }
}
