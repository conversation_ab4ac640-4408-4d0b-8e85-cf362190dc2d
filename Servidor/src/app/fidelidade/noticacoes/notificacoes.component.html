<div class="page-title-right d-none d-md-block">
  <ol class="breadcrumb m-0">
    <li class="breadcrumb-item active">Fidelidade</li>
  </ol>
</div>

<h4 class="page-title"><i class="fab fa-whatsapp"></i> Configurações de Notificações</h4>

<div class="row g-0">
  <div class="col-12">
    <div class="card">
      <div class="card-body p-0">
        <div class="loading-overlay" *ngIf="loading">
          <div class="k-i-loading"></div>
        </div>

        <div class="row g-0" *ngIf="!loading">
          <!-- Menu Lateral -->
          <div class="col-md-3 col-lg-4 col-xl-3 notifications-sidebar" [class.d-none]="exibindoFormMobile" [class.d-md-block]="exibindoFormMobile">
            <!-- Filtros de Status -->
            <div class="status-filters">
              <div class="filter-title">
                <i class="fas fa-filter"></i>
                <span>Filtrar por Status</span>
              </div>
              <div class="filter-buttons">
                <button class="btn btn-sm filter-btn"
                        [class.active]="filtroAtivo === 'all'"
                        (click)="aplicarFiltroStatus('all')">
                  <i class="fas fa-list"></i>
                  Todas
                </button>
                <button class="btn btn-sm filter-btn"
                        [class.active]="filtroAtivo === 'active'"
                        (click)="aplicarFiltroStatus('active')">
                  <i class="fas fa-bell text-success"></i>
                  Ativas
                </button>
                <button class="btn btn-sm filter-btn"
                        [class.active]="filtroAtivo === 'inactive'"
                        (click)="aplicarFiltroStatus('inactive')">
                  <i class="fas fa-bell-slash text-danger"></i>
                  Inativas
                </button>
              </div>
            </div>

            <!-- Busca -->
            <div class="search-box">
              <i class="fas fa-search"></i>
              <input type="text" class="form-control" placeholder="Buscar notificação..." [(ngModel)]="termoBusca" (input)="aplicarBusca()">
              <div class="clear-search-btn" (click)="limparBusca()" *ngIf="termoBusca">
                <i class="fas fa-times"></i>
              </div>
            </div>


            <!-- Navegação -->
            <div class="notifications-nav">
              <!-- Separador de Categorias -->
              <div class="category-separator">
                <span>Tipos de Notificações</span>
                <small *ngIf="termoBusca" class="categories-indicator">
                  Mostrando {{contarCategoriasVisiveis()}} de {{contarCategoriasTotal()}} categorias
                </small>
              </div>

              <!-- Resumo Global de Busca -->
              <div class="search-summary" *ngIf="termoBusca && temResultadosOcultosPorFiltro()">
                <div class="search-summary-header">
                  <i class="fas fa-search-plus"></i>
                  <span>Resultados da busca</span>
                </div>
                <div class="search-summary-content">

                  <!-- Quando filtro mostra resultados -->
                  <div *ngIf="contarResultadosBuscaFiltrados() > 0" class="total-results">
                    <strong>{{contarResultadosBuscaFiltrados()}}</strong>
                    <span *ngIf="filtroAtivo === 'all'"> notificação(ões) encontrada(s)</span>
                    <span *ngIf="filtroAtivo === 'active'"> notificação(ões) ativa(s) encontrada(s)</span>
                    <span *ngIf="filtroAtivo === 'inactive'"> notificação(ões) inativa(s) encontrada(s)</span>
                    para "{{termoBusca}}"
                  </div>

                  <!-- Quando filtro não mostra resultados mas existem outros -->
                  <div *ngIf="contarResultadosBuscaFiltrados() === 0 && temResultadosOcultosPorFiltro()" class="no-filtered-results">
                    <strong>0 notificações {{filtroAtivo === 'active' ? 'ativas' : 'inativas'}} encontradas</strong> para "{{termoBusca}}"
                    <div class="available-results">
                      ({{contarResultadosBuscaTotal()}} notificação(ões) {{obterStatusOculto()}}(s) disponível(is))
                    </div>
                  </div>

                  <!-- Breakdown por categoria quando há resultados -->
                  <div class="results-breakdown" *ngIf="contarResultadosBuscaFiltrados() > 0">
                    {{obterResumoResultadosBusca()}}
                  </div>

                  <!-- Botão para ver todos quando há resultados ocultos -->
                  <div class="action-buttons" *ngIf="temResultadosOcultosPorFiltro()">
                    <button class="btn btn-primary btn-sm" (click)="verTodosResultados()">
                      <i class="fas fa-eye"></i>
                      Ver todas as {{contarResultadosBuscaTotal()}} notificações encontradas
                    </button>
                  </div>
                </div>
              </div>

              <!-- Mensagem quando busca não encontra nada -->
              <div class="no-results-global" *ngIf="termoBusca && contarResultadosBuscaTotal() === 0">
                <div class="no-results-content">
                  <i class="fas fa-search text-muted"></i>
                  <div class="no-results-text">
                    <strong>Nenhuma notificação encontrada para "{{termoBusca}}"</strong>
                    <small>Verifique a ortografia ou tente termos mais específicos</small>
                  </div>
                </div>
              </div>



              <!-- Pedidos -->
              <div class="nav-section pedidos-section" *ngIf="temModuloPedidos && (notificacoesPedidos.length > 0 || !termoBusca)">
                <div class="nav-section-header">
                  <div class="section-title">
                    <div class="title-content">
                      <span class="category-name">Pedidos</span>
                      <small class="category-description">Fluxo de pedidos e pagamentos</small>
                    </div>
                  </div>
                  <div class="section-badges">
                    <div class="badge badge-success"
                         [title]="'Notificações ativas: ' + obterContadoresSecao('pedidos').ativas">
                      {{obterContadoresSecao('pedidos').ativas}} ativas
                    </div>
                    <div class="badge badge-danger"
                         [title]="'Notificações inativas: ' + obterContadoresSecao('pedidos').inativas">
                      {{obterContadoresSecao('pedidos').inativas}} inativas
                    </div>
                  </div>
                </div>
                <div class="nav-items">
                  <a class="nav-item" *ngFor="let notificacao of notificacoesPedidos"
                     [class.active]="notificacaoSelecionada === notificacao"
                     [class.disabled-notification]="!notificacao.ativada"
                     [title]="getTooltipNotificacao(notificacao)"
                     (click)="selecionarNotificacao(notificacao)">
                    <div class="notification-icon">
                      <i [class]="getIconeNotificacao(notificacao.tipoDeNotificacao)"
                         [class.text-success]="notificacao.ativada"
                         [class.text-muted]="!notificacao.ativada"></i>
                    </div>
                    <div class="notification-content">
                      <span class="notification-name">{{notificacao.tipoDeNotificacao}}</span>
                      <div class="notification-status">
                        <span class="status-badge" [class.active]="notificacao.ativada" [class.inactive]="!notificacao.ativada">
                          <i class="fas fa-circle"></i>
                          {{notificacao.ativada ? 'Ativa' : 'Inativa'}}
                        </span>
                      </div>
                    </div>
                  </a>

                  <!-- Mensagem quando lista vazia -->
                  <div class="empty-list-message" *ngIf="notificacoesPedidos.length === 0">
                    <i class="fas fa-inbox text-muted"></i>
                    <div class="empty-message-content">
                      <span>{{obterMensagemListaVazia('pedidos')}}</span>

                      <!-- Botão para ver todos os resultados quando há filtro + busca -->
                      <button *ngIf="termoBusca && filtroAtivo !== 'all'"
                              class="btn btn-sm btn-outline-primary mt-2"
                              (click)="verTodosResultados()">
                        <i class="fas fa-eye"></i>
                        Ver todas as notificações para "{{termoBusca}}"
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Fidelidade -->
              <div class="nav-section fidelidade-section" *ngIf="temModuloFidelidade && (notificacoesFidelidade.length > 0 || !termoBusca)">
                <div class="nav-section-header">
                  <div class="section-title">
                    <div class="title-content">
                      <span class="category-name">Fidelidade</span>
                      <small class="category-description">Programa de pontos e cartões</small>
                    </div>
                  </div>
                  <div class="section-badges">
                    <div class="badge badge-success"
                         [title]="'Notificações ativas: ' + obterContadoresSecao('fidelidade').ativas">
                      {{obterContadoresSecao('fidelidade').ativas}} ativas
                    </div>
                    <div class="badge badge-danger"
                         [title]="'Notificações inativas: ' + obterContadoresSecao('fidelidade').inativas">
                      {{obterContadoresSecao('fidelidade').inativas}} inativas
                    </div>
                  </div>
                </div>
                <div class="nav-items">
                  <a class="nav-item" *ngFor="let notificacao of notificacoesFidelidade"
                     [class.active]="notificacaoSelecionada === notificacao"
                     [class.disabled-notification]="!notificacao.ativada"
                     [title]="getTooltipNotificacao(notificacao)"
                     (click)="selecionarNotificacao(notificacao)">
                    <div class="notification-icon">
                      <i [class]="getIconeNotificacao(notificacao.tipoDeNotificacao)"
                         [class.text-success]="notificacao.ativada"
                         [class.text-muted]="!notificacao.ativada"></i>
                    </div>
                    <div class="notification-content">
                      <span class="notification-name">{{notificacao.tipoDeNotificacao}}</span>
                      <div class="notification-status">
                        <span class="status-badge" [class.active]="notificacao.ativada" [class.inactive]="!notificacao.ativada">
                          <i class="fas fa-circle"></i>
                          {{notificacao.ativada ? 'Ativa' : 'Inativa'}}
                        </span>
                      </div>
                    </div>
                    <div class="notification-indicator" *ngIf="!notificacao.ativada">
                      <i class="fas fa-exclamation-triangle text-warning"></i>
                    </div>
                  </a>

                  <!-- Mensagem quando lista vazia -->
                  <div class="empty-list-message" *ngIf="notificacoesFidelidade.length === 0">
                    <i class="fas fa-inbox text-muted"></i>
                    <div class="empty-message-content">
                      <span>{{obterMensagemListaVazia('fidelidade')}}</span>

                      <!-- Botão para ver todos os resultados quando há filtro + busca -->
                      <button *ngIf="termoBusca && filtroAtivo !== 'all'"
                              class="btn btn-sm btn-outline-primary mt-2"
                              (click)="verTodosResultados()">
                        <i class="fas fa-eye"></i>
                        Ver todas as notificações para "{{termoBusca}}"
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- SDR -->
              <div class="nav-section sdr-section" *ngIf="notificacoesSDR.length > 0">
                <div class="nav-section-header">
                  <div class="section-title">
                    <div class="title-content">
                      <span class="category-name">SDR</span>
                      <small class="category-description">Etapas de relacionamento</small>
                    </div>
                  </div>
                  <div class="section-badges">
                    <div class="badge badge-success"
                         [title]="'Notificações ativas: ' + obterContadoresSecao('sdr').ativas">
                      {{obterContadoresSecao('sdr').ativas}} ativas
                    </div>
                    <div class="badge badge-danger"
                         [title]="'Notificações inativas: ' + obterContadoresSecao('sdr').inativas">
                      {{obterContadoresSecao('sdr').inativas}} inativas
                    </div>
                  </div>
                </div>
                <div class="nav-items">
                  <a class="nav-item" *ngFor="let notificacao of notificacoesSDR"
                     [class.active]="notificacaoSelecionada === notificacao"
                     [class.disabled-notification]="!notificacao.ativada"
                     [title]="getTooltipNotificacao(notificacao)"
                     (click)="selecionarNotificacao(notificacao)">
                    <div class="notification-icon">
                      <i [class]="getIconeNotificacao(notificacao.tipoDeNotificacao)"
                         [class.text-success]="notificacao.ativada"
                         [class.text-muted]="!notificacao.ativada"></i>
                    </div>
                    <div class="notification-content">
                      <span class="notification-name">{{notificacao.tipoDeNotificacao}}</span>
                      <div class="notification-status">
                        <span class="status-badge" [class.active]="notificacao.ativada" [class.inactive]="!notificacao.ativada">
                          <i class="fas fa-circle"></i>
                          {{notificacao.ativada ? 'Ativa' : 'Inativa'}}
                        </span>
                      </div>
                    </div>
                    <div class="notification-indicator" *ngIf="!notificacao.ativada">
                      <i class="fas fa-exclamation-triangle text-warning"></i>
                    </div>
                  </a>

                  <!-- Mensagem quando lista vazia -->
                  <div class="empty-list-message" *ngIf="notificacoesSDR.length === 0">
                    <i class="fas fa-inbox text-muted"></i>
                    <div class="empty-message-content">
                      <span>{{obterMensagemListaVazia('SDR')}}</span>

                      <!-- Botão para ver todos os resultados quando há filtro + busca -->
                      <button *ngIf="termoBusca && filtroAtivo !== 'all'"
                              class="btn btn-sm btn-outline-primary mt-2"
                              (click)="verTodosResultados()">
                        <i class="fas fa-eye"></i>
                        Ver todas as notificações para "{{termoBusca}}"
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Entregas -->
              <div class="nav-section entregas-section" *ngIf="notificacoesEntregas.length > 0">
                <div class="nav-section-header">
                  <div class="section-title">
                    <div class="title-content">
                      <span class="category-name">Entregas</span>
                      <small class="category-description">Entregadores e logística</small>
                    </div>
                  </div>
                  <div class="section-badges">
                    <div class="badge badge-success"
                         [title]="'Notificações ativas: ' + obterContadoresSecao('entregas').ativas">
                      {{obterContadoresSecao('entregas').ativas}} ativas
                    </div>
                    <div class="badge badge-danger"
                         [title]="'Notificações inativas: ' + obterContadoresSecao('entregas').inativas">
                      {{obterContadoresSecao('entregas').inativas}} inativas
                    </div>
                  </div>
                </div>
                <div class="nav-items">
                  <a class="nav-item" *ngFor="let notificacao of notificacoesEntregas"
                     [class.active]="notificacaoSelecionada === notificacao"
                     [class.disabled-notification]="!notificacao.ativada"
                     [title]="getTooltipNotificacao(notificacao)"
                     (click)="selecionarNotificacao(notificacao)">
                    <div class="notification-icon">
                      <i [class]="getIconeNotificacao(notificacao.tipoDeNotificacao)"
                         [class.text-success]="notificacao.ativada"
                         [class.text-muted]="!notificacao.ativada"></i>
                    </div>
                    <div class="notification-content">
                      <span class="notification-name">{{notificacao.tipoDeNotificacao}}</span>
                      <div class="notification-status">
                        <span class="status-badge" [class.active]="notificacao.ativada" [class.inactive]="!notificacao.ativada">
                          <i class="fas fa-circle"></i>
                          {{notificacao.ativada ? 'Ativa' : 'Inativa'}}
                        </span>
                      </div>
                    </div>
                    <div class="notification-indicator" *ngIf="!notificacao.ativada">
                      <i class="fas fa-exclamation-triangle text-warning"></i>
                    </div>
                  </a>

                  <!-- Mensagem quando lista vazia -->
                  <div class="empty-list-message" *ngIf="notificacoesEntregas.length === 0">
                    <i class="fas fa-inbox text-muted"></i>
                    <div class="empty-message-content">
                      <span>{{obterMensagemListaVazia('entregas')}}</span>

                      <!-- Botão para ver todos os resultados quando há filtro + busca -->
                      <button *ngIf="termoBusca && filtroAtivo !== 'all'"
                              class="btn btn-sm btn-outline-primary mt-2"
                              (click)="verTodosResultados()">
                        <i class="fas fa-eye"></i>
                        Ver todas as notificações para "{{termoBusca}}"
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Separador para Templates -->
              <div class="category-separator templates-separator" *ngIf="temTemplatesDisponiveis()">
                <span>Ferramentas</span>
              </div>

              <!-- Templates -->
              <div class="nav-section templates-section" *ngIf="temTemplatesDisponiveis()">
                <div class="nav-section-header">
                  <div class="section-title">
                    <div class="title-content">
                      <span class="category-name">Templates</span>
                      <small class="category-description">Modelos de mensagem</small>
                    </div>
                  </div>
                </div>
                <div class="nav-items">
                  <a class="nav-item template-item" [class.active]="mostrandoTemplates"
                     (click)="mostrarTemplates()">
                    <div class="notification-icon">
                      <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="notification-content">
                      <span class="notification-name">Templates de Mensagem</span>
                    </div>
                  </a>
                </div>
              </div>
            </div>

            <!-- Legenda -->
            <div class="status-legend">
              <div class="legend-title">
                <i class="fas fa-info-circle"></i>
                <span>Legenda</span>
              </div>
              <div class="legend-items">
                <div class="legend-item">
                  <i class="fas fa-circle text-success"></i>
                  <span>Notificação ativa</span>
                </div>
                <div class="legend-item">
                  <i class="fas fa-circle text-muted"></i>
                  <span>Notificação inativa</span>
                </div>
                <div class="legend-item">
                  <i class="fas fa-exclamation-triangle text-warning"></i>
                  <span>Requer atenção</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Área de Conteúdo -->
          <div class="col-md-9 col-lg-8 col-xl-9 notifications-content p-0" [class.d-none]="!exibindoFormMobile && isMobile" [class.d-md-block]="true">
            <!-- Botão Voltar Mobile -->
            <div class="d-md-none" *ngIf="exibindoFormMobile">
              <button class="btn btn-light" (click)="voltarParaLista()">
                <i class="fas fa-arrow-left"></i> Voltar
              </button>
            </div>

            <!-- Conteúdo de Notificação -->
            <div *ngIf="!mostrandoTemplates" class="notification-form" #formContent>
              <!-- Título do Form -->


              <app-form-notificacao *ngIf="notificacaoSelecionada"
                [notificacao]="notificacaoSelecionada"
                [icone]="getIconeNotificacao(notificacaoSelecionada.tipoDeNotificacao)">
              </app-form-notificacao>
            </div>

            <!-- Conteúdo de Templates -->
            <div *ngIf="mostrandoTemplates" class="templates-content">
              <div class="notification-header">
                <div class="notification-title">
                  <i class="fas fa-layer-group"></i>
                  <h4>Templates de Mensagem</h4>
                </div>
                <div class="notification-actions">
                  <button class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Novo Template
                  </button>
                </div>
              </div>
              <app-templates-de-mensagem></app-templates-de-mensagem>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
