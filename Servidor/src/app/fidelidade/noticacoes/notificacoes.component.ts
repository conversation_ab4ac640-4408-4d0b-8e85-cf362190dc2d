import { Component, OnInit, ViewChild, ElementRef, HostListener } from '@angular/core';
import { NotificacoesService } from '../../services/notificacoes.service';
import { ConstantsService } from '../ConstantsService';

// Constantes para evitar strings repetidas
const FILTROS = {
  TODOS: 'all',
  ATIVAS: 'active',
  INATIVAS: 'inactive'
};

const CATEGORIAS = {
  FIDELIDADE: 'fidelidade',
  PEDIDOS: 'pedidos',
  SDR: 'sdr',
  ENTREGAS: 'entregas'
};

@Component({
  selector: 'app-notificacoes',
  templateUrl: './notificacoes.component.html',
  styleUrls: ['./notificacoes.component.scss']
})
export class NotificacoesComponent implements OnInit {
  @ViewChild('formContent') formContent: ElementRef;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.isMobile = window.innerWidth <= 767.98;
  }

  // Estado da aplicação
  loading = false;
  notificacaoSelecionada: any = null;
  mostrandoTemplates = false;
  termoBusca = '';
  exibindoFormMobile = false;
  isMobile = window.innerWidth <= 767.98;

  // Arrays de notificações por tipo (filtrados)
  notificacoesFidelidade = [];
  notificacoesPedidos = [];
  notificacoesSDR = [];
  notificacoesEntregas = [];

  // Arrays originais (para busca e filtros)
  private _notificacoesFidelidadeOriginal = [];
  private _notificacoesPedidosOriginal = [];
  private _notificacoesSDROriginal = [];
  private _notificacoesEntregasOriginal = [];

  // Configurações de módulos
  temModuloPedidos = false;
  temModuloFidelidade = false;

  // Filtros ativos
  filtroAtivo = FILTROS.TODOS;

  constructor(
    private notificacoesService: NotificacoesService,
    private constantsService: ConstantsService
  ) { }

  ngOnInit() {
    this.loading = true;
    this.carregarConfiguracoes();
    this.carregarNotificacoes();
  }

  // ===========================================
  // MÉTODOS DE INICIALIZAÇÃO
  // ===========================================

  private carregarConfiguracoes() {
    this.constantsService.moduloPedido$.subscribe(data => this.temModuloPedidos = data);
    this.constantsService.moduloFidelidade$.subscribe(data => this.temModuloFidelidade = data);
  }

  private carregarNotificacoes() {
    this.notificacoesService.todas().then((notificacoes) => {
      this.loading = false;
      this.separarNotificacoesPorTipo(notificacoes);
      this.selecionarPrimeiraNotificacao();
    });
  }

  private separarNotificacoesPorTipo(notificacoes: any[]) {
    // Entregas
    this.notificacoesEntregas = this.filtreRemovendo(notificacoes,
      notificacao => notificacao.tipoDeNotificacao.indexOf('Entregador') >= 0);
    this._notificacoesEntregasOriginal = [...this.notificacoesEntregas];

    // Remove Carrinho Abandonado
    this.filtreRemovendo(notificacoes,
      notificacao => notificacao.tipoDeNotificacao === 'Carrinho Abandonado');

    // Pedidos
    this.notificacoesPedidos = this.filtreRemovendo(notificacoes,
      notificacao => notificacao.tipoDeNotificacao.indexOf('Pedido') >= 0 ||
        notificacao.tipoDeNotificacao.indexOf('Pagamento') >= 0 ||
        notificacao.tipoDeNotificacao.indexOf('Comanda') >= 0);
    this._notificacoesPedidosOriginal = [...this.notificacoesPedidos];

    // SDR
    this.notificacoesSDR = this.filtreRemovendo(notificacoes,
      notificacao => notificacao.tipoDeNotificacao.indexOf('Etapa ') >= 0);
    this._notificacoesSDROriginal = [...this.notificacoesSDR];

    // Fidelidade (restante)
    this.notificacoesFidelidade = notificacoes;
    this._notificacoesFidelidadeOriginal = [...this.notificacoesFidelidade];
  }

  private selecionarPrimeiraNotificacao() {
    if (this.notificacoesFidelidade.length > 0) {
      this.notificacaoSelecionada = this.notificacoesFidelidade[0];
    }
  }

  // ===========================================
  // MÉTODOS DE INTERAÇÃO
  // ===========================================

  selecionarNotificacao(notificacao: any) {
    this.notificacaoSelecionada = notificacao;
    this.mostrandoTemplates = false;
    this.exibindoFormMobile = true;

    setTimeout(() => {
      if (this.formContent) {
        const headerOffset = 100;
        const elementPosition = this.formContent.nativeElement.getBoundingClientRect().top;
        const offsetPosition = elementPosition - headerOffset;

        window.scrollTo({
          top: window.pageYOffset + offsetPosition,
          behavior: 'smooth'
        });
      }
    }, 100);
  }

  voltarParaLista() {
    this.exibindoFormMobile = false;
  }

  mostrarTemplates() {
    this.mostrandoTemplates = true;
    this.notificacaoSelecionada = null;
    this.exibindoFormMobile = true;

    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  // ===========================================
  // MÉTODOS DE FILTRO E BUSCA
  // ===========================================

  aplicarFiltroStatus(filtro: string) {
    this.filtroAtivo = filtro;
    this.aplicarFiltrosEmTodasSecoes();
  }

  aplicarBusca() {
    this.aplicarFiltrosEmTodasSecoes();
  }

  limparBusca() {
    this.termoBusca = '';
    this.aplicarFiltrosEmTodasSecoes();
  }

  verTodosResultados() {
    this.aplicarFiltroStatus(FILTROS.TODOS);
  }

  private aplicarFiltrosEmTodasSecoes() {
    this.filtrarPorStatus(this._notificacoesFidelidadeOriginal, CATEGORIAS.FIDELIDADE);
    this.filtrarPorStatus(this._notificacoesPedidosOriginal, CATEGORIAS.PEDIDOS);
    this.filtrarPorStatus(this._notificacoesSDROriginal, CATEGORIAS.SDR);
    this.filtrarPorStatus(this._notificacoesEntregasOriginal, CATEGORIAS.ENTREGAS);
  }

  private filtrarPorStatus(arrayOriginal: any[], categoria: string) {
    let arrayFiltrado = [...arrayOriginal];

    // Aplica filtro de status
    if (this.filtroAtivo === FILTROS.ATIVAS) {
      arrayFiltrado = arrayFiltrado.filter(n => n.ativada);
    } else if (this.filtroAtivo === FILTROS.INATIVAS) {
      arrayFiltrado = arrayFiltrado.filter(n => !n.ativada);
    }

    // Aplica filtro de busca
    if (this.termoBusca) {
      const termo = this.termoBusca.toLowerCase();
      arrayFiltrado = arrayFiltrado.filter(n =>
        n.tipoDeNotificacao.toLowerCase().includes(termo)
      );
    }

    // Atualiza array correspondente
    this.atualizarArrayPorCategoria(categoria, arrayFiltrado);
  }

  private atualizarArrayPorCategoria(categoria: string, array: any[]) {
    switch (categoria) {
      case CATEGORIAS.FIDELIDADE:
        this.notificacoesFidelidade = array;
        break;
      case CATEGORIAS.PEDIDOS:
        this.notificacoesPedidos = array;
        break;
      case CATEGORIAS.SDR:
        this.notificacoesSDR = array;
        break;
      case CATEGORIAS.ENTREGAS:
        this.notificacoesEntregas = array;
        break;
    }
  }

  // ===========================================
  // MÉTODOS DE CONTAGEM (CONSOLIDADOS)
  // ===========================================

  private contarNotificacoesPorStatus(notificacoes: any[], ativas: boolean): number {
    return notificacoes.filter(n => n.ativada === ativas).length;
  }

  obterContadoresSecao(categoria: string): { ativas: number, inativas: number } {
    const notificacoes = this.obterArrayOriginalPorCategoria(categoria);
    return {
      ativas: this.contarNotificacoesPorStatus(notificacoes, true),
      inativas: this.contarNotificacoesPorStatus(notificacoes, false)
    };
  }

  private obterArrayOriginalPorCategoria(categoria: string): any[] {
    switch (categoria) {
      case CATEGORIAS.FIDELIDADE:
        return this._notificacoesFidelidadeOriginal;
      case CATEGORIAS.PEDIDOS:
        return this._notificacoesPedidosOriginal;
      case CATEGORIAS.SDR:
        return this._notificacoesSDROriginal;
      case CATEGORIAS.ENTREGAS:
        return this._notificacoesEntregasOriginal;
      default:
        return [];
    }
  }

  // ===========================================
  // MÉTODOS DE BUSCA E RESULTADOS
  // ===========================================

  contarResultadosBuscaFiltrados(): number {
    if (!this.termoBusca) return 0;

    return this.notificacoesFidelidade.length +
           this.notificacoesPedidos.length +
           this.notificacoesSDR.length +
           this.notificacoesEntregas.length;
  }

  contarResultadosBuscaTotal(): number {
    if (!this.termoBusca) return 0;

    const termo = this.termoBusca.toLowerCase();
    let total = 0;

    [this._notificacoesFidelidadeOriginal, this._notificacoesPedidosOriginal,
     this._notificacoesSDROriginal, this._notificacoesEntregasOriginal]
    .forEach(array => {
      total += array.filter(n =>
        n.tipoDeNotificacao.toLowerCase().includes(termo)
      ).length;
    });

    return total;
  }

  temResultadosOcultosPorFiltro(): boolean {
    return this.contarResultadosBuscaTotal() > this.contarResultadosBuscaFiltrados();
  }

  obterStatusOculto(): string {
    if (this.filtroAtivo === FILTROS.ATIVAS) return 'inativa';
    if (this.filtroAtivo === FILTROS.INATIVAS) return 'ativa';
    return '';
  }

  obterResumoResultadosBusca(): string {
    if (!this.termoBusca) return '';

    const termo = this.termoBusca.toLowerCase();
    const categorias = [];

    const categoriasConfig = [
      { array: this._notificacoesFidelidadeOriginal, nome: 'Fidelidade' },
      { array: this._notificacoesPedidosOriginal, nome: 'Pedidos' },
      { array: this._notificacoesSDROriginal, nome: 'SDR' },
      { array: this._notificacoesEntregasOriginal, nome: 'Entregas' }
    ];

    categoriasConfig.forEach(config => {
      const count = config.array.filter(n =>
        n.tipoDeNotificacao.toLowerCase().includes(termo)
      ).length;
      if (count > 0) {
        categorias.push(`${config.nome} (${count})`);
      }
    });

    return categorias.length === 0
      ? 'Nenhum resultado encontrado'
      : `Encontrado em: ${categorias.join(', ')}`;
  }

  // ===========================================
  // MÉTODOS DE CATEGORIAS
  // ===========================================

  contarCategoriasVisiveis(): number {
    let count = 0;

    if (this.temModuloFidelidade && this.notificacoesFidelidade.length > 0) count++;
    if (this.temModuloPedidos && this.notificacoesPedidos.length > 0) count++;
    if (this.notificacoesSDR.length > 0) count++;
    if (this.notificacoesEntregas.length > 0) count++;

    return count;
  }

  contarCategoriasTotal(): number {
    let count = 0;

    if (this.temModuloFidelidade) count++;
    if (this.temModuloPedidos) count++;
    if (this._notificacoesSDROriginal.length > 0) count++;
    count++; // Entregas sempre disponível

    return count;
  }

  temTemplatesDisponiveis(): boolean {
    return true;
  }

  // ===========================================
  // MÉTODOS DE UTILIDADE
  // ===========================================

  getTooltipNotificacao(notificacao: any): string {
    const status = notificacao.ativada ? 'Ativa' : 'Inativa';
    const descricao = notificacao.ativada
      ? 'Esta notificação está funcionando normalmente'
      : 'Esta notificação está desativada e não será enviada';

    return `${notificacao.tipoDeNotificacao} - Status: ${status}\n${descricao}`;
  }

  // Método para gerar mensagens de lista vazia
  obterMensagemListaVazia(categoria: string): string {
    const categoriaLower = categoria.toLowerCase();

    if (!this.termoBusca) {
      if (this.filtroAtivo === FILTROS.TODOS) {
        return `Nenhuma notificação de ${categoriaLower} encontrada`;
      }
      const status = this.filtroAtivo === FILTROS.ATIVAS ? 'ativa' : 'inativa';
      return `Nenhuma notificação ${status} de ${categoriaLower}`;
    }

    if (this.filtroAtivo === FILTROS.TODOS) {
      return `Nenhuma notificação de ${categoriaLower} encontrada para "${this.termoBusca}"`;
    }

    const status = this.filtroAtivo === FILTROS.ATIVAS ? 'ativa' : 'inativa';
    return `Nenhuma notificação ${status} de ${categoriaLower} para "${this.termoBusca}"`;
  }

  getIconeNotificacao(tipo: string): string {
    const icones = {
      // Fidelidade
      'Aniversário': 'fas fa-birthday-cake',
      'Bem-Vindo': 'fas fa-handshake',
      'Cardápio': 'fas fa-utensils',
      'Cartão Completo': 'fas fa-award',
      'Cliente Perdido': 'fas fa-user-slash',
      'Confirmar Cartão': 'fas fa-check-circle',
      'Ganhou Pontos': 'fas fa-coins',
      'Link Extrato': 'fas fa-file-invoice',
      'Novo Cartão': 'fas fa-id-card',
      'Resgatou Cartão': 'fas fa-gift',
      'Resgatou Prêmio': 'fas fa-trophy',
      'Validar Cartão': 'fas fa-user-check',

      // Pedidos
      'Avaliar Pedido': 'fas fa-star',
      'Comanda Fechada': 'fas fa-cash-register',
      'ConfirmacaoPedido': 'fas fa-clipboard-check',
      'Mensagem Saudação Whatsapp Pedido': 'fas fa-comment-dots',
      'Pedido Alterado': 'fas fa-edit',
      'Pedido Cancelado': 'fas fa-times-circle',
      'Pedido Confirmado': 'fas fa-check-circle',
      'Pedido Em Preparação': 'fas fa-fire-alt',
      'Pedido Entregue': 'fas fa-check-double',
      'Pedido Pronto': 'fas fa-concierge-bell',
      'Pedido Realizado Cardapio Online': 'fas fa-globe',
      'Pedido Saiu Para Entrega': 'fas fa-motorcycle',

      // SDR
      'Etapa Inicial': 'fas fa-flag',
      'Etapa Intermediária': 'fas fa-clock',
      'Etapa Final': 'fas fa-flag-checkered',
      'Etapa Cancelada': 'fas fa-ban',

      // Entregas
      'Entregador Próximo': 'fas fa-running',
      'Entregador Chegou': 'fas fa-store',
      'Entregador a Caminho': 'fas fa-route',
      'Entrega Concluída': 'fas fa-box-check',
      'Link Pagamento do Pedido': 'fas fa-link',
      'Pagamento Pendente Online': 'fas fa-clock',
      'Pagamento Confirmado Online': 'fas fa-check-circle'
    };

    // Procura por correspondência parcial se não encontrar exata
    const tipoNormalizado = tipo.toLowerCase();
    for (const [key, value] of Object.entries(icones)) {
      if (tipoNormalizado.includes(key.toLowerCase())) {
        return value;
      }
    }

    return 'far fa-bell';
  }

  private filtreRemovendo(lista: any[], funcaoFiltro: (item: any) => boolean): any[] {
    const listaFiltrada = lista.filter(funcaoFiltro);

    listaFiltrada.forEach(element => {
      const index = lista.indexOf(element);
      lista.splice(index, 1);
    });

    return listaFiltrada;
  }
}
