.modal-header {
  background: url('/assets/fidelidade/Banner_01.png');
}

.loading {
  width: 16px;
  height: 16px;
}

.linha {
  border-bottom: 5px solid #979AA0;
  margin-left: -16px;
  margin-right: -16px;
}

.modal-title {
  line-height: 36px;
  font-size: 16px;
}

.centralizado {
  text-align: center;
}

.painel_grafico {
  max-height: 500px;
}

.ui-calendar.ui-calendar-w-btn{
  height: 37px !important;
}

.resgate{
  .alert{
    padding: 10px;

  }
}

.planos{
  .btn{
    line-height: 2 !important;
  }
  .btn-label{
    padding: 10px 10px;
    height: 45px;
    max-width: 200px;
    text-align: left;

  }
}

@media (min-width: 481px) and (max-width: 767px) {
  .card {
    margin-left: -12px;
    margin-right: -12px;
  }

  .card-body {
    padding: 1rem;
  }

  .mt-4 {
    margin-top: 10px !important;
  }
}

@media (max-width: 480px) {
  .card {
    margin-left: -12px;
    margin-right: -12px;
  }

  .card-body {
    padding: 1rem;
    padding-left: 5px;
    padding-right: 5px;
  }

  .mt-4 {
    margin-top: 10px !important;
  }
}

.borda {
  border: solid 1px #eeeeee;
  padding: 5px;
  border-radius: 5px;
  padding-bottom: 0px;
}

label {
  margin-bottom: 0px;
}

// Estilos para o cartão cliente
.cartao-cliente-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 6px 10px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  cursor: pointer;
  max-width: 100%;
  display: inline-flex;
  align-items: center;
  margin-left: 0;

  @media (max-width: 576px) {
    padding: 4px 8px;
    margin-top: 5px;
    margin-left: 0;
  }

  &:hover {
    background: #e9ecef;
    box-shadow: 0 2px 5px rgba(0,0,0,0.08);
  }

  .cartao-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    margin-right: 8px;

    i {
      font-size: 12px;
    }
  }

  .cartao-info {
    display: flex;
    flex-direction: column;
    margin-right: 8px;

    small {
      font-size: 0.65rem;
      margin-bottom: 1px;
      line-height: 1;
    }

    .font-weight-bold {
      font-size: 0.85rem;
      letter-spacing: 0.5px;
      line-height: 1.2;
    }
  }

  .data-criacao {
    font-size: 0.7rem;
    opacity: 0.7;
    margin-left: 8px;

    @media (max-width: 767px) {
      display: none;
    }
  }

  .badge-status {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 3px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 500;
    margin: 0 4px;

    &.status-ativo {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    &.status-inativo {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  }
}
