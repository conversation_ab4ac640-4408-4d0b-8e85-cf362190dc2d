// Variáveis de cores mais sofisticadas
$color-primary: #4361ee;
$color-success: #2ec4b6;
$color-warning: #ff9f1c;
$color-danger: #e71d36;
$color-gray: #6c757d;
$color-light: #f8f9fa;
$color-dark: #2d3748;
$color-blue-dark: #3a86ff;
$color-blue-light: #e1f5fe;

// Variáveis de cores para estados
$color-livre-bg: #d8f3dc;
$color-livre-text: #2d6a4f;
$color-ocupado-bg: #ffccd5;
$color-ocupado-text: #9d0208;
$color-alerta-bg: #fff3cd;
$color-alerta-text: #9c6644;

// Variáveis de design
$border-radius-sm: 8px;
$border-radius-md: 12px;
$border-radius-lg: 16px;
$box-shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.05);
$box-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.1);
$transition-default: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

// Layout Principal
.header-section {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  margin-bottom: 2.5rem;
  
  @media (min-width: 768px) {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .header-top {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .page-title {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: $color-dark;
    font-size: 1.75rem;
    font-weight: 600;
    letter-spacing: -0.02em;
    white-space: nowrap;
    
    i {
      color: $color-primary;
      font-size: 1.5rem;
    }
  }
}

.search-container {
  width: 100%;
  max-width: 600px;
  position: relative;
  
  .busca {
    width: 100%;
    height: 48px;
    border-radius: $border-radius-md;
    border: 1px solid #e2e8f0;
    padding: 0.75rem 1rem 0.75rem 2.75rem;
    font-size: 0.95rem;
    transition: $transition-default;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    
    &:focus {
      outline: none;
      border-color: $color-primary;
      box-shadow: 0 0 0 3px rgba($color-primary, 0.15);
    }
  }
  
  &::before {
    content: '\f002';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: $color-gray;
    font-size: 0.9rem;
    pointer-events: none;
  }
}

// Grid de Mesas
.mesas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 1.5rem;
  padding: 0.5rem;
}

// Card da Mesa
.mesa-card {
  .mesa-item {
    border: none;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-soft;
    transition: $transition-default;
    overflow: hidden;
    height: 260px;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: $box-shadow-hover;
    }
    
    // Cabeçalho do Card
    .card-header {
      padding: 1rem 1.5rem;
      border: none;
      background: linear-gradient(135deg, $color-ocupado-bg, lighten($color-ocupado-bg, 5%));
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      flex-shrink: 0;
      
      &.disponivel {
        background: linear-gradient(135deg, $color-livre-bg, lighten($color-livre-bg, 5%));
      }
      
      .mesa-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .mesa-numero {
          margin: 0;
          font-size: 1.125rem;
          font-weight: 700;
          color: $color-dark;
          letter-spacing: -0.01em;
        }
        
        .status-badge {
          padding: 0.35rem 0.75rem;
          border-radius: 20px;
          font-size: 0.75rem;
          font-weight: 600;
          background: rgba($color-ocupado-text, 0.15);
          color: $color-ocupado-text;
          letter-spacing: 0.02em;
          
          &.status-livre {
            background: rgba($color-livre-text, 0.15);
            color: $color-livre-text;
          }
        }
      }
    }
    
    // Corpo do Card
    .card-body {
      padding: 1.5rem;
      padding-bottom: 4.5rem;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      background: white;
      position: relative;
      
      // Mesa Disponível
      .mesa-disponivel {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        height: 100%;
        text-align: center;
        gap: 1rem;
        padding: 0.75rem 0;
        
        .status-icon {
          font-size: 2rem;
          color: $color-success;
          background: rgba($color-success, 0.1);
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
        }
        
        .status-text {
          color: $color-gray;
          font-size: 0.875rem;
          line-height: 1.5;
          margin-bottom: 1rem;
        }
        
        // Container para o botão - garantindo que esteja no final
        .btn-container {
          margin-top: auto;
          width: 100%;
          padding-top: 1rem;
        }
      }
      
      // Mesa Ocupada
      .mesa-ocupada {
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        padding: 0.25rem 0;
        
        .info-principal {
          background: #f9fafb;
          border-radius: $border-radius-md;
          padding: 0.875rem;
          
          .valor-total {
            margin-bottom: 0.75rem;
            
            .label {
              display: block;
              color: $color-gray;
              font-size: 0.7rem;
              text-transform: uppercase;
              letter-spacing: 0.05em;
              margin-bottom: 0.2rem;
            }
            
            .valor {
              color: $color-success;
              font-size: 1.25rem;
              font-weight: 700;
              line-height: 1.1;
              letter-spacing: -0.01em;
            }
          }
          
          .info-secundaria {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: $color-gray;
            
            .tempo, .comandas-count {
              display: flex;
              align-items: center;
              gap: 0.35rem;
              
              i {
                font-size: 0.875rem;
                color: $color-primary;
              }
            }
          }
        }
        
        .acoes-mesa {
          display: flex;
          gap: 0.75rem;
          margin-top: auto;
          
          .btn-acao {
            flex: 1;
            padding: 0.625rem;
            border: none;
            border-radius: $border-radius-md;
            font-size: 0.8125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: $transition-default;
            color: white;
            
            i {
              font-size: 0.875rem;
            }
            
            &:active {
              transform: scale(0.98);
            }
            
            &.nova-comanda {
              background: linear-gradient(135deg, $color-success, darken($color-success, 5%));
              box-shadow: 0 2px 10px rgba($color-success, 0.3);
              
              &:hover {
                background: linear-gradient(135deg, darken($color-success, 3%), darken($color-success, 8%));
                box-shadow: 0 4px 15px rgba($color-success, 0.4);
              }
            }
            
            &.adicionar-pedido {
              background: linear-gradient(135deg, $color-primary, darken($color-primary, 5%));
              box-shadow: 0 2px 10px rgba($color-primary, 0.3);
              
              &:hover {
                background: linear-gradient(135deg, darken($color-primary, 3%), darken($color-primary, 8%));
                box-shadow: 0 4px 15px rgba($color-primary, 0.4);
              }
            }
          }
        }
      }
      
      // Botão Selecionar
      .btn-selecionar {
        position: absolute;
        bottom: 1.5rem;
        left: 1.5rem;
        right: 1.5rem;
        width: calc(100% - 3rem);
        padding: 0.875rem;
        border: none;
        border-radius: $border-radius-md;
        background: #2ecc71;
        color: white;
        font-weight: 600;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        transition: $transition-default;
        box-shadow: 0 2px 10px rgba(#2ecc71, 0.3);
        
        &:hover {
          background: darken(#2ecc71, 5%);
          box-shadow: 0 4px 15px rgba(#2ecc71, 0.4);
          transform: translateY(-2px);
        }
        
        &:active {
          transform: scale(0.98);
        }
        
        i {
          font-size: 1.125rem;
        }
      }
    }
  }
}

// Responsividade
@media (max-width: 768px) {
  .mesas-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .header-section {
    .page-title {
      font-size: 1.5rem;
    }
  }
  
  .search-container .busca {
    height: 42px;
  }
}

@media (max-width: 576px) {
  .mesas-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }
  
  .header-section {
    .page-title {
      font-size: 1.5rem;
    }
  }
  
  .search-container .busca {
    height: 42px;
  }
}

// Estilos para o Modal de Seleção de Comandas
:host ::ng-deep .modal-selecionar-comanda {
  .k-window-content {
    padding: 0;
    border-radius: $border-radius-lg;
    overflow: hidden;
  }
  
  .modal-comandas {
    display: flex;
    flex-direction: column;
    height: 100%;
    
    .comandas-list {
      flex: 1;
      overflow-y: auto;
      padding: 1.25rem;
      background: #f9fafb;
      
      .comanda-item {
        background: white;
        border: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: $border-radius-md;
        padding: 1.25rem;
        margin-bottom: 1rem;
        cursor: pointer;
        transition: $transition-default;
        position: relative;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &:hover {
          border-color: $color-primary;
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          
          &::before {
            opacity: 1;
          }
        }
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          background: linear-gradient(to bottom, $color-primary, lighten($color-primary, 15%));
          border-radius: $border-radius-md 0 0 $border-radius-md;
          opacity: 0;
          transition: $transition-default;
        }
        
        .comanda-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.75rem;
          
          .comanda-codigo {
            font-size: 1.125rem;
            font-weight: 600;
            color: $color-blue-dark;
            letter-spacing: -0.01em;
          }
          
          .comanda-valor {
            font-size: 1.125rem;
            font-weight: 700;
            color: $color-success;
            background: rgba($color-success, 0.1);
            padding: 0.35rem 0.75rem;
            border-radius: 20px;
          }
        }
        
        .comanda-detalhes {
          .comanda-hora {
            font-size: 0.875rem;
            color: $color-gray;
            display: flex;
            align-items: center;
            gap: 0.35rem;
            
            i {
              color: $color-gray;
              font-size: 0.75rem;
            }
          }
        }
      }
    }
    
    .nova-comanda {
      padding: 1.25rem;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      background: white;
      
      .btn-nova-comanda {
        width: 100%;
        padding: 0.875rem;
        border: none;
        border-radius: $border-radius-md;
        background: linear-gradient(135deg, $color-success, darken($color-success, 5%));
        color: white;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        transition: $transition-default;
        box-shadow: 0 2px 10px rgba($color-success, 0.3);
        
        &:hover {
          background: linear-gradient(135deg, darken($color-success, 3%), darken($color-success, 8%));
          box-shadow: 0 4px 15px rgba($color-success, 0.4);
        }
        
        &:active {
          transform: scale(0.98);
        }
        
        i {
          font-size: 1.125rem;
        }
      }
    }
  }
}

// Estilos para o Modal Lateral
::ng-deep .modal-lateral {
  &.k-dialog-wrapper {
    position: fixed !important;
    top: 60px !important;
    right: 0 !important;
    bottom: 0 !important;
    left: auto !important;
    margin: 0 !important;
    transform: none !important;
    z-index: 10000 !important;
    width: auto !important;
  }
  
  .k-dialog {
    position: relative !important;
    height: 100vh !important;
    border-radius: $border-radius-lg 0 0 0 !important;
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    margin: 0 !important;
    max-width: 420px;
    width: 100% !important;
  }
  
  .k-window-content {
    padding: 0;
    height: 100vh !important;
    display: flex;
    flex-direction: column;
  }
  
  .k-window-titlebar {
    padding: 1.25rem;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    
    .k-window-title {
      font-size: 1.25rem;
      font-weight: 600;
      letter-spacing: -0.01em;
      color: $color-dark;
    }
    
    .k-window-actions .k-button {
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: $transition-default;
      
      &:hover {
        background: rgba($color-danger, 0.1);
        color: $color-danger;
      }
    }
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// Estilo para o overlay do modal lateral
body.modal-lateral-aberto {
  overflow: hidden;
  
  &:after {
    content: '';
    position: fixed;
    top: 60px !important;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(3px);
    z-index: 9999;
    animation: fadeIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Melhorias para o alerta "Não gera comanda"
.nao-gera-comanda {
  background-color: $color-alerta-bg;
  border: 1px solid $color-warning;
  
  &.status-badge {
    background-color: rgba($color-alerta-text, 0.15);
    color: $color-alerta-text;
    font-size: 0.75rem;
    border-radius: 20px;
    margin-left: 8px;
    align-items: center;
    font-weight: 600;
    position: relative;
    cursor: help;
    
    i {
      margin-right: 4px;
      font-size: 0.75rem;
    }
    
    &:hover::after {
      opacity: 1;
      visibility: visible;
    }
  }
}

// Efeito de pulsação para mesas ocupadas
@keyframes pulsate {
  0% {
    box-shadow: 0 0 0 0 rgba($color-danger, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba($color-danger, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba($color-danger, 0);
  }
}

.mesa-item .card-header:not(.disponivel) .status-badge {
  animation: pulsate 2s infinite;
}

// Melhorias em elementos responsivos
@media (max-width: 768px) {
  ::ng-deep .modal-lateral .k-dialog {
    max-width: 100%;
    width: 100% !important;
  }
  
  .mesa-card .mesa-item {
    height: auto;
    min-height: 240px;
  }
}

// Estilo para o botão Voltar
.btn-voltar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: $color-dark;
  font-size: 1rem;
  transition: $transition-default;
  margin-right: 0.75rem;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  &:hover {
    background: $color-light;
    color: $color-primary;
    transform: translateX(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
}
