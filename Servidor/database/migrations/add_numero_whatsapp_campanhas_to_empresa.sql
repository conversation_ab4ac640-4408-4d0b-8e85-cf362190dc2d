-- Migration: Adicionar número WhatsApp específico para campanhas na tabela empresa
-- Data: 2025-01-19
-- Descrição: Permite que empresas tenham um número WhatsApp diferente para envio de campanhas

-- Adicionar coluna para referenciar o número WhatsApp usado em campanhas
ALTER TABLE empresa 
ADD COLUMN numero_whatsapp_campanhas_id INT;

-- Adicionar foreign key constraint
ALTER TABLE empresa 
ADD CONSTRAINT fk_empresa_numero_whatsapp_campanhas 
FOREIGN KEY (numero_whatsapp_campanhas_id) 
REFERENCES numero_whatsapp(id);

-- Adicionar coment<PERSON>rio para documentação
COMMENT ON COLUMN empresa.numero_whatsapp_campanhas_id IS 'ID do número WhatsApp usado especificamente para envio de campanhas. Se NULL, usa o número principal.';
