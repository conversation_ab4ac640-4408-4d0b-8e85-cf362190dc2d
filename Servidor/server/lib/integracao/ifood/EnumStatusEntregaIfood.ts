import {EventsIfoodDeliveryEnum} from "../../../service/integracoes/EventsIfoodEnum";


export enum EnumStatusEntregaIfood{
  Cotado = 'cotado',
  Pendente = 'pendente',
  Solicitado = 'solicitado',
  Negado = 'negado',
  EntregadorAlocado = 'alocado',
  AcaminhoDaLoja = 'pickup',
  PedidoColetado = 'pickup_complete',
  ChegouDestino = 'arrived_at_destination',
  EntregaConfirmada = 'delivered',
  RetornandoLoja = 'returning_to_origin',
  RetornouLoja = 'returned',
  Cancelado = 'cancelado'
}

export const DeParaEventsStatusDeliveryPedido = new Map<string, any>([
  [EventsIfoodDeliveryEnum.EntregadaorSolicitado, EnumStatusEntregaIfood.Pendente],
  [EventsIfoodDeliveryEnum.EntregadaorAceito, EnumStatusEntregaIfood.Solicitado],
  [EventsIfoodDeliveryEnum.EntregadaorNegado, EnumStatusEntregaIfood.Negado],
  [EventsIfoodDeliveryEnum.EntregadorAlocado, EnumStatusEntregaIfood.EntregadorAlocado],
  [EventsIfoodDeliveryEnum.EntregadorAcaminhoLoja, EnumStatusEntregaIfood.AcaminhoDaLoja],
  [EventsIfoodDeliveryEnum.EntregadorChegouLoja, EnumStatusEntregaIfood.AcaminhoDaLoja],
  [EventsIfoodDeliveryEnum.EntregadorColetouPedido, EnumStatusEntregaIfood.PedidoColetado],
  [EventsIfoodDeliveryEnum.EntregadorChegouDestino, EnumStatusEntregaIfood.ChegouDestino],
  [EventsIfoodDeliveryEnum.EntregadorRetornandoLoja, EnumStatusEntregaIfood.RetornandoLoja],
  [EventsIfoodDeliveryEnum.RequestDevolucaoDecisaoCodigo, EnumStatusEntregaIfood.RetornandoLoja],
  [EventsIfoodDeliveryEnum.EntregadorRetornouLoja, EnumStatusEntregaIfood.RetornouLoja],
  [EventsIfoodDeliveryEnum.RequestCodigoConfirmacaoDeliveryValidado, EnumStatusEntregaIfood.EntregaConfirmada],

])
