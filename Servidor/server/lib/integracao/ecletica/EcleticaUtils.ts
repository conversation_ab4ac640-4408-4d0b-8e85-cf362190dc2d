import {ItemPedido} from "../../../domain/delivery/ItemPedido";
import {EnumImpressao} from "./DTOPedidoEcletica";
import {DTOItemPedidoEcletica, IItemProdutoEcletica} from "./DTOItemPedidoEcletica";

export class EcleticaUtils{


  static obtenhaListaItens(itemPedido: ItemPedido, agrupamento: any, observacoesNoInicio: boolean,
                           indicePrimeiroItem: number, impressao: EnumImpressao = null,
                           pedidoMesa: boolean = false){

    let itens: Array<IItemProdutoEcletica> = [];
    let dtoItemProduto: IItemProdutoEcletica;
    let dtoItensSabores: any = [];

    let adicionaisItem: Array<any> = itemPedido.obtenhaAdicionaisEnviarComoProduto();
    let adicionaisNormais = adicionaisItem.filter( adicional => !adicional.sabor);
    let adicionaisSaboresPizza = adicionaisItem.filter( adicional => adicional.sabor);

    let totalAdicionais = 0, itensAdicionais: any = []

    let temAdicionalPrecoCalculado = adicionaisNormais.find((item: any) => item.precoCalculado != null) != null;

    if(itemPedido.produto.preco > 0 || itemPedido.produto.ehBrinde || temAdicionalPrecoCalculado){
      if(itemPedido.produto.codigoPdv){
        dtoItemProduto = DTOItemPedidoEcletica.novoDeItemProduto(itemPedido, agrupamento.contador, impressao, pedidoMesa);
        itens.push(dtoItemProduto);
      } else {
        throw Error (String(`Produto não possui código do PDV cadastrado: "${itemPedido.produto.nome}"`));
      }
    }

    adicionaisNormais.forEach( (itemAdicional: any) => {
      if(itemAdicional.codigoPdv){
        let dtoItemAdicional =
          DTOItemPedidoEcletica.novoDeAdicional(itemPedido.qtde, itemAdicional, agrupamento.contador, impressao,
            pedidoMesa);

        itensAdicionais.push(dtoItemAdicional)

        let totalDoAdicional = dtoItemAdicional.valor_unitario * (dtoItemAdicional.quantidade / itemPedido.qtde );

        totalAdicionais += totalDoAdicional;
      } else {
        let descricaoItem = String(`Adicional "${itemAdicional.nome}" do produto "${itemPedido.produto.nome}"`);

        throw Error (
          String(`${descricaoItem} não possui código do PDV cadastrado`));
      }
    })

    if(dtoItemProduto){
      dtoItemProduto.valor_unitario -= totalAdicionais
      dtoItemProduto.valor_unitario = Number(  dtoItemProduto.valor_unitario.toFixed(2));
    }

    // nao subir a linha prescisa do totalAdicionais
    if(adicionaisSaboresPizza.length){ // tratar envio sabores pizza por tamanho
      let totalSabores = adicionaisSaboresPizza.length;

      for(let item = 1; item <= itemPedido.qtde; item++){
        let qtdeDistribuidoSabores = 0, totalProdutoPizza = (itemPedido.valor) - totalAdicionais;
        let qtdeMedia   = Number((1 /  totalSabores ).toFixed(2));
        let enviarProdutoTamanhoSabores = itemPedido.produtoTamanho && itemPedido.produtoTamanho.template.deParaTamanhoSabores.length;

        //desconto vai no item, quando nao mesa desconto vai no pagamento
        if(pedidoMesa && itemPedido.desconto)
          totalProdutoPizza = itemPedido.obtenhaValorPagoUnitario() - totalAdicionais;


        if(enviarProdutoTamanhoSabores){
          let itemPizzaTamanhoSabores =
            itemPedido.produtoTamanho.template.deParaTamanhoSabores.find( dePara => dePara.qtdeSabores === totalSabores)

          if(!itemPizzaTamanhoSabores){
            let produtoPizza: any = itemPedido.produto;
            let descricao = String(`Template ${produtoPizza.template.identificador } ${itemPedido.produtoTamanho.template.descricao}`)
            throw Error (
              String(` ${descricao} não possui código do PDV cadastrado para ${totalSabores} sabores`));

          }

          itens.push({
            produto_brinde: 'N',
            agrupamento:  agrupamento.contador,
            impressao: impressao,
            id_produto:  Number(itemPizzaTamanhoSabores.codigoPdv),
            nome: itemPizzaTamanhoSabores.nome.substring(0, 50),
            quantidade: 1,
            valor_unitario: 0,
            obs:  ''}
          );
        }

        for(let i = 0; i < totalSabores; i++){
          let itemAdicional: any = adicionaisSaboresPizza[i];
          let ultimo: boolean = (i === totalSabores - 1);

          if(itemAdicional.codigoPdv){
            let qtdeSabor = qtdeMedia;

            if(ultimo)
              qtdeSabor =  Number((1 - qtdeDistribuidoSabores).toFixed(2));

            let dtoItemSaborProduto = DTOItemPedidoEcletica.novoDeItemTamanahoProduto(itemAdicional, qtdeSabor,
              itemPedido, totalProdutoPizza, agrupamento.contador, impressao);

            itens.push(dtoItemSaborProduto);

            dtoItensSabores.push(dtoItemSaborProduto);
            qtdeDistribuidoSabores += qtdeMedia;

          }else {
            let descricaoItem =     String(`Tamanho ${itemAdicional.descricaoTamanho} do produto ${itemAdicional.nome} `);

            throw Error (
              String(`${descricaoItem} não possui código do PDV cadastrado`));
          }
        }

        if(itensAdicionais.length){ // add os adicioanais por ultimo na lista
          for(let i = 0; i < itensAdicionais.length; i++){
            let adicional: any = Object.assign({}, itensAdicionais[i]);
            adicional.quantidade =  adicional.quantidade / itemPedido.qtde;
            adicional.agrupamento = agrupamento.contador;
            itens.push(adicional)
          }
        }
        agrupamento.contador++
      }
    } else {
      if(itensAdicionais.length){ // add os adicioanais por ultimo na lista
        for(let i = 0; i < itensAdicionais.length; i++)
          itens.push(itensAdicionais[i])
      }
    }

    if(itemPedido.observacao && itens.length){
      if(observacoesNoInicio){
        let primeiroItem: any =  itens[indicePrimeiroItem - 1];
        if(!primeiroItem)  primeiroItem = itens[0];
        primeiroItem.obs =  itemPedido.observacao.substr(0, 300) ;
      } else {
        itens[itens.length - 1].obs =  itemPedido.observacao.substr(0, 300) ;
      }
    }

    agrupamento.contador++

    return itens;
  }
}
