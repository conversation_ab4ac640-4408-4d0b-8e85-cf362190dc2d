import {DTOComboEcletica, DTONoCombo} from "./DTOComboEcletica";
import * as _ from "underscore";
import {EcleticaProdutoUtils} from "./EcleticaProdutoUtils";


export class EcleticaComboUtils {

  static obtenhaNo(nivel: any, combo: any){
    let minimo = 1,  maximo = combo.quantidade_estipulada > 0 ? combo.quantidade_estipulada : null ;

    // “S” determina quantidade máxima, “N” para    quantidade obrigatória.
    if(combo.usa_quantidade_maxima === "N")
      minimo =  combo.quantidade_estipulada


    return new DTONoCombo(nivel, minimo, maximo);
  }

  static monteCombos(combosEcletica: any, produtosEcletica: any, produtosCombos: any, categoriasEcletica: any){
    let combosMontar: any = [];
    let naoEncontrados = [];
    let produtos: any = [];

    if(combosEcletica ){
      categoriasEcletica.push({id: Number(EcleticaProdutoUtils.CODIGO_CORINGA), nome: 'Combos sem pai'})
      //primeira passada achar todos no raiz de cada combo
      let combosRaiz: any = combosEcletica.filter( (combo: any) => combo.combo_principal === 0);

      let mapCombosRaiz =  _.groupBy(combosRaiz, (combo: any) => combo.produto_principal)

      produtosCombos.forEach((produtoCombo: any) => {
         if(!mapCombosRaiz[produtoCombo.codigoPdv])
           produtos.push(produtoCombo)
      })


      Object.keys(mapCombosRaiz).forEach( (idProdutoPrincipal) => {
        let combosDoNo: any = mapCombosRaiz[idProdutoPrincipal];

        let produtoPrincipal = produtosEcletica.find( (produtoEcletica: any) => produtoEcletica.id === Number(idProdutoPrincipal))

        // não mecher em combos sem pai
        // if(!produtoPrincipal)
        //    produtoPrincipal = { id:  Number(EcleticaProdutoUtils.CODIGO_CORINGA),
        //                       nome: 'Combo sem pai', valor_unitario: 0, adicional_delivery: 0,
        //                        id_categoria: Number( EcleticaProdutoUtils.CODIGO_CORINGA)}

        //Pode ser “N” para “Normal”,“S” para escondido ou “V” para vinculado.
        // usa_quantidade_maxima //&& produtoPrincipal.status  !== 'S'
        if(produtoPrincipal  && produtoPrincipal.status   === 'N' ) { //
          let comboMontar = new DTOComboEcletica(produtoPrincipal);
          let noInicial  = '0';

          for(let i = 0; i < combosDoNo.length; i++){
            let combo = combosDoNo[i];
            let precoDiferenciado = combo.preco_diferenciado === 'S'
            let qtdeObrigatoria = combo.usa_quantidade_maxima === 'N';
            let qtdeEstipulada = combo.quantidade_estipulada

            let venderProdutosEmAdicionasSeparados =   qtdeEstipulada > 1 && qtdeObrigatoria

            let produtoVinculado = produtosEcletica.find( (produtoEcletica: any) => produtoEcletica.id === combo.produto_vinculado)

            if(produtoVinculado){
              let dtoNoCombo: DTONoCombo;

              if(venderProdutosEmAdicionasSeparados){
                let proximoNo = String(Number(i + 1));

                dtoNoCombo = EcleticaComboUtils.obtenhaNo(proximoNo, combo);

                comboMontar.nos[dtoNoCombo.nivel] = dtoNoCombo;

              } else {
                dtoNoCombo =  comboMontar.nos[noInicial]

                if(!dtoNoCombo){
                  dtoNoCombo = EcleticaComboUtils.obtenhaNo(noInicial, combo);

                  comboMontar.nos[dtoNoCombo.nivel] = dtoNoCombo;
                }
              }

              let produtoNaCompo: any = Object.assign({}, produtoVinculado);

              if(precoDiferenciado){
                produtoNaCompo.valor_unitario = combo.preco_unitario;
                produtoNaCompo.adicional_delivery = 0
              }

              dtoNoCombo.produtos.push(produtoNaCompo);
            } else {
              naoEncontrados.push(combo)
            }
          }

          Object.keys(comboMontar.nos).forEach((nivel) => {
            EcleticaComboUtils.obtenhaProdutosProximoNo(comboMontar,  nivel, combosEcletica, produtosEcletica);
          })

          // if(comboMontar.niveis['0'].produtos.length || Object.keys(comboMontar.niveis).length > 1)
          combosMontar.push(comboMontar)

        } else {
          combosDoNo.forEach( (combo: any) =>       naoEncontrados.push(combo))
        }
      })

      combosMontar.forEach( (comboMontar: any) => {
        produtos.push( comboMontar.convertaParaProduto(categoriasEcletica))
      })
    }

    console.log('Total nao encontrados: ' + naoEncontrados.length)

    return produtos;
  }

  static obtenhaProdutosProximoNo(comboMontar: DTOComboEcletica,
                                  nivel: string, combosEcletica: any, produtosEcletica: any){
    console.log(String(`No do nível ${nivel}, preencher ${comboMontar.nos[nivel].produtos.length} produtos:`))
    let proximoNo: any = String(Number(nivel) + 1);
    let dtoProximoNo: DTONoCombo

    for(let i = 0; i < comboMontar.nos[nivel].produtos.length; i++){
      let produto: any = comboMontar.nos[nivel].produtos[i];
      //ver depois
      let combosDesseNo =
        combosEcletica.filter( (combo: any) =>
          combo.combo_principal === comboMontar.produtoPrincipal.id && combo.produto_principal === produto.id);

      if(combosDesseNo && combosDesseNo.length){
        if(!dtoProximoNo){
          //ver depois , é possvievel vir mais de uma combo aqui mais de um aqui?????
          dtoProximoNo = EcleticaComboUtils.obtenhaNo(proximoNo, combosDesseNo[0])
          comboMontar.nos[proximoNo.toString()] = dtoProximoNo;
        }

        for(let j =  0; j < combosDesseNo.length; j++){
          let combo = combosDesseNo[j];
          let precoDiferenciado = combo.preco_diferenciado === 'S'

          let produtoVinculado: any = produtosEcletica.find( (produtoEcletica: any) => produtoEcletica.id === combo.produto_vinculado)

          if(produtoVinculado){
            if(! dtoProximoNo.produtos.find((prod: any) => prod.id === produtoVinculado.id)){
              let produtoNaCombo: any = Object.assign({}, produtoVinculado);

              if(precoDiferenciado){
                produtoNaCombo.valor_unitario = combo.preco_unitario;
                produtoNaCombo.adicional_delivery = 0
              }

              dtoProximoNo.produtos.push(produtoNaCombo);
            }
          }
        }
      }
    }

    if(dtoProximoNo)
      EcleticaComboUtils.obtenhaProdutosProximoNo(comboMontar,  proximoNo, combosEcletica, produtosEcletica)


  }
}
