import {DTODadosEcletica} from "./DTOPedidoEcletica";
import {ItemPedido} from "../../../domain/delivery/ItemPedido";
import {Pedido} from "../../../domain/delivery/Pedido";

export class DTOPedidoInLocoEcletica extends DTODadosEcletica{
    constructor(pedido: Pedido) {
      super()
      let agrupamento = { contador: 1 };

      let observacoesNoInicio = pedido.empresa.integracaoDelivery ?
        pedido.empresa.integracaoDelivery.enviarObservacoesNoInicio() : false;


      pedido.itens.forEach( (itemPedido: ItemPedido) => {
        this.obtenhaDTOItens(itemPedido, agrupamento, observacoesNoInicio, null, true);
      })

      if( pedido.cupom && pedido.desconto > 0)
        this.distribuaDescontoEntreItens(pedido.desconto)

      let garcom: any = (pedido.comanda.garcom &&  pedido.comanda.garcom.nome)
                              ? pedido.comanda.garcom  : pedido.garcom;

      if(pedido.garcom){
        console.log(String(`garçom pedido ${pedido.garcom.id} -> ${pedido.garcom.nome}`));
      }

      if(pedido.comanda && pedido.comanda.garcom){
        console.log(String(`garçom comanda ${pedido.comanda.garcom.id} -> ${pedido.comanda.garcom.nome}`));
      }

      let num = pedido.comanda ?  pedido.comanda.codigoPdv : null;
      let operacao: string;

      if(num) {
        operacao = 'comanda'
      } else {
        if(!pedido.mesa.codigoPdv) throw Error('Codigo mesa não configurado: ' + pedido.mesa.nome )
        num = pedido.mesa.codigoPdv.substr(0, 4)
        operacao = 'mesa'
      }

      this.dadosPedido = {
        tipo_pedido: 'L',
        garcom: garcom ? garcom.nome : '',
        operacao: operacao,
        num: num,
        nome_integrador: "MeuCardapio.ai"
      }
    }
}
