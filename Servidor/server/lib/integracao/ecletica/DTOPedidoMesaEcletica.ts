import {Pedido} from "../../../domain/delivery/Pedido";
import {ItemPedido} from "../../../domain/delivery/ItemPedido";
import {EcleticaUtils} from "./EcleticaUtils";
import {DTOItemPedidoEcletica, IItemProdutoEcletica} from "./DTOItemPedidoEcletica";
import {Ambiente} from "../../../service/Ambiente";

export class DTOInserirMesaEcletica{
  metodo  = "inserirPedidoJSON";
  numero: string;
  tipo_operacao: string;
  modulo = "Meucardapio";
  numero_pocket: string;
  versao_atual = "2.3.0";
  usa_pinpad = "N";
  id_transacao: string;
  dadosOperador: any;
  dadosDispositivo: any;
  inserirPedidoJSON: any
  constructor(numeroMesaComanda: string , operacao: string, id_transacao: string, garcom: any) {
    this.numero = numeroMesaComanda
    this.id_transacao =  id_transacao;
    this.tipo_operacao =  operacao;
    this.numero_pocket = "1";
    this.dadosOperador = { }

    if(garcom && garcom.codigoPdv)
      this.dadosOperador = { codigo_operador: garcom.codigoPdv, nome_operador: garcom.nome};

    this.dadosDispositivo =  {
      ip_dispositivo: Ambiente.Instance.ipV4(),
      sistema_operacional: "android",
      modelo_pinpad: "homolog_html_v2_cupom",
      operadora_pinpad: ""
    }

    this.inserirPedidoJSON  = {
      Itens: [],
      Checkin: "",
      ComprovantePagamento: [],
      CpfnaNota: [],
      FormaDePagamento: [],
      Pagamento: {
        "valor_total": 0,
        "dinheiro": 0,
        "cheque": 0,
        "cartao": 0,
        "ticket": 0,
        "outros": 0,
        "servico": 0,
        "desconto": 0,
        "vale_ref": 0,
        "contra_vale_emi": 0,
        "contra_vale_rec": 0,
        "troco": 0,
        "numero_de_pessoas": 1,
        "nome_cliente": "",
        "telefone_cliente": "",
        "repique": 0,
        "pendura": 0,
        "cpf": ""
      },
    }
  }

  adicioneItemEcletica(itemEcletica: DTOItemPedidoEcletica, ordem: number, posicao = 0){
    let novoItem: any = {
      "ordem": ordem,
      "cod_reg": itemEcletica.agrupamento,
      "posicao": posicao,
      "cod_item": itemEcletica.id_produto,
      "descr_item": itemEcletica.nome,
      "quantidade": itemEcletica.quantidade,
      "vlr_unit": itemEcletica.valor_unitario,
      "valor_total": Number((itemEcletica.quantidade *  itemEcletica.valor_unitario).toFixed(2)),
      "observacao": itemEcletica.obs,
      "multisabor": "N",
      "brinde": "N",
      "combo": "N",
      "promocao": "N",
      "status": "N",
      "faltante": "N"
    }

    //novoItem.impressao1 = "2";
    //novoItem.impressao2 = "3";

    this.inserirPedidoJSON.Itens.push(novoItem)
  }


}


export class DTOItemPedidoMesaEcletica extends DTOInserirMesaEcletica{
  constructor(numeroMesaComanda: string, tipo_operacao: string,  idTransacao: string, itens: Array<IItemProdutoEcletica>, garcom: any) {
    super(numeroMesaComanda, tipo_operacao, idTransacao, garcom)

    let ordem = 1;
    itens.forEach((item: IItemProdutoEcletica) => {
      this.adicioneItemEcletica(item, ordem++);
    })
  }
}


export class DTOPedidoMesaEcletica extends DTOInserirMesaEcletica {

  constructor(pedido: Pedido , empresa: any) {
    let codigoComanda = pedido.comanda.codigoPdv;
    let tipo_operacao = codigoComanda ? 'comanda' : "mesa";
    if(!codigoComanda)
      codigoComanda = pedido.mesa.codigoPdv;
    let garcom = pedido.garcom ;
    super( codigoComanda,  tipo_operacao, String(`${empresa.id}${pedido.codigo}`), garcom)

    let agrupamento = { contador: 1 };
    let observacoesNoInicio = empresa.integracaoDelivery.enviarObservacoesNoInicio()

    let ordem = 1;
    pedido.itens.forEach((item: ItemPedido) => {
      let itens = EcleticaUtils.obtenhaListaItens(item, agrupamento, observacoesNoInicio,
        this.inserirPedidoJSON.Itens.length, null, true);

      itens.forEach((itemEcletica: DTOItemPedidoEcletica) => {
        this.adicioneItemEcletica(itemEcletica, ordem++);
      })
    })
  }

}
