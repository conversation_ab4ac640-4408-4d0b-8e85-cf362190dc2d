import {Pedido} from "../../../domain/delivery/Pedido";
import {<PERSON>tato} from "../../../domain/Contato";
import {ItemPedido} from "../../../domain/delivery/ItemPedido";
import {PagamentoPedido} from "../../../domain/delivery/PagamentoPedido";
import {Endereco} from "../../../domain/delivery/Endereco";
import * as moment from "moment";
import {EnumTipoPagamentoExternoBlueSoft} from "../../emun/EnumTipoPagamentoExternoBlueSoft";

//Duvidas:
//3 - pedidoSeparado o que significa
export class DTOPedidoBlueSoft{
  aguardandoPagamento: boolean;
  canalVendaKey: any;
  cnpjLoja: any
  lojaKey: number
  numeroPedido: string
  cliente: DTOClienteBlueSoft;
  dadosDeEntrega: any;
  dataAgendamentoRetirada: string
  dataAgendamentoRetiradaFinal: string
  ecommerceKey = '2'; // 1 é vtex
  observacao: string
  itens: any = [];
  tipoFormaPagamentoKey: number;
  tipoIntegracaoPagamento: string;
  tipoDeEntrega: string;
  valorFrete: number
  //volume = 0
  //peso = 0
  //pedidoSeparado: boolean = true;
  //prazoPagamentoKey: number;
  recebimento: string; //Recebimento. Necessário apenas se gerenciamento pelo módulo de pedido balcão estiver habilitado.
  pagamentos: any = []
  dataPrevisaoEntrega: string;
  dataPrevisaoEntregaFinal: string;
  constructor(pedido: Pedido, empresa: any) {
    let credenciais = empresa.integracaoDelivery.obtenhaCredencial();
    this.cnpjLoja = credenciais.cpfCnpj;
    this.lojaKey = credenciais.loja;
    this.ecommerceKey = credenciais.idEcommerce;
    this.canalVendaKey = credenciais.canalVendaKey ;
   // this.aguardandoPagamento  = false; //apenas pagamentos online, que nao foram processados.
    this.numeroPedido = String(`PK${this.lojaKey}_${pedido.codigo}`);

    if(pedido.ehDelivery()){
      this.tipoDeEntrega = 'ENTREGA_NO_CEP';
      if(pedido.horarioEntregaAgendada  )
        this.dataPrevisaoEntrega =  moment(pedido.horarioEntregaAgendada).format('DD/MM/YYYY HH:mm')  //dd/MM/yyyy HH:mm
    } else {
      this.tipoDeEntrega = 'RETIRA_NA_LOJA';
      if(pedido.horarioEntregaAgendada  ){
        this.dataAgendamentoRetirada =  moment(pedido.horarioEntregaAgendada).format('DD/MM/YYYY HH:mm')  //dd/MM/yyyy HH:mm
        this.dataAgendamentoRetiradaFinal =  this.dataAgendamentoRetirada
      }
    }

    this.valorFrete = pedido.taxaEntrega;
    this.cliente = new DTOClienteBlueSoft(pedido.contato, pedido.endereco, empresa, pedido.obtenhaEmailCompra())

    let pagamentoCashback: PagamentoPedido ;

    pedido.pagamentos.forEach ( (pagamento: PagamentoPedido) => {
      if(!pagamento.foiPorCashback()){
        if (!pagamento.formaDePagamento.formaIntegrada)
          throw Error(String(`Forma de pagamento "${pagamento.formaDePagamento.nome}" não configurada na loja`))

        let pagamentoSB: any =  {
       //   idEcommerce: this.ecommerceKey,
          valor: pagamento.valor,
          formaDePagamento: pagamento.formaDePagamento.formaIntegrada.codigo
        }


        if(pagamento.formaDePagamento.online){
          this.tipoIntegracaoPagamento  = "PAGAMENTO_INTEGRADO"
          pagamentoSB.tipoIntegracaoPagamento =  "PAGAMENTO_INTEGRADO";
          if(!pagamento.formaDePagamento.pix) {
            //CIELO, PAGAR_ME, MUNDIPAGG,BRASPAG
            //let gatewayPagamento = pagamento.formaDePagamento.configMeioDePagamento.meioDePagamento;
            pagamentoSB.autorizadora =   'CIELO' //'PAGAR_ME'; gatewayPagamento === 'pargme' ou 'cielo'
            pagamentoSB.parcelas = 1;
            pagamentoSB.tipoPagamentoEcommerce = 'GATEWAY';
            pagamentoSB.bandeira =  pagamento.bandeira // pagamento.bandeira === 'Mastercard' ? 'Master' : pagamento.bandeira;
            pagamentoSB.codigoAutorizacao = pagamento.codigoAutorizacao;
            pagamentoSB.nsu = pagamento.nsu;
          }
        } else {
          this.tipoIntegracaoPagamento  = "PAGAMENTO_NAO_INTEGRADO" //Pagamento na entrega/retirada
          pagamentoSB.tipoPagamentoEcommerce =  pedido.ehDelivery() ? "PAGAMENTO_NA_ENTREGA" : "PAGAMENTO_NA_RETIRADA";
          pagamentoSB.tipoIntegracaoPagamento =  "PAGAMENTO_NAO_INTEGRADO"
          if(pagamento.formaDePagamento.formaIntegrada.codigo.indexOf('CARTAO_DE_') >= 0)
            pagamentoSB.autorizadora =   'CIELO'

          // this.recebimento = "string" // fixo para teste
        }
       // pagamentoSB.tipoFormaPagamentoKey =  Number(formaDePagamentoExterna.split('#')[0]);
        console.log(pagamentoSB)
        this.pagamentos.push(pagamentoSB);
      } else {
        pagamentoCashback = pagamento;
      }
    });

    for(let i = 0; i < pedido.itens.length; i++)
      this.adicioneItem(pedido.itens[i])
  }

  private adicioneItem(itemPedido: ItemPedido) {
    if(!itemPedido.produto.codigoPdv)
      throw Error (String(`Produto não possui código do PDV cadastrado: "${itemPedido.produto.nome}"`));

    let valorUnitario = itemPedido.valor;

    if(itemPedido.produto.vendaPorPeso())
       valorUnitario =  Number( (itemPedido.total / itemPedido.qtde).toFixed(2));


    let itemMesmoProuduto = this.itens.find((item: any) => item.produtoKey ===  itemPedido.produto.codigoPdv );

    if(!itemMesmoProuduto){
      let item = {
        desconto: itemPedido.desconto || 0,
        observacao: itemPedido.observacao ? itemPedido.observacao.substr(0, 150) : '',
        precoDeVenda: valorUnitario,
        produtoKey: itemPedido.produto.codigoPdv,
        quantidade: itemPedido.qtde,
        //"quantidadeSeparada": 0
      }

      this.itens.push(item)
    } else {
      itemMesmoProuduto.desconto +=  itemPedido.desconto;
      if( itemPedido.observacao){
        itemMesmoProuduto.observacao +=  itemPedido.observacao;
        itemMesmoProuduto.observacao =  itemMesmoProuduto.observacao.substr(0, 150)
      }

      itemMesmoProuduto.quantidade +=  itemPedido.qtde;
    }
  }
}

export class DTOClienteBlueSoft{
  nome: string;
  //nomeFantasia: string;
  //razaoSocial: string;
  cpfCnpj: string
  rg: string
  dataNascimento: string //2019-08-24T14:15:22Z string <date-time>
  email: string;
  enderecoCobranca: any;
  enderecoEntrega: any;
  telefones: Array<string> = []
  //fidelidade: boolean
  //inscricaoEstadual: string;
  constructor(contato: Contato, endereco: Endereco, empresa: any, emailCompra: string) {
    if(!contato.cpf) throw Error('Contato não tem cpf, ative campo cpf na loja.');

    this.nome = contato.nome;
    this.cpfCnpj = contato.cpf ;
    this.email = contato.email ?  contato.email :
                                  (emailCompra ? emailCompra :  String(`naotem${contato.telefone}@promokit.com.br`));

    if(contato.dataNascimento)
      this.dataNascimento = contato.dataNascimento ?  contato.dataNascimento.toString() : '';

    this.telefones.push(contato.telefone);

    let enderecoEntrega  = endereco ? endereco : this.obtenhaEnderecoPadrao(empresa);

    this.enderecoCobranca  = {
      bairro: enderecoEntrega.bairro,
      cep: enderecoEntrega.cep,
      cidade: enderecoEntrega.cidade.nome,
      complemento: enderecoEntrega.complemento,
      numero:  enderecoEntrega.numero || '',
      rua: enderecoEntrega.logradouro || '',
      uf: enderecoEntrega.cidade.estado.sigla,
      codigoIbgeCidade: enderecoEntrega.cidade.codigoIbge
    }

    if(endereco)
      this.enderecoEntrega = Object.assign({}, this.enderecoCobranca)

  }

  obtenhaEnderecoPadrao(empresa: any){
    let enderecoPadrao: any = {
      logradouro: 'Rua',
      endereco: "Endereço",
      numero: 1,
      bairro: 'Bairro',
      cep: '99999999'
    }

    if(!empresa.enderecoCompleto)
      throw Error('Endereço Padrão nao pôde ser obtido, informe o endereço completo no cadastro da empresa ')

    enderecoPadrao.cidade  = empresa.enderecoCompleto.cidade

    return enderecoPadrao;
  }
}
