import * as path from "path";

const fs = require('fs');


export class ErroUberDirect {
  constructor(public codigo: string, public descricao: string ) { }

  erroDeEndereco(){
    return this.codigo === 'unknown_location' || this.codigo === 'address_undeliverable' || this.codigo === 'unknown_location'
  }
}

export class ErrosUberUtils{
  static erros: any =  []

  static carregueErros(){
    try{
      let arquivo: string = path.join(__dirname, '../../../', 'public', 'erros-uberdirect.csv');
      let data: any = fs.readFileSync(arquivo);
      data.toString().split('\n').forEach( (linha: string) => {
        let colunas = linha.split(',');

        if(colunas.length > 2){
          let codigoErro = colunas[0].toString();
          let descricao = colunas[2].toString();

          ErrosUberUtils.erros.push(new ErroUberDirect(codigoErro , descricao))
        }
      })

      console.log('total erros carregados:' +     ErrosUberUtils.erros.length)
    }catch (e) {
      console.log('Arquivo de erros Uber nao carregados')
      console.log(e.message)
    }
  }

  static obtenhaErro(codigo: string){
    if(!ErrosUberUtils.erros.length)  ErrosUberUtils.carregueErros();

    if(!codigo) return null;

    return ErrosUberUtils.erros.find( (erro: ErroUberDirect) => erro.codigo  === codigo)
  }
}
