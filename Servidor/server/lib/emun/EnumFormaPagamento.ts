export enum  EnumFormaPagamento {
  Boleto = 'boleto',
  Cartao = 'cartao',
  CartaoCredito = 'cartao-credito' ,
  CartaoDebito = 'cartao-debito',
  Transferencia = 'transferencia',
  Dinheiro = 'dinheiro',
  Cashback = 'cashback',
  Resgate = 'resgate',
  Pix = 'pix'
}


export const EnumFormaPagamentoLabel = new Map<any, string>([
  [EnumFormaPagamento.Boleto, 'Boleto'] ,
  [EnumFormaPagamento.Dinheiro, 'Dinheiro'] ,
  [EnumFormaPagamento.Cartao, 'Cartão'] ,
  [EnumFormaPagamento.CartaoCredito, 'Cartão de Crédito'] ,
  [EnumFormaPagamento.CartaoDebito, 'Cartão de Débito'] ,
  [EnumFormaPagamento.Transferencia, 'Transferência Bancária'] ,
  [EnumFormaPagamento.Cashback, 'Cashback'] ,
  [EnumFormaPagamento.Resgate, 'Fidelidade Resgate'] ,
  [EnumFormaPagamento.Pix, 'Pix'] ,

]);
