import {TipoDeNotificacaoEnum} from "../../domain/TipoDeNotificacaoEnum";
import {EnumStatusComanda} from "../../domain/comandas/EnumStatusComanda";

export enum EnumStatusPedido {
  Novo,
  EmPreparacao,
  Pronto,
  SaiuParaEntrega,
  Entregue,
  Cancelado,
  Devolvido,
  FalhaNoPagamento,
  PagamentoRecusado
}

export const StatusPedidoLabel = new Map<number, string>([
  [EnumStatusPedido.Novo, 'Novo'],
  [EnumStatusPedido.EmPreparacao, 'Em preparação'],
  [EnumStatusPedido.Pronto, 'Pronto'],
  [EnumStatusPedido.SaiuParaEntrega, 'Saiu para entrega'],
  [EnumStatusPedido.Entregue, 'Entregue'],
  [EnumStatusPedido.Cancelado, 'Cancelado'],
  [EnumStatusPedido.Devolvido, 'Devolvido'],
  [EnumStatusPedido.FalhaNoPagamento, 'Falha no pagamento'],
  [EnumStatusPedido.PagamentoRecusado, 'Pagamento recusado']
]);


export const MensagemPedidoNotificacao = new Map<number, string>([
  [EnumStatusPedido.Novo, 'foi recebido'],
  [EnumStatusPedido.EmPreparacao, 'está sendo preparado'],
  [EnumStatusPedido.Pronto, 'ficou pronto'],
  [EnumStatusPedido.SaiuParaEntrega, 'saiu para entrega'],
  [EnumStatusPedido.Entregue, 'foi entregue'],
  [EnumStatusPedido.Cancelado, 'foi cancelado'],
  [EnumStatusPedido.Devolvido, 'foi devolvido'],
  [EnumStatusPedido.FalhaNoPagamento, 'teve uma tentativa de pagamento falha'],
]);

export const MensagemComandaNotificacao = new Map<string, string>([
  [EnumStatusComanda.Fechada, 'foi fechada']
]);



export const TipoDeNotificacaoPorStatusPedido = new Map<number, TipoDeNotificacaoEnum>( [
  [EnumStatusPedido.EmPreparacao, TipoDeNotificacaoEnum.PedidoEmPreparacao],
  [EnumStatusPedido.Cancelado, TipoDeNotificacaoEnum.PedidoCancelado],
  [EnumStatusPedido.Pronto, TipoDeNotificacaoEnum.PedidoPronto],
  [EnumStatusPedido.Entregue, TipoDeNotificacaoEnum.PedidoEntregue],
  [EnumStatusPedido.SaiuParaEntrega, TipoDeNotificacaoEnum.PedidoSaiuParaEntrega]
]);

export const StatusPedidoApi = new Map<String, EnumStatusPedido>([
  ['novo', EnumStatusPedido.Novo],
  ['emPreparacao', EnumStatusPedido.EmPreparacao],
  ['pronto', EnumStatusPedido.Pronto],
  ['saiuParaEntrega', EnumStatusPedido.SaiuParaEntrega],
  ['entregue', EnumStatusPedido.Entregue],
  ['cancelado', EnumStatusPedido.Cancelado]
]);
