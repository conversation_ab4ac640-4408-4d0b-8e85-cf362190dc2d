import {PedidoGenerico} from "../../domain/delivery/PedidoGenerico";
import {PagamentoPedido} from "../../domain/delivery/PagamentoPedido";
import {EnumMeioDePagamento} from "../../domain/delivery/EnumMeioDePagamento";
import {DTOPagamento} from "./DTOPedido";
// @ts-ignore
import moment = require("moment");
// @ts-ignore
import * as pluralize from "pluralize";
import {StatusPedidoLabel} from "../emun/EnumStatusPedido";
import {ItemPedido} from "../../domain/delivery/ItemPedido";
import {FormatadorUtils} from "../FormatadorUtils";


export class DTODadosPedido {
  id: number;
  codigo: string;
  guid: string;
  horario: any;
  horarioDescricao: string;
  horarioVenda: any;
  horarioVendaDescricao: any;
  horarioAtualizacao: any;
  horarioAtualizacaoDescricao: string;
  agoraDescricao: string;
  duracao: number;
  duracaoHoras: number;
  cliente: any = {};
  empresa: any = {};
  operador: any = {};
  endereco: any;
  enderecoCompleto: string;
  status: string;
  statusOrdem: number;
  visualizado: boolean;
  duracaoDescricao: string;
  subvalor: number;
  desconto: number;
  descontoTaxaEntrega: number;
  descontoFormaDePagamento: number;
  cashback: number;
  total: number;
  totalPagar: number;
  totalResgatado: number;
  acumulo: string;
  itens: any = [];
  pagamentos: any = [];
  pago: boolean;
  cancelado: boolean;
  podeEditar: boolean;
  taxaEntrega: number;
  taxaFormaDePagamento: number;
  observacoes: string;
  finalizado: boolean
  retirar: boolean
  despachar: boolean
  comerNoLocal: boolean;
  mensagemRetirada: string;
  formaDeEntrega: string;
  horarioEntregaAgendada: Date;
  horarioEntregaAgendadaDescricao: string;
  descricaoFormasPagamento: string
  aguardandoPagamentoOnline: boolean;
  foiPagoOnline: boolean;
  cartaoAutenticado: boolean;
  cupom: string;
  entregador: string;
  pontosGanhos: number;
  pontosGanhosDescricao: any;
  temCashback: boolean;
  linkPagamento: string;
  gatewayPagamentoOnline: string;
  aguardandoEstorno: boolean;
  ultimoPagamentoNegado: boolean;
  origem: string;
  cashbackUsado = 0;
  pagamentoOnline: boolean;
  pixOnline: boolean;
  novaTentativaCartao: boolean;
  novaTentativaPix: boolean;
  motivoReprovacao: string;
  aceito: boolean;
  notificarNovo: boolean;
  monitorarConfirmacaoPagamento: boolean;
  descricaoDescontoFormaDePagamento: string;
  informarCodigoComanda = false;
  aguardandoColeta = true;
  emDisputa: boolean;
  agendadoParaOutroDia: boolean;
  idLojaExterna: string;
  aguardandoTokenizar: boolean;
  impresso: boolean;
  horarioPrevisoFicarPronto: Date;
  tempoRestantePagar: number = null;
  doIfood = false;
  balcao: boolean;
  constructor(pedido: PedidoGenerico, empresa: any) {
    this.id = pedido.id;
    this.codigo = pedido.codigo;
    this.horario = pedido.horario;
    this.horarioVenda = pedido.obtenhaDataVenda();
    this.duracao = moment().diff(moment(pedido.horario), 'm')
    this.duracaoHoras = moment().diff(moment(pedido.horario), 'h')
    this.empresa = pedido.empresa;
    this.endereco = pedido.endereco;
    this.enderecoCompleto = pedido.endereco ? pedido.endereco.obtenhaEnderecoCompleto() : '-';
    this.origem = pedido.origem;
    this.aceito = true;
    this.doIfood =  pedido.doIfood()
    this.balcao = pedido.deBalcao();

    if( pedido.contato && pedido.contato.id ) {
      this.cliente = pedido.contato.obtenhaDTOContato();
      if(pedido.contato.ehConsumidorNaoIndentificado())
        delete this.cliente.telefone;
    } else if(this.balcao){
      if(pedido.identificacaoBalcao)
        this.cliente  = {nome: pedido.identificacaoBalcao}
    }

    this.impresso = pedido.impresso;

    if(this.endereco)
      if(  this.endereco.localizacao && this.endereco.localizacao.indexOf('undefined') >= 0)
          this.endereco.localizacao = ''

    this.guid = pedido.guid;
    this.subvalor = pedido.valor +  pedido.desconto  + pedido.descontoFormaDePagamento;
    this.taxaEntrega = pedido.taxaEntrega;
    this.taxaFormaDePagamento = pedido.taxaFormaDePagamento;
    this.desconto = pedido.desconto
    this.descontoTaxaEntrega = pedido.descontoTaxaEntrega
    this.descontoFormaDePagamento = pedido.descontoFormaDePagamento

    if(pedido.descontoFormaDePagamento)
      for(let pagamento of pedido.pagamentos)
        if(pagamento.formaDePagamento.possuiDesconto) {
          this.descricaoDescontoFormaDePagamento = "Desconto " + pagamento.formaDePagamento.descricao
          break;
        }
    this.total = pedido.obtenhaTotal();
    this.totalPagar = pedido.obtenhaTotalPagar();
    this.cancelado = pedido.foiCanceladoOuDevolvido();
    this.emDisputa = pedido.estaEmContestacao();
    this.totalResgatado = -pedido.obtenhaTotalResgatado();
    this.acumulo = empresa.integracaoPedidoFidelidade  ? empresa.integracaoPedidoFidelidade.tipoAcumulo() : ''

    moment.locale('pt-br');
    this.horarioDescricao = moment(pedido.horario)
      .utcOffset(empresa.fusoHorario)
      .format('DD MMMM YYYY, HH:mm');

    this.horarioVendaDescricao = moment(this.horarioVenda)
      .utcOffset(empresa.fusoHorario)
      .format('DD MMMM YYYY, HH:mm');

    this.horarioAtualizacao =  pedido.horarioAtualizacao;
    this.horarioAtualizacaoDescricao = moment(pedido.horarioAtualizacao)
      .utcOffset(empresa.fusoHorario)
      .format('DD MMMM YYYY, HH:mm');

    this.horarioEntregaAgendada = pedido.horarioEntregaAgendada;
    this.horarioEntregaAgendadaDescricao = pedido.horarioEntregaAgendada ? 
      moment(pedido.horarioEntregaAgendada)
        .utcOffset(empresa.fusoHorario)
        .format('DD MMMM YYYY, HH:mm') 
      : '';

    this.agoraDescricao = moment()
      .utcOffset(empresa.fusoHorario)
      .format('DD MMMM YYYY, HH:mm:ss');

    this.pagamentos = []
    this.observacoes = pedido.observacoes;
    this.pago = pedido.pagarOnline() ? pedido.pagamentoOnlineAprovado() : pedido.pago;
    this.podeEditar =  pedido.status <= 3 ;

    this.horarioPrevisoFicarPronto =   this.horarioEntregaAgendada ?
      this.horarioEntregaAgendada :  moment(pedido.horario).add(40, 'm').toDate();

    if(this.horarioEntregaAgendada && pedido.doIfood())
      this.agendadoParaOutroDia = !moment().isSameOrAfter(moment(this.horarioEntregaAgendada), 'day');

    this.finalizado = pedido.pago && Number(pedido.status) > 3;
    this.formaDeEntrega = pedido.formaDeEntrega ? (pedido.comerNoLocal ? 'Comer no local' : pedido.formaDeEntrega.nome) : '';
    this.retirar = pedido.retirarPessoalmente();
    this.despachar = pedido.ehDelivery();
    this.comerNoLocal = pedido.comerNoLocal;
    this.idLojaExterna  = pedido.idLojaExterna;

    if( pedido.pagamentos){
      pedido.pagamentos.forEach((pagamento: any) => {
        if(pagamento.link) this.linkPagamento = pagamento.link
        if(pagamento.foiPorCashback())
          this.cashbackUsado += pagamento.valor;

        this.pagamentos.push( new DTOPagamento(pagamento, this.total))
      })
    }

    if(this.retirar){
      this.mensagemRetirada = !pedido.comerNoLocal ? 'Retirada sera feita pelo cliente' : 'Cliente ira comer no local';
    }

    this.pagamentoOnline = pedido.pagarOnline();
    this.pixOnline = pedido.pagarOnline() && pedido.pagarViaPix();
    this.foiPagoOnline = pedido.pagarOnline() &&  pedido.pago;
    this.cartaoAutenticado =  pedido.autenticouPagamentoOnline();
    this.aguardandoColeta = pedido.ehDelivery() && pedido.aceito && pedido.aindaNaoSaiuEntrega();

    if(moment().diff(moment(pedido.obtenhaDataVenda()), 'days') > 1) //pedidos antigos
      this.aguardandoColeta = false;

    //tratamento de pagamentos online
    this.statusOrdem = Number(pedido.status);
    if( this.cancelado ){
      this.aguardandoPagamentoOnline = false;
      this.aguardandoTokenizar = false;
      this.novaTentativaCartao = false;
      this.novaTentativaPix = false;
      this.status =  StatusPedidoLabel.get(Number(pedido.status))
    } else {
      let pagamentoPrincipal: PagamentoPedido = pedido.obtenhaPagamentoPrincipal() as PagamentoPedido;

      let pagamentoEmPix = false;

      if( pagamentoPrincipal && pagamentoPrincipal.formaDePagamento )
        pagamentoEmPix = pagamentoPrincipal.formaDePagamento.pix

      this.notificarNovo = true;
      this.aguardandoPagamentoOnline = pedido.pagarOnline() &&  !pagamentoPrincipal.foiAprovado()
      this.gatewayPagamentoOnline = pedido.gatewayPagamentoOnline();

      if(pagamentoPrincipal && pagamentoPrincipal.reembolsoSolicitado())
        this.aguardandoEstorno = true;

      if(this.aguardandoPagamentoOnline){
        this.notificarNovo = pagamentoPrincipal.formaDePagamento.notificarNovoPedido;
        this.monitorarConfirmacaoPagamento = pagamentoPrincipal.formaDePagamento.notificarConfirmacaoPagamento;
      }

      if(this.gatewayPagamentoOnline){
        if(pagamentoEmPix){
          this.novaTentativaPix = pagamentoPrincipal ? ( !pagamentoPrincipal.codigoQrCode || pagamentoPrincipal.tentarNovamente() ) : false;
        } else {
          this.aguardandoTokenizar = pagamentoPrincipal.aguardandoTokenizar();
          if(this.aguardandoTokenizar  ){
            this.novaTentativaCartao = false;
          } else {
            this.novaTentativaCartao =   (  this.gatewayPagamentoOnline !== EnumMeioDePagamento.CieloSuperlink)
          }
        }
      }

      if( (pagamentoPrincipal && pagamentoPrincipal.foiNegado()) || this.novaTentativaPix)
        this.motivoReprovacao = pagamentoPrincipal.motivoReprovacao

      if(this.aguardandoPagamentoOnline){
        this.status = this.obtenhaStatusPagamentoOnline(pedido);
        this.ultimoPagamentoNegado = pedido.ultimoPagamentoNegado();
        if(pagamentoPrincipal && pagamentoPrincipal.dataExpiracao)
          this.tempoRestantePagar = moment(pagamentoPrincipal.dataExpiracao).diff( moment(), 's' )
      } else{
        this.status =  StatusPedidoLabel.get(Number(pedido.status))
      }
    }

    let comanda = (pedido as any).comanda;

    if( comanda && comanda.id){
      this.descricaoFormasPagamento =    comanda.pagamentos ?
        (pedido as any).pagamentos.map((pagamento: any) => pagamento.obtenhaDescricao()).join(', ') : ""

      this.informarCodigoComanda = empresa.integradoComComandas() ? ! comanda.codigoPdv : false;
    } else {
      this.descricaoFormasPagamento =    pedido.pagamentos ?
        pedido.pagamentos.map((pagamento: any) => pagamento.obtenhaDescricao()).join(', ') : ""

    }

    if( this.duracao  < 60 ){
      this.duracaoDescricao = Math.floor(this.duracao  ).toString() + " min";
    } else if  ( this.duracao  < 1440 ){
      this.duracaoDescricao = Math.floor(this.duracao /  (60 ) ).toString() + " hrs";
    } else {
      let dias = Math.floor(this.duracao / (60 * 24 ));
      this.duracaoDescricao = dias.toString() +  " " + pluralize('dia', dias);
    }


  }

  formateNumeros(){
    (this.subvalor as any) = FormatadorUtils.numeroParaCurrency(this.subvalor);
    (this.total as any) = FormatadorUtils.numeroParaCurrency(this.total);
    (this.taxaEntrega as any) = FormatadorUtils.numeroParaCurrency(this.taxaEntrega);
    (this.desconto as any) = FormatadorUtils.numeroParaCurrency(this.desconto);

    this.itens.forEach((item: any) => {
      item.valor =  FormatadorUtils.numeroParaCurrency(item.valor);
      item.total =  FormatadorUtils.numeroParaCurrency(item.total);
    })

    this.pagamentos.forEach((pagamento: any) => {
      pagamento.valor =  FormatadorUtils.numeroParaCurrency(pagamento.valor);
    })
  }
  protected obtenhaItensOrdenados(itens: Array<ItemPedido>) {
    return itens.sort(
      (itemA: any, itemB: any) => (itemA.produto.categoria ? Number(String(`${itemA.produto.categoria.posicao}${itemA.produto.id}`)) : -1) -
        (itemB.produto.categoria ? Number(String(`${itemB.produto.categoria.posicao}${itemB.produto.id}`)) : -1));
  }

  protected obtenhaStatusPagamentoOnline(pedido: PedidoGenerico) {
    if(this.ultimoPagamentoNegado)
      return "Pagamento negado"

    return "Pagamento a confirmar";
  }
}
