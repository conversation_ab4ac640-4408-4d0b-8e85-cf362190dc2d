import {ConfiguracoesNotaFiscal} from "../../../domain/nfce/configuracoes/ConfiguracoesNotaFiscal";
import {CFOP} from "../../../domain/nfce/CFOP";
import {TributacaoNaturezaOperacao} from "../../../domain/nfce/TributacaoNaturezaOperacao";

export class DTOConfiguracoesNotaFiscal {
  id: number;
  ambiente: number;
  versaoNFe: string;
  idToken: string;
  csc: string;
  tipoImpressaoDoDanfe: number;
  nomeArquivoPfx: string;
  senhaCertificado: string;
  empresa: any;
  numeroInicial: number;
  seriePadrao: number;
  inscricaoEstadual: string;
  cnae: string;
  regimeTributario: any;
  tributacaoVendaProducaoPropria: TributacaoNaturezaOperacao;
  tributacaoVendaProdutosTerceiros: TributacaoNaturezaOperacao;
  enviarAutomaticamente: boolean;
  naturezaOperacao: string;


  constructor(configuracoes: ConfiguracoesNotaFiscal, senhaCertificado: string = null) {
    this.id = configuracoes.id
    this.ambiente = configuracoes.ambiente;
    this.versaoNFe = configuracoes.versaoNFe;
    this.idToken = configuracoes.idToken;
    this.csc = configuracoes.csc;
    this.tipoImpressaoDoDanfe = configuracoes.tipoImpressaoDoDanfe;
    this.nomeArquivoPfx = configuracoes.certificado.arquivo;
    this.senhaCertificado = senhaCertificado
    this.numeroInicial = configuracoes.numeroInicial;
    this.seriePadrao = configuracoes.seriePadrao;
    this.inscricaoEstadual = configuracoes.inscricaoEstadual;
    this.cnae = configuracoes.cnae;
    this.regimeTributario = configuracoes.regimeTributario;
    this.tributacaoVendaProducaoPropria = configuracoes.tributacaoVendaProducaoPropria;
    this.tributacaoVendaProdutosTerceiros = configuracoes.tributacaoVendaProdutosTerceiros;
    this.enviarAutomaticamente = configuracoes.enviarAutomaticamente;
    this.naturezaOperacao = configuracoes.naturezaDaOperacao;

    this.empresa = configuracoes.empresa ? {id: configuracoes.empresa.id,
      nome: configuracoes.empresa.nome,
      dominio: configuracoes.empresa.dominio} : null;

  }
}
