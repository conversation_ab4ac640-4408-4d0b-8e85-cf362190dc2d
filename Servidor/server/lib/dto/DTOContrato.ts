import {Contrato} from "../../domain/faturamento/Contrato";
import {DTOFatura} from "./DTOFatura";
// @ts-ignore
import moment = require("moment");
import {DTOAssinatura} from "./DTOAssinatura";

export class  DTOContrato {
  id: number;
  plano: any;
  diaVencimento: number;
  dataProximoVencimento: Date;
  dataAtivacao: string;
  dataFimTrial: string;
  diasGratis: number;
  valorNegociado: number;
  limiteContatos: number;
  limiteContatosNegociado: number;
  faturas: Array<DTOFatura> = [];
  assinatura: any;
  ativo: boolean;
  diasAtivado: number;
  valorMensalidade: number;
  taxaAdesao: number;
  proximaFatura: DTOFatura;
  percentualAtivos = 0;
  pagaNoBoleto: boolean;
  pagaNoCartao: boolean;
  pagarViaPix: boolean;
  numeroParcelas: number;
  constructor(contrato: Contrato) {
    if(!contrato) return;
    this.id = contrato.id;
    this.plano = contrato.plano;
    this.diaVencimento = contrato.diaVencimento;
    this.dataProximoVencimento = contrato.dataProximoVencimento;

    if(contrato.dataAtivacao){
      this.dataAtivacao = moment(contrato.dataAtivacao).format('DD/MM/YYYY');
      this.diasAtivado = moment().diff(moment(contrato.dataAtivacao), 'd');
    }

    this.ativo = contrato.dataAtivacao != null || contrato.estaNoTrial();

    this.dataFimTrial = contrato.dataFimTrial ? moment(contrato.dataFimTrial).format('DD/MM/YYYY')  : '-';

    this.diasGratis = contrato.diasGratis;
    this.valorNegociado = contrato.valorNegociado;
    this.taxaAdesao = contrato.taxaAdesao;
    this.limiteContatosNegociado = contrato.limiteContatosNegociado;

    this.valorMensalidade = contrato.obtenhaValorMensalidade();
    this.limiteContatos = contrato.obtenhaLimiteContatos();
    this.numeroParcelas = contrato.numeroParcelas;

    this.faturas = contrato.faturas.map( fatura =>  new DTOFatura(fatura));

    if(contrato.assinatura){
      this.assinatura = new DTOAssinatura(contrato.assinatura)
      this.pagaNoBoleto = contrato.assinatura.pagaNoBoleto();
      this.pagaNoCartao = contrato.assinatura.aceitaCartao();
      this.pagarViaPix = contrato.assinatura.pagaNoPix();
    }


    if(contrato.faturas.length)
      this.proximaFatura =  new DTOFatura(contrato.obtenhaUltimaFatura())

  }
}

