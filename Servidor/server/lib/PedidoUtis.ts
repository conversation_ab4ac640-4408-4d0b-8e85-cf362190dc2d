import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";

let redis = require("redis");
let client = redis.createClient();

const inicio = 10000;

export class PedidoUtis {

  static async obtenhaProximoCodigo(empresa: any): Promise<string>{
    let chave = 'codigopedido#' + empresa.id;

    return new Promise(async (resolve, reject) => {
      client.incr(chave, async (err: any, reply: string) => {
        if(reply){


          if( Number(reply) === 1 ){ // sem chave no redis...
            let maxCodigo  = await new MapeadorDePedido().selecioneUltimoCodigo({ idEmpresa: empresa.id });

            let codigo = Number(maxCodigo) > 0  ?   ( Number(maxCodigo)  + 1 ) :  Number(inicio) + 1;

            client.set(chave, codigo, () => {
              resolve(codigo.toString() );
            })
          } else {
            resolve(reply );
          }


        } else {
          reject(err)
        }

      })
    })
  }
}
