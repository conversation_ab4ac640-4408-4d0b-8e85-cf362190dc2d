import {Ambiente} from "../../service/Ambiente";

export class DTODadosCobranca {
  cep: string;
  cnpj: string;
  cpf: string;
  email: string;
  bairro: string;
  logradouro: string;
  constructor(empresa: any) {
    this.cep = empresa.cep;
    this.cnpj = empresa.cnpj;
    this.email = empresa.email;

    if(empresa.responsavel){
      this.cpf = empresa.responsavel.cpf;
      if(empresa.responsavel.email)
        this.email = empresa.responsavel.email;
    }

    if(empresa.enderecoCompleto){
      this.bairro = empresa.enderecoCompleto.bairro;
      this.logradouro = empresa.enderecoCompleto.logradouro;
    }


    if(!Ambiente._instance.producao)
      this.email = '<EMAIL>'
  }
}
