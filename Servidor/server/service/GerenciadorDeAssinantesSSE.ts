import {ExecutorAsync} from "../utils/ExecutorAsync";
import {Usuario} from "../domain/Usuario";
import {MapeadorDeMensagemEnviada} from "../mapeadores/MapeadorDeMensagemEnviada";
import {v4 as uuid} from 'uuid';
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {StatusDeMensagem} from "./StatusDeMensagem";
import {RespostaEncurtarLinks} from "../utils/RespostaEncurtarLinks";
import {NotificacaoService} from "./NotificacaoService";
import {Ambiente} from "./Ambiente";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";

let redis = require("redis");
let client = redis.createClient();
client.select(1, () => {});
let subscriber = redis.createClient();

class Assinante {
  id: string;
  horario: Date;

  constructor(public req: any, public res: any) {
    this.id = process.pid + ":" + uuid();
    this.horario = new Date();
  }

  public obtenhaEmpresa() {
    return this.req.empresa;
  }

  public obtenhaUsuario() {
    return this.req.user;
  }

  public obtenhaCanal() {
    return this.obtenhaEmpresa().id + '';
  }

  obtenhaDados() {
    let usuario = this.obtenhaUsuario();

    if( usuario ) {
      usuario = {
        id: usuario.id,
        nome: usuario.nome
      }
    }

    return {
      id: this.id,
      usuario: usuario,
      empresa: {
        id: this.obtenhaEmpresa().id,
        nome: this.obtenhaEmpresa().nome
      },
      horario: this.horario
    };
  }
}

export class GerenciadorDeAssinantesSSE {
  private static _instancia: GerenciadorDeAssinantesSSE = new GerenciadorDeAssinantesSSE();
  private assinantes: Array<Assinante> = [];
  PREFIXO_CHAVE = 'assinantes::';
  TEMPO_EXPIRAR = 30;

  private constructor() {
    this.assineEvento();
  }

  static Instancia(): GerenciadorDeAssinantesSSE {
    return GerenciadorDeAssinantesSSE._instancia;
  }

  ativeAtualizadorDeAssinantes() {
    setInterval( () => {
      this.atualizeAssinantes();
    }, (this.TEMPO_EXPIRAR - 10) * 1000);
  }

  atualizeAssinantes() {
    //console.log('Atualizando assinantes ' + process.pid + " => " + this.assinantes.length);

    /*
    const assinantes = [...this.assinantes];

    const dadosAssinantes = [];

    for( let i = 0; i < assinantes.length; i++ ) {
      const assinante = assinantes[i];

      dadosAssinantes.push(assinante.obtenhaDados());
    }
     */

    //console.log(`[GerenciadorDeAssinantesSSE] AtualizandoAssinantes: ${this.obtenhaChave()} -> `);
    for( let i = 0; i < this.assinantes.length; i ++ ) {
      let assinante = this.assinantes[i];

//      console.log('[GerenciadorDeAssinantesSSE] Atualizando: ' +
//        `${this.obtenhaChave()} -> ` +
//        `${assinante.obtenhaEmpresa().id} - ${assinante.obtenhaEmpresa().nome}`);
    }

    client.expire(this.obtenhaChave(), this.TEMPO_EXPIRAR, (error: Error, tempo: number) => {
      //console.log('atualizou: ' + tempo);
    });
  }

  obtenhaTodosAssinantes() {
    return new Promise( (resolve, reject) => {
      client.keys(this.PREFIXO_CHAVE + '*', (error: Error, chaves: string[]) => {
        if( error ) {
          reject(error);
        }

        const multi = client.multi();

        for( let chave of chaves ) {
          multi.hgetall(chave);
        }

        const lista: Array<any> = [];

        multi.exec( (err: Error|null, replies: any) => {
          for( let assinantes of replies ) {
            Object.keys(assinantes).forEach( prop => {
              const objeto = JSON.parse(assinantes[prop]);

              lista.push(objeto);
            });
          }

          resolve({
            total: lista.length,
            assinantes: lista
          });
        });
      });
    });
  }

  assineEvento() {
    this.ativeAtualizadorDeAssinantes();

    subscriber.on("message", (channel: string, message: string) => {
//      console.log('mensagem nova');

      let objeto =  JSON.parse(message);

      const assinantes = [...this.assinantes];

      //console.log('Vai notificar assinantes: ' + channel);
      for( let i = 0; i < assinantes.length; i ++ ) {
        let assinante = assinantes[i];

        try {

          if (assinante.obtenhaCanal() !== channel) {
            continue;
          }

          if( assinante.res.finished ) {
            console.log('Finalizou');
            continue;
          }

          let idUltimaMensagem = assinante.req.query.id;

          if (objeto.tipo === 'enviou-mensagens') {
            idUltimaMensagem = objeto.id;

            this.busqueProximaMensagem(assinante.req, assinante.res, objeto.id);
          } else {
            this.busqueProximaMensagem(assinante.req, assinante.res, idUltimaMensagem);
          }

          console.log(channel);
        } catch(erro) {
          console.log('Erro ao notificar', erro);
          console.log(erro);
        }
      }
    });
  }

  sseDemo(req: any, res: any) {
    let messageId = 0;

    let idUltimaMensagem = req.query.id;

    const assinante = new Assinante(req, res);
    this.novoAssinante(assinante);

    this.busqueProximaMensagem(req, res, idUltimaMensagem);

    const empresa = req.empresa;

    subscriber.subscribe(assinante.obtenhaCanal());

    const idIntervalo = setInterval( () => {
      res.write(`id: 0\n`);
      res.write(`data: {}\n\n`);
    }, 15000);
    req.on('close', () => {
//      console.log(`[GerenciadorDeAssinantesSSE] ${this.obtenhaChave()} Assinante:`
//        + ` ${assinante.obtenhaEmpresa().id} - ${assinante.obtenhaEmpresa().nome}`);
      this.removaAssinante(assinante);

      clearInterval(idIntervalo);
      res.end();
      //console.log('encerrou: ' + empresa.id);

      req.session = null;
    });
  }

  busqueProximaMensagem(req: any, res: any, idMensagem: string) {
    if( !req.user ) {
      return;
    }

    ExecutorAsync.execute( async (cb: Function) => {
      require('domain').active.contexto.idEmpresa = req.empresa.id;

      const usuario: Usuario = req.user;

      require('domain').active.contexto.empresa = req.empresa;
      let numeroWhatsapp = usuario.numeroWhatsapp;

      let numero = '';

      if( numeroWhatsapp ) {
        numero = numeroWhatsapp.whatsapp;
      }

      console.log(`[GerenciadorDeAssinantesSSE] ${this.obtenhaChave()} buscando mensagem: `
        + req.empresa.nome + `(${usuario.nome} - ${numero}) -> ` + idMensagem);

      const mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada();

      if( !numeroWhatsapp ) {
        numeroWhatsapp = req.empresa.numeroWhatsapp;
      }

      let dados: any = {
        idm: idMensagem,
        numeroWhatsapp: numeroWhatsapp,
        inicio: 0,
        total: 1
      };

      if( req.empresa.dark ) {
        mapeadorDeMensagemEnviada.desativeMultiCliente();
        dados.pendentesDark = true;
      } else {
        dados.pendentes = true;
      }

      let terminou = false;
      do {
        let mensagem: MensagemEnviada = await mapeadorDeMensagemEnviada.selecioneSync(dados).catch( (erro) => {});

        console.log(`[GerenciadorDeAssinantesSSE] ${this.obtenhaChave()} mensagem: ` + ' -> ' + mensagem);

        if( mensagem ) {
          if( mensagem.expirou() ) {
            mensagem.status = StatusDeMensagem.Expirada;
            await mapeadorDeMensagemEnviada.atualizeSync(mensagem).catch( (erro: Error) => {});

            continue;
          }

          mensagem.enviarLinksBotao = req.empresa.enviarLinksBotao;

          if( mensagem.campanha ) {
            const campanha = mensagem.campanha;

            mensagem.posicao = campanha.qtdeEnviadas + 1;

            if( mensagem.mensagem.startsWith('Mensagem de Marketing:') ) {
              const notificacao = campanha.crieObjetoNotificacao();
              notificacao.tipoDeNotificacao = mensagem.tipoDeNotificacao;

              const contexto: any = {};

              const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(req.empresa));

              await notificacaoService.crieLinkCardapio(mensagem.contato, contexto, mensagem.tipoDeNotificacao === TipoDeNotificacaoEnum.TesteCampanha
                || mensagem.tipoDeNotificacao.indexOf('Etapa') !== -1);

              const resposta: RespostaEncurtarLinks = await notificacao.obtenhaMensagemProcessada(mensagem.empresa, mensagem.contato, contexto, true);

              if (resposta === null) {
                mensagem.status = StatusDeMensagem.NaoEnviadaCampo;
                await new MapeadorDeMensagemEnviada().atualizeSync(mensagem);
                terminou = false;

                continue;
              }

              mensagem.mensagem = resposta.mensagemFinal;

              await new MapeadorDeMensagemEnviada().atualizeSync(mensagem);
            }
          }

          terminou = true;
//          console.log('[GerenciadorDeAssinantesSSE] vai escrever: ' + req.empresa.nome + ' ' + mensagem.id);

          if( req.empresa.dark ) {
            mensagem.mensagem = '*' + mensagem.empresa.nome + '*:\n\n'  + mensagem.mensagem
          }

          mensagem.assinar = usuario.assinarMensagens;
          mensagem.abrirChat = (mensagem.empresa.dominio === 'sdrmarcio' || mensagem.empresa.dominio === 'fibo');

          await mensagem.processeLinksParaPreview();

          mensagem.processeMenu();

          res.write(`id: ${mensagem.id}\n`);
          res.write(`data: ${JSON.stringify(mensagem)}\n\n`);

//          console.log('[GerenciadorDeAssinantesSSE] escreveu: ' + req.empresa.nome);
        } else {
//          console.log('[GerenciadorDeAssinantesSSE] não achou: ' + idMensagem);
          terminou = true;
        }
      } while( !terminou );

      cb();
    }, () => {}, 100);
  }

  private novoAssinante(assinante: Assinante) {
//    console.log(`[GerenciadorDeAssinantesSSE] Novo Assinante: ${this.obtenhaChave()} ->` +
//      `${assinante.obtenhaEmpresa().id} - ${assinante.obtenhaEmpresa().nome}`);

    const chave = this.obtenhaChave();

    client.hset(chave, assinante.id, JSON.stringify(assinante.obtenhaDados()), (erro: Error) => {
      if( erro ) {
        return;
      }

      client.expire(chave, this.TEMPO_EXPIRAR, () => {
        this.assinantes.push(assinante);
        this.atualizeAssinantes();
      });
    });
  }

  private removaAssinante(assinante: Assinante) {
//    console.log(`[GerenciadorDeAssinantesSSE] Removendo assinante:` +
//      `${assinante.obtenhaEmpresa().id} - ${assinante.obtenhaEmpresa().nome}`);

    const chave = this.obtenhaChave();

    client.hdel(chave, assinante.id, (erro: Error) => {
      if (erro) {
        return;
      }

      this.assinantes = this.assinantes.filter((obj) => {
        return obj !== assinante;
      });
    });
  }


  obtenhaChave() {
    return this.PREFIXO_CHAVE + process.pid;
  }
}
