import {MapeadorDeQrCode} from "../mapeadores/MapeadorDeQrCode";
import {QrCode} from "../domain/delivery/QrCode";

export class QrCodeService {
  async existeQrCodeComMesmaUrlOuNome(url: string, nome: string): Promise<QrCode | null> {
    const queryParams = {
      url: url,
      nome: nome
    };

    const qrCodes = await this.listeAsync(queryParams);
    return qrCodes.length > 0 ? qrCodes[0] : null;
  }


  async listeAsync(query: any): Promise<QrCode[]> {
    const mapeadorDeQrCode = new MapeadorDeQrCode();
    return await mapeadorDeQrCode.listeAsync(query);
  }

  async insiraGraph(qrCode: QrCode): Promise<QrCode> {
    await this.validaQrCode(qrCode);

    const mapeadorDeQrCode = new MapeadorDeQrCode();

    await mapeadorDeQrCode.insiraGraph(qrCode);
    return qrCode;
  }

  async validaQrCode(qrCode: QrCode): Promise<void> {
    const qrCodeExistente = await this.existeQrCodeComMesmaUrlOuNome(qrCode.url, qrCode.nome);

    if (qrCodeExistente && qrCodeExistente.id !== qrCode.id) {
      let campoDuplicado = ".";
      if (qrCodeExistente.url.toUpperCase() === qrCode.url.toUpperCase()) {
        campoDuplicado = "link: <strong>" + qrCode.url + "</strong>";
      } else if (qrCodeExistente.nome.toUpperCase() === qrCode.nome.toUpperCase()) {
        campoDuplicado = "nome: <strong>" + qrCode.nome + "</strong>";
      }
      throw new Error(`Já existe um QRCode com o mesmo ${campoDuplicado}. (ID: ${qrCodeExistente.id}, Nome: ${qrCodeExistente.nome}, URL: ${qrCodeExistente.url})`);
    }
  }


  async atualizeSync(qrCode: QrCode): Promise<QrCode> {
    await this.validaQrCode(qrCode);

    const mapeadorDeQrCode = new MapeadorDeQrCode();
    await mapeadorDeQrCode.atualizeSync(qrCode);
    return qrCode;
  }

  async removaAsync(qrCode: QrCode): Promise<void> {
    const mapeadorDeQrCode = new MapeadorDeQrCode();

    await mapeadorDeQrCode.removaAsync(qrCode);
  }

  async selecioneSync(conditions: any): Promise<QrCode | null> {
    const mapeadorDeQrCode = new MapeadorDeQrCode();

    const qrCode = await mapeadorDeQrCode.selecioneSync(conditions);

    return qrCode;
  }
}
