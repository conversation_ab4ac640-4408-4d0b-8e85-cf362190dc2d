 import { ChatGPTService } from "../ia/ChatGPTService";

interface SugestaoParams {
  telefone: string;
  mensagens: any[];
  faseSpin: 'situacao' | 'problema' | 'implicacao' | 'necessidade';
  produto: string;
  tomConversa: 'formal' | 'informal' | 'tecnico';
  contato?: any;
  // Novos parâmetros para melhor contexto
  isNovoLead?: boolean;
  segmento?: string;
  tamanhoEmpresa?: 'micro' | 'pequena' | 'media' | 'grande';
  historicoInteracoes?: number;
  horarioMensagem?: 'manha' | 'tarde' | 'noite';
  motivoContato?: 'inbound' | 'outbound' | 'retorno';
}

interface ResultadoSugestao {
  sugestao: {
    texto: string;
    confianca: number;
    faseSpin: string;
    timestamp: Date;
  };
  faseSugerida?: string;
  observacoes?: string;
}

export class CrmWhatsappAssistantService {
  private chatGPTService: ChatGPTService;

  constructor() {
    this.chatGPTService = new ChatGPTService();
  }

  /**
   * Gera sugestão de resposta usando IA baseado na metodologia SPIN Selling
   */
  async gerarSugestaoResposta(params: SugestaoParams): Promise<ResultadoSugestao> {
    const { telefone, mensagens, faseSpin, produto, tomConversa, contato } = params;
    console.log('[CrmWhatsappAssistantService] Gerando sugestão com params:', { telefone, faseSpin, produto, tomConversa });

    // Construir contexto da conversa
    const contextoConversa = this.construirContextoConversa(mensagens);
    console.log('[CrmWhatsappAssistantService] Contexto construído:', contextoConversa);

    // Determinar contexto adicional
    const hora = new Date().getHours();
    const horarioMensagem = hora < 12 ? 'manha' : hora < 18 ? 'tarde' : 'noite';
    
    // Construir prompt baseado na fase SPIN com contexto enriquecido
    const prompt = this.construirPromptSpin({
      faseSpin,
      contextoConversa,
      produto,
      tomConversa,
      nomeContato: contato?.nome || 'Cliente',
      isNovoLead: !contato?.id,
      segmento: this.detectarSegmento(contextoConversa, contato),
      tamanhoEmpresa: contato?.tamanhoEmpresa || 'pequena',
      horarioMensagem
    });
    console.log('[CrmWhatsappAssistantService] Prompt construído para fase:', faseSpin);

    try {
      // Preparar mensagens para o ChatGPT
      const mensagensFormatadas = mensagens.slice(-10).map(msg => ({
        role: msg.remetente === 'Eu' ? 'assistant' : 'user',
        content: msg.texto
      }));

      // Chamar ChatGPT para gerar sugestão
      console.log('[CrmWhatsappAssistantService] Chamando ChatGPT com', mensagensFormatadas.length, 'mensagens');
      const resposta = await this.chatGPTService.chameOpenAIChat(
        telefone,
        'whatsapp_assistant',
        prompt,
        '', // mensagem vazia pois já está no contexto
        mensagensFormatadas,
        0.7, // temperatura
        '[whatsapp-assistant]',
        { type: 'json_object' }
      );
      console.log('[CrmWhatsappAssistantService] Resposta do ChatGPT:', resposta);

      // Processar resposta
      let resultado: any;
      try {
        resultado = JSON.parse(resposta.text);
      } catch (e) {
        // Se não for JSON, usar resposta como texto direto
        resultado = {
          sugestao: resposta.text,
          confianca: 0.7
        };
      }

      return {
        sugestao: {
          texto: resultado.sugestao || resposta.text,
          confianca: resultado.confianca || 0.8,
          faseSpin: faseSpin,
          timestamp: new Date()
        },
        faseSugerida: resultado.faseSugerida,
        observacoes: resultado.observacoes
      };

    } catch (erro: any) {
      console.error('[CrmWhatsappAssistantService] Erro ao gerar sugestão:', erro);

      // Fallback para sugestões predefinidas
      return this.obterSugestaoFallback(faseSpin, contextoConversa);
    }
  }

  /**
   * Detecta automaticamente a fase SPIN baseado nas mensagens
   */
  async detectarFaseSpin(mensagens: any[]): Promise<string> {
    const palavrasChave = {
      situacao: ['atualmente', 'fazemos', 'processo', 'sistema', 'hoje', 'empresa', 'negócio'],
      problema: ['dificuldade', 'problema', 'demora', 'erro', 'falha', 'ruim', 'complicado'],
      implicacao: ['impacto', 'prejuízo', 'tempo perdido', 'custo', 'afeta', 'consequência'],
      necessidade: ['preciso', 'quero', 'gostaria', 'solução', 'resolver', 'melhorar']
    };

    // Analisar últimas 5 mensagens
    const textoRecente = mensagens.slice(-5)
      .map(m => m.texto.toLowerCase())
      .join(' ');

    const pontuacao = {
      situacao: 0,
      problema: 0,
      implicacao: 0,
      necessidade: 0
    };

    // Contar palavras-chave
    for (const [fase, palavras] of Object.entries(palavrasChave)) {
      for (const palavra of palavras) {
        if (textoRecente.includes(palavra)) {
          pontuacao[fase as keyof typeof pontuacao] += 1;
        }
      }
    }

    // Retornar fase com maior pontuação
    let faseMaiorPontuacao = 'situacao';
    let maiorPontuacao = 0;

    for (const [fase, pontos] of Object.entries(pontuacao)) {
      if (pontos > maiorPontuacao) {
        maiorPontuacao = pontos;
        faseMaiorPontuacao = fase;
      }
    }

    return faseMaiorPontuacao;
  }

  /**
   * Constrói o contexto resumido da conversa
   */
  private construirContextoConversa(mensagens: any[]): string {
    // Pegar últimas 5 mensagens mais relevantes
    const mensagensRelevantes = mensagens.slice(-5);

    return mensagensRelevantes
      .map(m => `${m.remetente}: ${m.texto}`)
      .join('\n');
  }

  /**
   * Constrói o prompt específico para cada fase SPIN
   */
  private construirPromptSpin(params: {
    faseSpin: string;
    contextoConversa: string;
    produto: string;
    tomConversa: string;
    nomeContato: string;
    isNovoLead?: boolean;
    segmento?: string;
    tamanhoEmpresa?: string;
    horarioMensagem?: string;
  }): string {
    const { faseSpin, contextoConversa, produto, tomConversa, nomeContato, isNovoLead, segmento, tamanhoEmpresa, horarioMensagem } = params;

    const promptsBase = {
      situacao: `Você é um especialista em vendas consultivas usando metodologia SPIN Selling.
FASE ATUAL: SITUAÇÃO - Descoberta e entendimento do contexto do cliente.

CONTEXTO ADICIONAL:
${isNovoLead ? '- Este é um NOVO LEAD (não cadastrado no CRM)' : '- Cliente já cadastrado no CRM'}
${segmento ? `- Segmento: ${segmento}` : ''}
${tamanhoEmpresa ? `- Porte da empresa: ${tamanhoEmpresa}` : ''}
${horarioMensagem ? `- Horário da conversa: ${horarioMensagem}` : ''}

TÉCNICAS RECOMENDADAS PARA ESTA FASE:
• Rapport building através de observações personalizadas
• Perguntas abertas que estimulem o cliente a falar
• Demonstração de conhecimento do segmento
• Escuta ativa e validação
${segmento === 'restaurante' ? `
• Perguntas específicas: movimento diário, ticket médio, desafios operacionais
• Mencionar sazonalidade e horários de pico
• Explorar delivery vs presencial` : ''}

OBJETIVOS DA RESPOSTA:
1. Criar conexão genuína com ${nomeContato}
2. Descobrir o processo/sistema atual usado
3. Identificar pontos de melhoria sem julgar
4. Preparar terreno para fase de PROBLEMA
5. Tom ${tomConversa} ${horarioMensagem === 'noite' ? 'mas respeitoso do horário' : ''}

CONVERSA ATUAL:
${contextoConversa}

INSTRUÇÕES ESPECIAIS:
- Se ${isNovoLead ? 'é um novo lead, seja mais contextual e educativo' : 'já é conhecido, seja mais direto'}
- Evite perguntas genéricas, seja específico para o contexto
- Use técnicas de espelhamento verbal sutil
- Máximo 3 linhas de resposta
${tomConversa === 'informal' ? '- Use 1-2 emojis estratégicos' : '- Evite emojis, mantenha profissionalismo'}

Produto/Solução: ${produto}

FORMATO DE RESPOSTA JSON:
{
  "sugestao": "texto da mensagem sugerida (máx 3 linhas)",
  "confianca": 0.85,
  "faseSugerida": "situacao",
  "observacoes": "insight sobre o cliente ou próximo passo",
  "tecnicaUsada": "técnica de vendas aplicada"
}`,

      problema: `Você é um especialista em identificação de dores empresariais usando SPIN Selling.
FASE ATUAL: PROBLEMA - Descoberta profunda de dificuldades e desafios.

CONTEXTO ADICIONAL:
${isNovoLead ? '- NOVO LEAD: Seja mais cuidadoso ao explorar problemas' : '- Cliente conhecido: Pode ser mais direto'}
${segmento ? `- Segmento: ${segmento}` : ''}
${tamanhoEmpresa ? `- Porte: ${tamanhoEmpresa}` : ''}

TÉCNICAS AVANÇADAS PARA IDENTIFICAR PROBLEMAS:
• Perguntas de aprofundamento: "Você mencionou X, como isso afeta Y?"
• Técnica do "Por quê?": Explorar causas raízes
• Quantificação: "Com que frequência isso acontece?"
• Comparação temporal: "Sempre foi assim ou piorou recentemente?"
${segmento === 'restaurante' ? `
• Problemas comuns: desperdício, controle de estoque, gestão de equipe
• Explorar: erros em pedidos, tempo de espera, rotatividade
• Sazonalidade e impacto no fluxo de caixa` : ''}

PSICOLOGIA DA DOR:
- Dores explícitas: O que o cliente já reconhece
- Dores latentes: Problemas que ele não percebeu ainda
- Dores emocionais: Stress, frustração, preocupação
- Amplificar sem exagerar: Ajude-o a ver o impacto real

OBJETIVOS ESPECÍFICOS:
1. Identificar 2-3 problemas principais
2. Entender a frequência e severidade
3. Descobrir tentativas anteriores de solução
4. Preparar para quantificar impactos (fase implicação)
5. Tom ${tomConversa} com empatia genuína

CONVERSA ATUAL:
${contextoConversa}

INSTRUÇÕES CRÍTICAS:
- NUNCA ofereça soluções nesta fase
- Use "entendo" e "compreendo" para validar
- Faça o cliente se sentir ouvido e compreendido
${isNovoLead ? '- Novo lead: Eduque sobre problemas comuns do segmento' : '- Cliente conhecido: Relembre problemas anteriores'}
- Máximo 3 linhas, foco em UMA pergunta poderosa

Cliente: ${nomeContato}
Produto (NÃO mencionar ainda): ${produto}

FORMATO JSON:
{
  "sugestao": "mensagem com pergunta exploratória profunda",
  "confianca": 0.85,
  "faseSugerida": "problema",
  "observacoes": "principais dores identificadas",
  "proximaPergunta": "sugestão de follow-up",
  "dorPrincipal": "qual problema parece mais crítico"
}`,

      implicacao: `Você é um especialista em construção de urgência e ROI usando SPIN Selling.
FASE ATUAL: IMPLICAÇÃO - Amplificar consciência dos impactos e criar urgência.

CONTEXTO DO NEGÓCIO:
${segmento ? `- Segmento: ${segmento}` : ''}
${tamanhoEmpresa ? `- Porte: ${tamanhoEmpresa} (adapte os valores ao porte)` : ''}
${isNovoLead ? '- NOVO LEAD: Use benchmarks do mercado' : '- Cliente conhecido: Use dados históricos se disponíveis'}

TÉCNICAS AVANÇADAS DE IMPLICAÇÃO:
• Efeito dominó: "Se X continua, o que acontece com Y e Z?"
• Quantificação financeira: Transforme problemas em R$
• Impacto temporal: "Em 6 meses, isso significa..."
• Comparação com concorrência: "Enquanto isso, seus concorrentes..."
• Custo de oportunidade: "O que deixa de ganhar por causa disso?"

CÁLCULOS DE IMPACTO POR SEGMENTO:
${segmento === 'restaurante' ? `
• Perda por pedido errado: R$ médio x frequência x mês
• Tempo perdido em processos: Horas x custo/hora x funcionários
• Clientes perdidos: Ticket médio x frequência x lifetime value
• Desperdício: % do faturamento mensal` : `
• Produtividade perdida: Horas x valor/hora
• Retrabalho: Custo x frequência
• Oportunidades perdidas: Valor médio x quantidade`}

PSICOLOGIA DA URGÊNCIA (sem pressão excessiva):
- Mostre o custo de NÃO agir
- Use projeções realistas
- Crie visualização mental do impacto
- Gatilhos: perda, competição, escassez (sutil)

OBJETIVOS DESTA FASE:
1. Fazer o cliente SENTIR o peso do problema
2. Quantificar em R$ sempre que possível  
3. Criar senso de urgência natural
4. Preparar para apresentar solução (necessidade)
5. Tom ${tomConversa} mas com seriedade sobre impactos

CONVERSA ATUAL:
${contextoConversa}

INSTRUÇÕES ESPECIAIS:
- Use números e métricas quando possível
- Seja específico, não genérico
- ${horarioMensagem === 'noite' ? 'Reconheça o horário, seja breve' : 'Explore detalhes'}
- Máximo 3 linhas com UMA métrica impactante
- Se o cliente minimizar, amplifique com dados

Cliente: ${nomeContato}
Solução disponível (mencionar sutilmente se oportuno): ${produto}

FORMATO JSON:
{
  "sugestao": "mensagem quantificando impacto real",
  "confianca": 0.88,
  "faseSugerida": "implicacao", 
  "observacoes": "impactos principais identificados",
  "metricaPrincipal": "número mais impactante mencionado",
  "urgencia": "nível de urgência percebido (1-10)",
  "proximoGatilho": "qual urgência explorar em seguida"
}`,

      necessidade: `Você é um closer especialista em demonstração de valor e fechamento consultivo.
FASE ATUAL: NECESSIDADE - Apresentar solução conectada às dores e fechar negócio.

CONTEXTO DE FECHAMENTO:
${segmento ? `- Segmento: ${segmento}` : ''}
${tamanhoEmpresa ? `- Porte: ${tamanhoEmpresa}` : ''}
${isNovoLead ? '- NOVO LEAD: Ofereça teste/demonstração primeiro' : '- Cliente conhecido: Pode ir direto para proposta'}
${horarioMensagem === 'noite' ? '- HORÁRIO NOTURNO: Sugira conversa amanhã' : ''}

TÉCNICAS AVANÇADAS DE FECHAMENTO:
• Solução específica: "Para resolver X que você mencionou, nossa ferramenta Y..."
• ROI claro: "Investimento de R$ X, retorno estimado de R$ Y em Z meses"
• Prova social contextualizada: Cases do mesmo segmento/porte
• Redução de risco: Garantias, teste grátis, implementação assistida
• Criação de urgência: Benefício de começar agora vs depois

ESTRUTURA DA OFERTA IRRESISTÍVEL:
1. Recapitular problema principal
2. Apresentar solução específica
3. Quantificar benefício/ROI
4. Incluir diferencial único
5. Call-to-action claro e fácil

GATILHOS PSICOLÓGICOS ÉTICOS:
• Escassez: Vagas limitadas para onboarding este mês
• Autoridade: Certificações, prêmios, casos de sucesso
• Consenso: "Empresas como a sua já economizam X"
• Reciprocidade: Bônus exclusivo, consultoria grátis
• Compromisso: Começar pequeno, escalar depois

OPÇÕES DE FECHAMENTO POR CONTEXTO:
${isNovoLead ? `
• "Que tal uma demonstração gratuita amanhã?"
• "Posso criar uma conta teste para você experimentar?"
• "Vamos agendar 15min para eu mostrar na prática?"` : `
• "Podemos começar com o plano X este mês?"
• "Qual data seria ideal para iniciarmos?"
• "Prefere começar com implementação expressa ou completa?"`}

CONVERSA ATUAL:
${contextoConversa}

INSTRUÇÕES FINAIS:
- Seja específico sobre como ${produto} resolve as dores mencionadas
- Use números/métricas dos problemas discutidos
- Tom ${tomConversa} mas confiante e assertivo
- CTA claro e fácil de responder com "sim"
- Máximo 3 linhas, direto ao ponto
- Se houver objeção, não insista - volte para implicação

Cliente: ${nomeContato}
Produto/Solução: ${produto}

FORMATO JSON:
{
  "sugestao": "mensagem com oferta específica e CTA claro",
  "confianca": 0.90,
  "faseSugerida": "necessidade",
  "observacoes": "resumo da oportunidade",
  "valorProposto": "principal benefício quantificado",
  "proximoPasso": "ação específica sugerida",
  "probabilidadeFechamento": "% estimado de conversão"
}`
    };

    return promptsBase[faseSpin as keyof typeof promptsBase] || promptsBase.situacao;
  }

  /**
   * Retorna sugestão fallback caso a IA falhe
   */
  private obterSugestaoFallback(faseSpin: string, contexto: string): ResultadoSugestao {
    const sugestoesFallback = {
      situacao: 'Olá! Obrigado pelo contato. Para entender melhor como posso ajudar, você poderia me contar um pouco sobre como funciona seu processo atual?',
      problema: 'Entendo sua preocupação. Essas dificuldades que você mencionou acontecem com que frequência? Como isso tem impactado seu dia a dia?',
      implicacao: 'Realmente, isso deve estar afetando bastante a operação. Quanto tempo sua equipe perde com esse processo manual? E como isso impacta seus clientes?',
      necessidade: 'Pelo que entendi, nossa solução pode resolver exatamente esses pontos que você mencionou. Gostaria de ver uma demonstração rápida de como funcionaria no seu caso?'
    };

    return {
      sugestao: {
        texto: sugestoesFallback[faseSpin as keyof typeof sugestoesFallback] || sugestoesFallback.situacao,
        confianca: 0.6,
        faseSpin: faseSpin,
        timestamp: new Date()
      },
      observacoes: 'Sugestão padrão utilizada devido a erro na geração via IA'
    };
  }

  /**
   * Gera mensagem de rapport/atratividade para abordagem outbound
   */
  async gerarMensagemRapport(params: {
    telefone: string;
    nomeContato?: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
    produto: string;
    ultimaMensagem?: string;
  }): Promise<{ mensagem: string; confianca: number }> {
    const { telefone, nomeContato, empresa, tipoAbordagem, produto, ultimaMensagem } = params;
    console.log('[CrmWhatsappAssistantService] Gerando mensagem de rapport:', { telefone, tipoAbordagem, produto });

    try {
      // Construir prompt específico para rapport
      const prompt = this.construirPromptRapport({
        nomeContato: nomeContato || 'você',
        empresa,
        tipoAbordagem,
        produto,
        ultimaMensagem
      });
      console.log('[CrmWhatsappAssistantService] Prompt de rapport construído para abordagem:', tipoAbordagem);

      // Chamar ChatGPT para gerar mensagem
      const resposta = await this.chatGPTService.chameOpenAIChat(
        telefone,
        'whatsapp_rapport',
        prompt,
        '',
        [], // Sem histórico para rapport inicial
        0.8, // temperatura um pouco mais alta para criatividade
        '[whatsapp-rapport]',
        { type: 'json_object' }
      );
      console.log('[CrmWhatsappAssistantService] Resposta do ChatGPT (rapport):', resposta);

      // Processar resposta
      let resultado: any;
      try {
        resultado = JSON.parse(resposta.text);
      } catch (e) {
        // Se não for JSON, usar resposta como texto direto
        resultado = {
          mensagem: resposta.text,
          confianca: 0.7
        };
      }

      return {
        mensagem: resultado.mensagem || resposta.text,
        confianca: resultado.confianca || 0.85
      };

    } catch (erro: any) {
      console.error('[CrmWhatsappAssistantService] Erro ao gerar mensagem de rapport:', erro);

      // Fallback para mensagens predefinidas
      return this.obterMensagemRapportFallback(params);
    }
  }

  /**
   * Constrói prompt para gerar mensagens de rapport
   */
  private construirPromptRapport(params: {
    nomeContato: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
    produto: string;
    ultimaMensagem?: string;
  }): string {
    const { nomeContato, empresa, tipoAbordagem, produto, ultimaMensagem } = params;

    const promptsAbordagem = {
      direta: `Você é um copywriter especialista em cold outreach e vendas B2B diretas.
      
MISSÃO: Criar mensagem de WhatsApp que gere resposta imediata através de valor claro.

FÓRMULA DE ALTA CONVERSÃO:
1. Hook poderoso: Estatística, resultado ou pergunta intrigante
2. Credibilidade rápida: Prova social ou autoridade em 1 linha
3. Benefício específico: O que EXATAMENTE eles ganham
4. CTA fácil: Pergunta que só pode ser respondida com "sim" ou curiosidade
5. PS estratégico: Escassez, urgência ou benefício extra

TÉCNICAS PSICOLÓGICAS:
• Padrão de interrupção: Quebre expectativas logo no início
• Especificidade: Números exatos > afirmações vagas  
• FOMO sutil: O que estão perdendo agora
• Personalização: Mencione algo único sobre eles
• Curiosity gap: Deixe algo importante para revelar

ESTRUTURA VENCEDORA:
"[Hook com número] + [Prova social rápida] + [Benefício específico] + [CTA pergunta]"

Máximo 3 linhas | 1-2 emojis estratégicos | Tom ${empresa ? 'personalizado para ' + empresa : 'profissional'}`,

      indireta: `Você é um especialista em rapport building e social selling no WhatsApp.

MISSÃO: Criar conexão genuína que naturalmente leve a oportunidades de negócio.

PSICOLOGIA DA CONEXÃO:
1. Princípio da similaridade: Encontre pontos em comum
2. Validação emocional: Reconheça desafios sem julgar
3. Curiosidade genuína: Faça perguntas que mostrem interesse real
4. Timing respeitoso: Reconheça contexto (dia, hora, momento)
5. Valor antes da venda: Compartilhe algo útil primeiro

ESTRUTURA DE RAPPORT:
• Abertura calorosa e contextual
• Observação personalizada ou elogio específico
• Compartilhamento de valor (dica, insight, recurso)
• Convite suave para interação
• Deixar porta aberta sem pressão

GATILHOS DE CONEXÃO:
${empresa ? `• Mencione algo específico sobre ${empresa}` : '• Use referência do mercado/região'}
• Histórias curtas e relacionáveis
• Vulnerabilidade calculada (admitir algo)
• Humor leve se apropriado
• Interesse pelos desafios deles

Tom: Amigável mas profissional | 2-3 emojis calorosos | Como se conhecessem`,

      consultiva: `Você é um consultor estratégico usando WhatsApp para diagnóstico empresarial.

MISSÃO: Posicionar-se como trusted advisor através de perguntas poderosas.

FRAMEWORK CONSULTIVO:
1. Demonstrar expertise através de perguntas, não afirmações
2. Usar dados do mercado para contextualizar
3. Explorar o "por quê" por trás dos sintomas
4. Conectar pontos que eles não viram
5. Oferecer insights gratuitos de alto valor

TIPOS DE PERGUNTAS PODEROSAS:
• Diagnóstico: "Como vocês medem X atualmente?"
• Comparativa: "Como isso se compara com [benchmark]?"
• Projetiva: "Onde vocês se veem em 12 meses?"
• Hipotética: "E se pudessem [resultado desejado]?"
• Priorização: "De 1-10, quão crítico é resolver isso?"

ESTRUTURA CONSULTIVA:
1. Contexto do mercado/segmento
2. Pergunta diagnóstica específica
3. Oferta de insight ou recurso gratuito
4. Convite para "brainstorm" ou "troca de ideias"

${empresa ? `Demonstre conhecimento sobre ${empresa}/segmento` : 'Use insights gerais do mercado'}
Tom: Autoridade acessível | Sem emojis ou máximo 1 | Executive briefing`
    };

    const contextoProduto = produto === 'PromoKit - Sistema de Gestão'
      ? 'sistema de gestão para restaurantes e food service'
      : produto;

    let promptBase = `${promptsAbordagem[tipoAbordagem]}

Informações disponíveis:
- Nome do contato: ${nomeContato}
${empresa ? `- Empresa: ${empresa}` : ''}
- Produto/Serviço: ${contextoProduto}
${ultimaMensagem ? `- Última mensagem do cliente: "${ultimaMensagem}"` : ''}

IMPORTANTE:
- A mensagem deve despertar CURIOSIDADE e INTERESSE
- Evite parecer spam ou muito vendedor
- Use técnicas de copywriting e persuasão
- Personalize com as informações disponíveis
- Para trabalho OUTBOUND, a primeira impressão é crucial

Responda em formato JSON:
{
  "mensagem": "texto da mensagem de rapport",
  "confianca": 0.9,
  "tecnica": "breve descrição da técnica usada"
}`;

    return promptBase;
  }

  /**
   * Detecta o segmento do cliente baseado no contexto
   */
  private detectarSegmento(contexto: string, contato: any): string {
    const textoAnalise = (contexto + ' ' + (contato?.empresa || '')).toLowerCase();
    
    // Palavras-chave por segmento
    const segmentos = {
      'restaurante': ['restaurante', 'pizzaria', 'lanchonete', 'bar', 'café', 'padaria', 'delivery', 'comida', 'alimento', 'refeição', 'cardápio', 'mesa', 'garçom'],
      'varejo': ['loja', 'venda', 'produto', 'estoque', 'vitrine', 'cliente', 'compra', 'mercado', 'supermercado'],
      'servicos': ['serviço', 'consultoria', 'escritório', 'atendimento', 'agenda', 'consulta', 'projeto'],
      'industria': ['fábrica', 'produção', 'manufatura', 'industrial', 'processo', 'linha', 'montagem'],
      'saude': ['clínica', 'hospital', 'médico', 'paciente', 'consulta', 'exame', 'saúde', 'tratamento']
    };
    
    // Contar ocorrências
    let melhorSegmento = 'geral';
    let maiorPontuacao = 0;
    
    for (const [segmento, palavras] of Object.entries(segmentos)) {
      let pontuacao = 0;
      for (const palavra of palavras) {
        if (textoAnalise.includes(palavra)) {
          pontuacao++;
        }
      }
      if (pontuacao > maiorPontuacao) {
        maiorPontuacao = pontuacao;
        melhorSegmento = segmento;
      }
    }
    
    return melhorSegmento;
  }

  /**
   * Retorna mensagem de rapport fallback
   */
  private obterMensagemRapportFallback(params: {
    nomeContato?: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
  }): { mensagem: string; confianca: number } {
    const nome = params.nomeContato || 'você';
    const empresa = params.empresa;

    const mensagensFallback = {
      direta: [
        `Oi ${nome}! 👋 Vi que ${empresa ? `a ${empresa}` : 'sua empresa'} está crescendo. Tenho 3 insights que podem acelerar ainda mais esse crescimento. Posso compartilhar?`,
        `${nome}, empresas similares economizam 30% do tempo operacional com nossa solução. Vale 5min para descobrir como? 🚀`
      ],
      indireta: [
        `Oi ${nome}! Como estão as coisas por aí? 😊 Vi umas tendências interessantes no mercado que podem ser úteis para ${empresa || 'vocês'}...`,
        `${nome}, espero que esteja bem! Estava pensando nos desafios de gestão dessa época do ano. Como vocês têm lidado com isso?`
      ],
      consultiva: [
        `${nome}, notei que empresas ${empresa ? 'como a sua' : 'do seu porte'} enfrentam desafios com processos manuais. Qual área consome mais tempo na operação hoje?`,
        `Olá ${nome}! Tenho ajudado empresas a otimizar processos. Curioso: se pudesse automatizar UMA coisa ${empresa ? `na ${empresa}` : 'na empresa'}, o que seria?`
      ]
    };

    const mensagens = mensagensFallback[params.tipoAbordagem];
    const mensagemEscolhida = mensagens[Math.floor(Math.random() * mensagens.length)];

    return {
      mensagem: mensagemEscolhida,
      confianca: 0.7
    };
  }
}
