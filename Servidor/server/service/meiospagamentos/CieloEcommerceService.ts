import {IMeioPagamentoService} from "./IMeioPagamentoService";
import {Pedido} from "../../domain/delivery/Pedido";
import {PagamentoPedido} from "../../domain/delivery/PagamentoPedido";
import {StatusPagamentoDeParaCielo} from "../../lib/emun/EnumStatusPagamento";
import {Ambiente} from "../Ambiente";
import {PedidoService} from "../PedidoService";
import {NotificacaoMeioPagamentoService} from "../NotificacaoMeioPagamentoService";

const axios = require('axios');
const moment = require('moment');

const Erros = require('../../cielo-erros.json');

let credenciasSandbox  = {
  MerchantId: '4be48e43-207b-4502-886c-d6ca15d2f94f',
  MerchantKey: 'UHUDPMAOJUHTQMNDWNPPEGMZLYICSMHMONWZXANB'
}

export class CieloEcommerceService implements IMeioPagamentoService{
  params: any = {
     sandbox: {
        host:  'https://apisandbox.cieloecommerce.cielo.com.br' ,
        hostConsulta: 'https://apiquerysandbox.cieloecommerce.cielo.com.br'
     },
     producao: {
       host:  'https://api.cieloecommerce.cielo.com.br' ,
       hostConsulta: 'https://apiquery.cieloecommerce.cielo.com.br'
     }
  }
  private sandbox: boolean
  constructor(private merchantId: string, private merchantKey: string) {
    this.sandbox =  !Ambiente.Instance.producao;
  }

  executePost(path: string, payload: string){
    let url = this.obtenhaHost(path);
    return new Promise((resolve, reject) => {
      axios.post(url, payload, {
        headers: {
          "MerchantKey": this.sandbox ? credenciasSandbox.MerchantKey : this.merchantKey,
          "MerchantId": this.sandbox ? credenciasSandbox.MerchantId : this.merchantId,
          "Content-Type": "application/json"
        }
      }).then(   (response: any) => {
        resolve(response)
      }).catch( (request: any) => {
        reject(request)
      })
    });
  }

  executePut(path: string, payload: any = {}){
    let url = this.obtenhaHost(path);
    return new Promise((resolve, reject) => {
      axios.put(url, payload, {
        headers: {
          "MerchantKey": this.sandbox ? credenciasSandbox.MerchantKey : this.merchantKey,
          "MerchantId": this.sandbox ? credenciasSandbox.MerchantId : this.merchantId,
          "Content-Type": "application/json"
        }
      }).then(   (response: any) => {
        resolve(response)
      }).catch( (request: any) => {
        reject(request)
      })
    });
  }

  executeGetQuery(path: string, query: any = {}){
    let url = this.obtenhaHostConsulta(path);
    return new Promise((resolve, reject) => {
      axios.get(url, {
        headers: {
          "MerchantKey": this.sandbox ? credenciasSandbox.MerchantKey : this.merchantKey,
          "MerchantId": this.sandbox ? credenciasSandbox.MerchantId : this.merchantId,
          "Content-Type": "application/json"
        }
      }).then(   (response: any) => {
        resolve(response)
      }).catch( (request: any) => {
        reject(request)
      })
    });
  }


  executeGet(path: string, query: any = {}){
    let url = this.obtenhaHost(path);
    return new Promise((resolve, reject) => {
      axios.get(url, {
        headers: {
          "MerchantKey": this.sandbox ? credenciasSandbox.MerchantKey : this.merchantKey,
          "MerchantId": this.sandbox ? credenciasSandbox.MerchantId : this.merchantId,
          "Content-Type": "application/json"
        }
      }).then(   (response: any) => {
        resolve(response)
      }).catch( (request: any) => {
        reject(request)
      })
    });
  }

  crieTransacao(payloadPagamento: any): Promise<any>{
    return new Promise((resolve, reject) => {
      console.log(payloadPagamento)
      this.executePost('1/sales', payloadPagamento).then(   (response: any) => {
        console.log(response.data.Payment)
        resolve(response.data.Payment)
      }).catch( (request: any) => {
        reject(this.retornoErro('criar transação', request))
      })
    })
  }

  estornePagamento(idPagamento: string, valor: number = null){
    return new Promise<any>(async (resolve, reject) => {
      let transacion: any = await this.obtenhaTransacao(idPagamento);

      if(!transacion) return reject("Pagamento não existe: " + idPagamento);
      let payment = transacion.Payment;

      if(payment.Status === 10) //ja esta estornada
        return resolve(payment.Status);

      //?amount=${valor}

      this.executePut(String(`1/sales/${idPagamento}/void`)).then(   (response: any) => {
        //   Status": 10,
        //    "Tid": "0719094510712",
        //  "ProofOfSale": "4510712",
        //   "AuthorizationCode": "693066",
        // "ReturnCode": "9",
        //  "ReturnMessage": "Operation Successful",
        console.log(response.data)
        let codigoRetorno = Number(response.data.ReturnCode );

        if( codigoRetorno === 9 || codigoRetorno === 6 || Number(response.data.Status) === 10){
          //9 total, 6 parcial status 10 estornada
          resolve(response.data.Status) //10
        } else {
          let erro =  Erros[response.data.ReturnCode] ||  response.data.ReturnMessage;
          reject('Pagamento não estornado: ' + erro)
        }
      }).catch( (request: any) => {
        reject(this.retornoErro('estornar pagamento', request))
      })
    })
  }

  async sincronizePagamento(pedido: Pedido, pagamentoOnline: PagamentoPedido, empresa: any){
    let erro: string;
    if(pagamentoOnline.codigo) {
      let sale: any = await this.obtenhaTransacao(pagamentoOnline.codigo).catch((err) => {
        erro = err
      })

      if(sale){
        let novoStatus = StatusPagamentoDeParaCielo.get(Number(sale.Payment.Status))

        if(novoStatus.toString() !== pagamentoOnline.status.toString()){
          await pagamentoOnline.atualizeRetornoCielo(sale.Payment);
          pedido.empresa = empresa;
          await new PedidoService().mudouStatusPagamento(pedido, pagamentoOnline, novoStatus);
          pagamentoOnline.pedido = pedido;
          await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoOnline, empresa)
        }
      }
    }

    return erro;
  }
  obtenhaTransacao(id: string){
    return new Promise((resolve, reject) => {
      this.executeGetQuery('1/sales/' + id).then(   (response: any) => {
        console.log(response.data)
        resolve(response.data)
      }).catch( (request: any) => {
        reject(this.retornoErro('obter transação', request))
      })
    })
  }

  obtenhaCardBind(numero: any){
    return new Promise((resolve, reject) => {
      this.executeGet(String(`1/cardBin/${numero}`)).then(   (response: any) => {
        resolve(response.data)
      }).catch( (request: any) => {
        reject(this.retornoErro('validar bandeira do cartão', request))
      })
    })

  }

  obtenhaToken(dadosCartao: any) {
    return new Promise((resolve, reject) => {
      let payload: any = {
        CustomerName: dadosCartao.nome,
        CardNumber: dadosCartao.numero,
        Holder: dadosCartao.holder,
        ExpirationDate: moment(dadosCartao.validade).format('MM/YYYY'), //'25/2021'
        Brand: dadosCartao.bandeira //Visa / Master / Amex / Elo / Aura / JCB / Diners / Discover
      }

      if(!dadosCartao.nome) {
        let nomesNoCartao = dadosCartao.holder.split(' ');
        payload.CustomerName = nomesNoCartao[0].toUpperCase();

        if(nomesNoCartao.length > 1)
          payload.CustomerName = String(`${payload.CustomerName} ${nomesNoCartao[nomesNoCartao.length - 1]}`)
      }

      console.log(payload)
      this.executePost('1/card', payload).then((response: any) => {
        resolve(response.data.CardToken)
      }).catch((request: any) => {
        reject(this.retornoErro('Dados do cartão inválidos', request, ''))
      })
    })
  }

  private obtenhaHost(path: string) {
    let dados = this.sandbox ? this.params.sandbox :  this.params.producao ;

    return String(`${dados.host}/${path}`)
  }

  private   obtenhaHostConsulta(path: string) {
    let dados = this.sandbox ? this.params.sandbox :  this.params.producao ;

    return String(`${dados.hostConsulta}/${path}`)
  }

  private retornoErro(operacao: string, req: any, prefixo  = 'Nao foi possivel') {
    let detalhesErro = '';

    if(req.response){
      console.log('Erro na request '  + req.response.status)
      if(typeof req.toJSON === 'function')
        console.log(req.toJSON())

      if(req.response.data){
        console.log(req.response.data)
        if(typeof req.response.data === 'string')
          detalhesErro =  req.response.data
        else if(req.response.data.length){
          let erroData: any =  detalhesErro = req.response.data[0];
          detalhesErro = erroData.Code && Erros[ erroData.Code] ? Erros[ erroData.Code] : erroData.Message;
        }

      } else if (req.response.statusText){
        detalhesErro = req.response.statusText
      }
    } else if(req.message) detalhesErro = req.message;

    let erro = prefixo + ' ' + operacao +  ""

    if(detalhesErro) erro = erro + ": " + detalhesErro

    return erro.trim();
  }
}
