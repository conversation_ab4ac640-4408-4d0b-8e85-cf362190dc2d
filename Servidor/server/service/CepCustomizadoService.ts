import { MapeadorDeCepCustomizado } from '../mapeadores/MapeadorDeCepCustomizado';
import { CepCustomizado } from '../domain/CepCustomizado';

export class CepCustomizadoService {
  async busquePorCep(cep: string): Promise<CepCustomizado | null> {
    console.log(`Buscando CEP customizado: ${cep}`);

    const mapeador = new MapeadorDeCepCustomizado();

    // Buscar diretamente no banco usando a consulta SQL otimizada
    // A consulta SQL vai comparar tanto o CEP exato quanto o CEP sem formatação
    const resultado = await mapeador.selecioneSync({ cep: cep });

    if (resultado) {
      console.log('CEP encontrado na base customizada:', resultado);
      return resultado;
    }

    console.log('CEP não encontrado na base customizada');
    return null;
  }

  async insira(cepCustomizado: CepCustomizado): Promise<any> {
    const mapeador = new MapeadorDeCepCustomizado();
    return mapeador.insiraSync(cepCustomizado);
  }

  async atualize(cepCustomizado: CepCustomizado): Promise<any> {
    const mapeador = new MapeadorDeCepCustomizado();
    return mapeador.atualizeSync(cepCustomizado);
  }

  async remova(id: number): Promise<any> {
    const mapeador = new MapeadorDeCepCustomizado();
    return mapeador.removaAsync({ id });
  }
}
