import {FormaDeEntrega} from "../../domain/delivery/FormaDeEntrega";
import {Contato} from "../../domain/Contato";
import {Endereco} from "../../domain/delivery/Endereco";
import {Pedido} from "../../domain/delivery/Pedido";
import {EnumOrigemPedido} from "../../lib/emun/EnumOrigemPedido";
import {MapeadorDeContato} from "../../mapeadores/MapeadorDeContato";
import {Produto} from "../../domain/Produto";
import {MapeadorDeProduto} from "../../mapeadores/MapeadorDeProduto";
import {PagamentoPedido} from "../../domain/delivery/PagamentoPedido";
import {ItemPedido} from "../../domain/delivery/ItemPedido";
import {ValorDeAdicionaisEscolhaSimples} from "../../domain/delivery/ValorDeAdicionaisEscolhaSimples";
import {ValorDeAdicionaisMultiplaEscolha} from "../../domain/delivery/ValorDeAdicionaisMultiplaEscolha";
import {ListaOpcoesEscolhidas} from "../../domain/delivery/ListaOpcoesEscolhidas";
import {ValorDeOpcaoMultiplaEscolha} from "../../domain/delivery/ValorDeOpcaoMultiplaEscolha";
import {TipoDeContatoEnum} from "../../domain/TipoDeContatoEnum";
import {EnumDeParaMetodoPagamento} from "../../lib/integracao/pagamentos/EnumDeParaMetodoPagamento";
import {FormaDePagamento} from "../../domain/delivery/FormaDePagamento";
import {EnumStatusPagamento} from "../../lib/emun/EnumStatusPagamento";
import {IFoodService} from "./IFoodService";
import {IntegracaoIfood} from "../../domain/integracoes/IntegracaoIfood";
import {MapeadorDePedido} from "../../mapeadores/MapeadorDePedido";
import {PedidoBeneficio} from "../../domain/integracoes/PedidoBeneficio";
import {MapeadorDeFormaDePagamentoPdv} from "../../mapeadores/MapeadorDeFormaDePagamentoPdv";
import {EnumDisponibilidadeProduto} from "../../lib/emun/EnumDisponibilidadeProduto";
import {AdicionalDeProdutoMultiplaEscolha} from "../../domain/delivery/AdicionalDeProdutoMultiplaEscolha";
import {OpcaoDeAdicionalDeProduto} from "../../domain/delivery/OpcaoDeAdicionalDeProduto";
import {EnumTipoDeOrigem} from "../../lib/emun/EnumTipoDeOrigem";
import {ProdutoService} from "../ProdutoService";

export class IFoodUtils{
  static nomeIfoodOnline  = 'Ifood Online';
  static nomeIfoodLoja = 'Ifood Loja';
  constructor() {}

  static async obtenhaFormaPagamentoLoja(){
    let formaPagamento: any = await new MapeadorDeFormaDePagamentoPdv().selecioneSync({ nome: IFoodUtils.nomeIfoodLoja});

    return formaPagamento;
  }

  static async obtenhaFormaPagamentoOnline(){
    let formaPagamento: any = await new MapeadorDeFormaDePagamentoPdv().selecioneSync({ nome: IFoodUtils.nomeIfoodOnline});

    return formaPagamento;
  }

  static async novoPedido(order: any, empresa: any){
    let mapeadorProduto  = new MapeadorDeProduto(empresa.catalogo),
        mapeadorContato = new MapeadorDeContato();

    let erros: any = [];

    return new Promise(async (resolve, reject) => {
      let existente: any = await new MapeadorDePedido().selecioneTotalExterno( order.id);

      if(existente) return reject("Pedido já existe: " + order.id)

      /*order.payments.methods[0].method = "DIGITAL_WALLET";
      order.payments.methods[0].prepaid = true;
      order.payments.methods[0].type = "ONLINE";
      order.payments.methods[0].wallet =  {
        name: "MOVILE_PAY"
      };
      order.payments.methods[0].card = {
        brand: "MOVILE_PAY"
      } */
      console.log('novo pedido ifood:')
      console.log(JSON.stringify(order))
      try{
        if(order.orderType !== 'INDOOR'){
          let formaEntrega: any =  { id:  order.orderType === 'DELIVERY' ? FormaDeEntrega.ENTREGA : FormaDeEntrega.RETIRADA};
          let endereco: Endereco;
          let contato: Contato;
          const agendado = order.orderTiming === 'SCHEDULED';


          let dadosContato: any = order.customer,
            cpf = dadosContato.documentNumber ? dadosContato.documentNumber.replace(/\D/g, "") : null,
            nome = dadosContato.name,
            ifoodExtra: any = dadosContato.phone || {};

          if(cpf) ifoodExtra.cpfNota = cpf;
          if(order.displayId) ifoodExtra.displayId = order.displayId;

          contato = await mapeadorContato.selecioneDoIfood(dadosContato.id);

          if(cpf && !contato)
            contato = await mapeadorContato.selecioneSync({cpf: cpf})

          if(!contato){
            contato = new Contato(null, nome, null, null, null, cpf);
            contato.tipo = TipoDeContatoEnum.Ifood;
            contato.codigoIfood = dadosContato.id;
          }

          contato.empresa  = empresa;

          let observacoesEntrega: string;
          if(order.delivery){
            observacoesEntrega = order.delivery.observations;
            let deliveryAddress  = order.delivery.deliveryAddress;

            let horarioEntregaCliente = deliveryAddress.deliveryDateTime;

            endereco = Endereco.novoIfood(contato, deliveryAddress)
          }

          let pedido = new Pedido(empresa, null, contato, endereco, formaEntrega,
            0, 0, [], '', EnumOrigemPedido.Ifood);


          pedido.referenciaExterna = order.id;
          pedido.observacoes = order.extraInfo || '';
          pedido.idLojaExterna = order.merchant.id;

          if(order.delivery){
            pedido.ifoodCodigoRetirada = order.delivery.pickupCode;
            ifoodExtra.deliveredBy = order.delivery.deliveredBy;
          }

          pedido.pago = order.payments.pending > 0 ? false : true;

          if(order.additionalFees && order.additionalFees.length){
            pedido.ifoodTaxaServico = 0;
            order.additionalFees.forEach((taxa: any) => {
              pedido.ifoodTaxaServico += taxa.value;
            })
          }

          if(order.takeout){
            let horarioRetiradaPreviso = order.takeout.takeoutDateTime;
            let observations = order.takeout.observations;

            if(observations)
              pedido.observacoes += String(' Instruções retirada: ' + observations);
          }

          if(observacoesEntrega)
            pedido.observacoes += String(' Instruções entrega: ' + observacoesEntrega);

          if(agendado){
            if(order.schedule){ // agendado
              let horarioInicio = order.schedule.deliveryDateTimeStart;
              let horarioFim = order.schedule.deliveryDateTimeEnd;

              pedido.horarioEntregaAgendada = new Date(horarioInicio);
            }
          }


          order.payments.methods.forEach((methodInfo: any) => {
            // @ts-ignore
            let metodoPagamento = EnumDeParaMetodoPagamento[methodInfo.method];
            if(metodoPagamento){
              let valor = methodInfo.value, trocoPara = 0,  tipoPagamento = methodInfo.type ; // ONLINE, OFFLINE
              let bandeira =  methodInfo.card ? methodInfo.card.brand : null;

              let formaDePagamento: FormaDePagamento;

              let formasDePagamentoIfoodDoTipo: any = empresa.formasDePagamento.filter(
                (item: any) => item.formaDePagamentoPdv &&
                  item.formaDePagamentoPdv.tipo === tipoPagamento && item.nome.startsWith('ifood'));

              if(formasDePagamentoIfoodDoTipo.length){
                if(methodInfo.cash){
                  trocoPara =  methodInfo.cash.changeFor  || 0;
                  bandeira = 'dinheiro'
                }

                let online = tipoPagamento === 'ONLINE';

                if(online){
                  formaDePagamento = formasDePagamentoIfoodDoTipo.length ? formasDePagamentoIfoodDoTipo[0] : null
                } else {
                  formaDePagamento = formasDePagamentoIfoodDoTipo.find((item: any) =>
                    item.bandeira && IFoodUtils.mesmoNome(item.bandeira.nome, bandeira) );
                }

                if(formaDePagamento){
                  let pagamento = new PagamentoPedido(pedido, valor, trocoPara, formaDePagamento);

                  if(methodInfo.prepaid)
                    pagamento.status = EnumStatusPagamento.Aprovado

                  if(online)
                    pagamento.bandeira  = bandeira

                  pedido.pagamentos.push(pagamento)
                  pedido.taxaFormaDePagamento = 0
                } else {
                  erros.push( String(`Habilite a forma de pagamento Ifood "${tipoPagamento} - ${bandeira}"`))
                }
              } else {
                erros.push('Habilite a forma de pagamento Ifood  do tipo: ' +  tipoPagamento)
              }
            } else {
              erros.push('Metodo de pagamento Ifood não esperado: ' + methodInfo.method)
            }
          })

          for(let i = 0; i < order.items.length; i++){
            let item: any =  order.items[i];
            //importar pedidos testes
            // if(!item.externalCode && item.name.startsWith('PEDIDO DE TESTE'))
            //  item.externalCode = '9999'
            let produto: Produto  =  await mapeadorProduto.selecioneSync({idIfood: item.id, ocultos: true})

            if(!produto && item.externalCode){
              produto =  await mapeadorProduto.selecioneSync({id: item.externalCode})

              if(!produto  )
                produto =  await mapeadorProduto.selecioneSync({codigoPdv: item.externalCode})
            }

            if(!produto) {
              let errImportar: string;
              produto = await IFoodUtils.novoProduto(item, empresa.catalogo).catch((err) => {
                  errImportar = err
              })

              if(errImportar){
                 console.log(errImportar)
                 erros.push(String(`Produto não encontrado: "${( item.externalCode || item.id )} - ${item.name}". Falha importaçao automatica: ${errImportar}`))
                 continue;
              }
            }

            let valorUnitario = item.unitPrice,
              valorAdicionais = item.optionsPrice,
              total = item.totalPrice;

            produto.preco = valorUnitario;
            produto.novoPreco = null;

            let itemPedido = new ItemPedido(produto, valorUnitario,  item.quantity, item.observations)

            if(item.options && item.options.length){
              let index = 0, indiceLista = 0, indiceCampo = 0;
              let adicionaisEscolhaSimples: ValorDeAdicionaisEscolhaSimples = null;
              let adicionaisMultiplaEscolha: ValorDeAdicionaisMultiplaEscolha  = null;

              for(let idxopcao = 0; idxopcao <  item.options.length; idxopcao++){

                let option = item.options[idxopcao],
                  codigo = option.externalCode,
                  idIfood = option.id,
                  nomeOpcao = option.name,
                  qtdeOpcao = option.quantity,
                  valorUnitarioOpcao = option.unitPrice,
                  valorTotalOpcao = option.price;

                let opcaoNoProduto: any, adicionalNoProduto: any;

                if(produto.camposAdicionais){
                  for(let idxadicional = 0; idxadicional < produto.camposAdicionais.length; idxadicional++){
                    let campoAdicional = produto.camposAdicionais[idxadicional];

                    if(qtdeOpcao > 1 && campoAdicional.tipo !== "multipla-escolha") continue;

                    let opcaoDisponivel = campoAdicional.opcoesDisponiveis.find((opcao: any) =>
                      opcao.idIfood && opcao.idIfood === idIfood);

                    if(!opcaoDisponivel && codigo){
                      opcaoDisponivel = campoAdicional.opcoesDisponiveis.find((opcao: any) =>
                        (opcao.codigoPdv && opcao.codigoPdv === codigo.toString()) || opcao.id === Number(codigo));
                    }

                    if(!opcaoDisponivel)
                      opcaoDisponivel = campoAdicional.opcoesDisponiveis.find((opcao: any) => this.mesmoNome(opcao.nome , nomeOpcao));

                    if(opcaoDisponivel){
                      adicionalNoProduto = campoAdicional;
                      opcaoNoProduto = opcaoDisponivel;
                      break;
                    }
                  }

                  if(!opcaoNoProduto){
                    adicionalNoProduto =
                      produto.camposAdicionais.find((campo: any) => campo.nome.toUpperCase() === option.groupName.toUpperCase())

                    if(!adicionalNoProduto){
                      //criar so adicional inteiro dentro adicional
                      adicionalNoProduto =  await IFoodUtils.novoAdicionalProduto(option, produto)
                      opcaoNoProduto = adicionalNoProduto.opcoesDisponiveis[0];
                    } else {
                      //criar so opçao dentro adicional
                      opcaoNoProduto = await IFoodUtils.novaOpcaoAdicional(produto, adicionalNoProduto, option)
                    }
                  }
                }

                if(opcaoNoProduto){
                  opcaoNoProduto.ordem = index;
                  //setar valor veio ifood salvar tabela produto_vendido
                  opcaoNoProduto.valor = valorUnitarioOpcao;
                  opcaoNoProduto.total = valorTotalOpcao;

                  if( adicionalNoProduto.tipo === 'multipla-escolha'){
                    let nomeCampo = 'lista' + indiceLista++

                    if(!adicionaisMultiplaEscolha)
                      adicionaisMultiplaEscolha = new ValorDeAdicionaisMultiplaEscolha()

                    let listaOpcoes: ListaOpcoesEscolhidas = new ListaOpcoesEscolhidas();

                    listaOpcoes.opcoes = []
                    listaOpcoes.tipoDeCobranca = adicionalNoProduto.tipoDeCobranca;
                    listaOpcoes.ordem = opcaoNoProduto.ordem;

                    let valorOpcao = new ValorDeOpcaoMultiplaEscolha();

                    valorOpcao.opcao = opcaoNoProduto
                    valorOpcao.qtde = qtdeOpcao

                    listaOpcoes.opcoes.push(valorOpcao)

                    adicionaisMultiplaEscolha[nomeCampo as keyof ValorDeAdicionaisMultiplaEscolha] = listaOpcoes


                  } else {
                    if(!adicionaisEscolhaSimples)
                      adicionaisEscolhaSimples = new ValorDeAdicionaisEscolhaSimples()

                    let nomeCampo = 'campo' + indiceCampo++

                    adicionaisEscolhaSimples[nomeCampo as keyof ValorDeAdicionaisEscolhaSimples] = opcaoNoProduto


                  }
                } else {
                  erros.push(String(`Opçao "${codigo} - ${option.name}" não encontrada no produto ${produto.nome}`))
                }
              }

              itemPedido.adicionaisEscolhaSimples = adicionaisEscolhaSimples
              itemPedido.adicionaisMultiplaEscolha = adicionaisMultiplaEscolha
            }

            itemPedido.valor = valorUnitario;
            itemPedido.total = total

            pedido.itens.push(itemPedido)

          }

          pedido.taxaEntrega = order.total.deliveryFee;
          pedido.valor = order.total.subTotal

          if( order.benefits){
            order.benefits.forEach((beneficio: any) => {
              let desconto: number =    beneficio.value ;

              beneficio.sponsorshipValues.forEach((sponsorship: any) => {
                if(sponsorship.value > 0){
                  let beneficioPedido = new PedidoBeneficio(sponsorship.value, sponsorship.name, beneficio.target)

                  pedido.beneficios.push(beneficioPedido)
                }
              })

              if (beneficio.target === 'DELIVERY_FEE'){
                pedido.descontoTaxaEntrega += desconto;
                pedido.taxaEntrega -= desconto;
              } else {
                //beneficio.target === 'CART'
                pedido.desconto += desconto;
                pedido.valor -= desconto;
              }
            })
          }

          if(order.total.orderAmount !== pedido.obtenhaTotal())
            erros.push('Total do pedido inválido: ' + pedido.obtenhaTotal() )

          if(erros.length) return reject(erros.join(', '))

          if(empresa.integracaoPedidoFidelidade)
            await pedido.calculePontuacaoFidelidade(empresa.integracaoPedidoFidelidade)

          pedido.ifoodExtra = ifoodExtra ? JSON.stringify(ifoodExtra) : null;

          resolve(pedido)
        }
      } catch (err){
        console.error(err)
        reject('Erro ao processar novo pedido: ' + err)
      }
    });
  }


  static  novoProduto(item: any, catalogo: any): Promise<any>{
    return new Promise(async (resolve: any, reject: any) => {
      let produto: Produto = new Produto(null, item.name, item.unitPrice, '', null,
        null, catalogo, false, EnumDisponibilidadeProduto.Oculto )

      produto.origem = EnumTipoDeOrigem.ImportadoIfood;
      produto.codigoPdv = item.externalCode;
      produto.idIfood = item.id; //uniqueId

      if(item.options && item.options.length){
        item.options.forEach((option: any) => {
          IFoodUtils.novoAdicional(option, produto)
        })
      }

      await new ProdutoService().salveProdutoImportado(produto).catch((erroImportar) => {
         reject(erroImportar)
      });

      if(produto.id)
        resolve(produto)
    })

  }

  static async novoAdicionalProduto(option: any, produto: Produto){
    let adicional: any = IFoodUtils.novoAdicional(option, produto);

    await new ProdutoService().insiraAdicionalProduto(produto.catalogo, produto, adicional);

    return adicional;
  }

  static async novaOpcaoAdicional(produto: any, adicional: any, option: any){
    let opcao: any = IFoodUtils.novaOpcao(option, adicional)

    await new ProdutoService().salveOpcaoAdicional(produto, adicional, opcao, false, false, produto.empresa);

    return opcao;
  }

  static novoAdicional(option: any, produto: Produto){

    let adicional: any =  produto.camposAdicionais.find((item: any) => item.nome === option.groupName )

    if(!adicional){
      adicional =  new AdicionalDeProdutoMultiplaEscolha(option.groupName, false, [], 0, 10, true)
      produto.camposAdicionais.push(adicional)

      adicional.oculto = true;

    }

   IFoodUtils.novaOpcao(option, adicional)

   return adicional;
  }

  static novaOpcao(option: any, adicional: any){
    let opcao: any = new OpcaoDeAdicionalDeProduto(option.name, option.unitPrice, true, '');

    opcao.codigoPdv = option.externalCode || '';

    opcao.idIfood = option.id;
    opcao.oculta = true;

    adicional.opcoesDisponiveis.push(opcao);

    return opcao;
  }

  static mesmoNome(str1: string, str2: string) {
    // Remover espaços em branco e converter para minúsculas
    const string1 = str1 ? str1.replace(/\s/g, '').replace(/_/g, '').toLowerCase() : null;
    const string2 = str2 ? str2.replace(/\s/g, '').replace(/_/g, '').toLowerCase() : null;

    // Comparar as strings resultantes
    return string1 === string2;
  }

  static async executePollingLojas(){
    let service = new IFoodService(new IntegracaoIfood(null));

    let resposta: any = await service.facaPollingEventos();

    console.log(resposta)

    return resposta;
  }
}
