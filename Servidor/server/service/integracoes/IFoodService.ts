import {IntegracaoIfood} from "../../domain/integracoes/IntegracaoIfood";
import * as querystring from "querystring";
import {DeParaEventsStatusPedido, EventsIfoodDeliveryEnum, EventsIfoodEnum} from "./EventsIfoodEnum";
import {NotificacaoIfood} from "../../domain/integracoes/NotificacaoIfood";
import {ExecutorAsync} from "../../utils/ExecutorAsync";
import {MapeadorDeNotificacaoIfood} from "../../mapeadores/MapeadorDeNotificacaoIfood";
import {NotificacaoPedidoService} from "../NotificacaoPedidoService";
import {OrderEvent} from "../../domain/opendelivery/OrderEvent";
import {CacheService} from "../CacheService";
import * as moment from "moment";
import {MapeadorDePedido} from "../../mapeadores/MapeadorDePedido";
import { createClient } from 'redis';
import {Pedido} from "../../domain/delivery/Pedido";
import {DTOOrderIfood} from "../../lib/integracao/ifood/DTOOrderIfood";
import {DeliveryPedidoIfood} from "../../domain/integracoes/DeliveryPedidoIfood";
import {DeliveryPedido} from "../../domain/integracoes/DeliveryPedido";
import {NotificacaoIfoodDelivery} from "../../domain/integracoes/NotificacaoIfoodDelivery";
import {MapeadorDeNotificacaoDelivery} from "../../mapeadores/MapeadorDeNotificacaoDelivery";
const Redlock = require('redlock');
// Crie o cliente Redis
const redisClient = createClient();

// Crie o Redlock
// @ts-ignore
const redlock = new Redlock(
  // @ts-ignore
  [redisClient],
  {
    retryCount: 30,
    retryDelay: 500, // tempo em ms
  }
);
const axios = require('axios');
declare const global: any;

const fiboAppTeste: any = {
  client: 'e03d78a7-9f03-4ccf-86d5-f496a20fe3b9',
  secret: '39dz2bv56bmmvn97919wkszuas697pck1wspps5nv9ih7kgivw5ytvdobzi0qmc6z9hhao698upn7jmvg2s662ijcb1mp8mj2wk',
  lojaTeste: {
    id: "5d9c5238-efe4-4220-ae71-4294c4059e1c",
    nome: "Teste - Fibonacci Soluções Ágeis",
    emailDev: '<EMAIL>',
    link: "https://www.ifood.com.br/delivery/bujari-ac/teste---fibonacci-solucoes-ageis-bujari/5d9c5238-efe4-4220-ae71-4294c4059e1c",
  }
}

const fiboAppTesteDistribuido: any = {
  client: "0cb069c3-f0de-493f-b898-e837544f0006",
  secret: "v7k40nauu3ybu9g6tsj0vor411g5e8t09937u64gtnnos8hc3ptv2pr1tddzrd6v33ihbgarn0vxc2plyss4g0co24pfxtl7k3o"
}

const enderecoTeste: any = {
  coordinates: {
    latitude: -9.818888,
    longitude: -67.946676
  }
}

export class IFoodService{
  instance: any;
  //credencais Meucardapio centralizado homologado.
  baseUrl = 'https://merchant-api.ifood.com.br'
  clientId = '45fcc90a-8fd4-434f-b653-562b117049d6';
  clientSecret = 'cks4oq4iebwc0boqmy2uwzqg39gdx7y9uvv96gjbkqwwhmtj72k0lvdstl15ou9mzsqw7snrhga24g8opbgzn7xofo7rp8z0zka';
  clientIdDistribuido = 'c509ce5e-bd9c-4296-a106-4ce7d94bfbfd'
  clientSecretDistribuido = 'iteegij1qnd84vg2t7o5ej6heghqskrwq3mmt9jc8o6wpo6kwuot4tm7uuwokf7dky0tdq5pr3pps5wilkfbpq1nlsn7cg9q94h'
  constructor(public integracaoIfood: IntegracaoIfood) {
    let lojaHomologacao = integracaoIfood && integracaoIfood.idLoja === fiboAppTeste.lojaTeste.id;

    if(global.desenvolvimento || lojaHomologacao){
      console.log('**IFood Modo Teste**')
      this.clientId =  fiboAppTeste.client;
      this.clientSecret = fiboAppTeste.secret;

      this.clientIdDistribuido =  fiboAppTesteDistribuido.client;
      this.clientSecretDistribuido = fiboAppTesteDistribuido.secret;
    }

    this.instance = axios.create({
      baseURL: this.baseUrl,
      timeout: 30 * 1000
    });

  }

  static async  fechePedidosEmAberto(){
    let mapeador = new MapeadorDePedido();
    mapeador.desativeMultiCliente();
    let horarioCorte = moment().add(-7, 'd').startOf('d').format('YYYY-MM-DD HH:mm:ss')
    console.log({horarioCorte: horarioCorte})
    await mapeador.finalizePedidoIfoodEmAberto(horarioCorte);
    console.log('executou fechar pedidos: ' + new Date());
  }


  valideEmpresa(){
    return new Promise(async (resolve) => {
      let erro: any;
      let merchant: any = await this.obtenhaLoja(this.integracaoIfood.idLoja).catch((_erro) => erro = _erro);

      if(merchant && merchant.id) return resolve(null)

      resolve(erro || 'Loja ainda não autorizada')
    })

  }

  obtenhaTokenPorAutorizacao() {
    return new Promise(async (resolve, reject) => {
      if( this.integracaoIfood.tokenValido())
        return resolve(this.integracaoIfood.token);

      const data = {
        grantType: 'authorization_code',
        clientId: this.clientIdDistribuido,
        clientSecret: this.clientSecretDistribuido,
        authorizationCode: this.integracaoIfood.codigoAutorizacao,
        authorizationCodeVerifier: this.integracaoIfood.verificadorCodigoAutorizacao,
      };

      // Configurando o cabeçalho para x-www-form-urlencoded
      const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
      };

      console.log(data)

      this.instance.post('/authentication/v1.0/oauth/token',  querystring.stringify(data), { headers })
        .then( async (response: any) => {
          await this.integracaoIfood.setToken(response.data);
          resolve(this.integracaoIfood.token)
        }).catch( (error: any) => {
        reject(this.retorneErro(error, 'obter token'))
      });
    });
  }

  obtenhaToken() {
    return new Promise(async (resolve, reject) => {
       if(this.integracaoIfood.tokenValido())
         return resolve(this.integracaoIfood.token);

        // Configurando os dados que serão enviados no corpo da requisição
        const data: any = {
          grantType: 'client_credentials',
          clientId: this.clientId,
          clientSecret: this.clientSecret,
        };

      if(this.integracaoIfood.distribuida()){
        data.grantType = "refresh_token";
        data.clientId = this.clientIdDistribuido;
        data.clientSecret = this.clientSecretDistribuido;
        data.refreshToken = this.integracaoIfood.refreshToken;
      }

        // Configurando o cabeçalho para x-www-form-urlencoded
        const headers = {
          'Content-Type': 'application/x-www-form-urlencoded',
        };

        this.instance.post('/authentication/v1.0/oauth/token',  querystring.stringify(data), { headers })
          .then( async (response: any) => {
            await this.integracaoIfood.setToken(response.data);
            resolve(this.integracaoIfood.token)
        }).catch( (error: any) => {
             let naoAutorizado = error.response.status === 401;
             if(naoAutorizado)
                return reject('Loja não autorizada a renovar o token');

            reject(this.retorneErro(error, 'obter token'))
        });
    })
  }

  gereCodigoDeVinculo(){
    return new Promise(async (resolve, reject) => {
      // Configurando os dados que serão enviados no corpo da requisição
      const data = {
        clientId: this.clientIdDistribuido
      };

      // Configurando o cabeçalho para x-www-form-urlencoded
      const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
      };

      this.instance.post('/authentication/v1.0/oauth/userCode',  querystring.stringify(data), { headers })
        .then( async (response: any) => {
          resolve(response.data)
        }).catch( (error: any) => {
        reject(this.retorneErro(error, 'obter token'))
      });
    })
  }

  obtenhaLojasDoToken(){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        this.instance.get(String(`/merchant/v1.0/merchants`), this.getHeader())
          .then((response: any) => {
            if(response.status === 200) return  resolve(response.data)

            reject( this.retorneErro(response, 'obter loja'))
          }).catch((resp: any) => {
          if(resp.response && resp.response.status === 404)
            return resolve(null)

          reject(this.retorneErro(resp, 'obter loja'))
        }) ;
      }
    });
  }

  obtenhaLoja(idLoja: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        this.instance.get(String(`/merchant/v1.0/merchants/${idLoja}`), this.getHeader())
          .then((response: any) => {
            if(response.status === 200) return  resolve(response.data)

            reject( this.retorneErro(response, 'obter loja'))
          }).catch((resp: any) => {
            if(resp.response && resp.response.status === 404)
              return resolve(null)

            reject(this.retorneErro(resp, 'obter loja'))
        }) ;
      }
    })
  }

  obtenhaImagem(order: string, idEvidencia: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        let url = String(`/order/v1.0/orders/${order}/cancellationEvidences/${idEvidencia}`)

        console.log(url)
        this.instance.get(url,  {    headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/octet-stream',
          },
          responseType: 'arraybuffer',
        }) .then((response: any) => {
            if(response.status === 200) return  resolve(response.data)


            reject( this.retorneErro(response, 'obter imagem'))
          }).catch((resp: any) => {
          if(resp.response && resp.response.status === 404)
            return resolve(null)

          reject(this.retorneErro(resp, 'obter imagem'))
        }) ;
      }
    })
  }

  listeFormasPagamentos(){
    Promise.resolve([])
  }

  facaAckDasNotificacoes(notificacoes: any = []){
    return new Promise(async (resolve, reject) => {
      console.log('fazer acknowledgment das notificaçoes: ' + notificacoes.length)
      let token: any = await this.obtenhaToken().catch((erro) => {
         console.log('Falha ao obter token ifood')
         console.error(erro)
         reject(erro)
      });


      let events = notificacoes.map((item: any) => ({ id: item.eventId || item.id }));
      if(token){
        console.log('Fazer acknowledgment dos events:')
        console.log(events)
        this.instance.post("/order/v1.0/events/acknowledgment", events, this.getHeaderJson())
          .then((response: any) => {
            resolve(null);
          }).catch((resp: any) => {
            let erro = this.retorneErro(resp, 'ack events');
            console.error(erro)
            resolve(null)
        }) ;
      } else {
        console.log('Token nao obtido...')
      }
    })
  }


  obtenhaNovosEventos(merchants: Array<string> = null){
      return new Promise(async (resolve, reject) => {
        //proteção para  de ping com token do centralizado sem loja nenhum informada ( abre todas as lojas)
        if(!this.integracaoIfood.distribuida() && !merchants) return Promise.reject('Nenhum merchant informado')

        let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

        if(token){
          this.instance.get(String(`/order/v1.0/events:polling`), this.getHeader(merchants))
            .then((response: any) => {
              if(response.status === 204) return  resolve([])
              if(response.status === 200) return  resolve(response.data)

              reject( this.retorneErro(response, 'obter novos eventos'))
            }).catch((resp: any) => {
            reject(this.retorneErro(resp, 'obter novos eventos'))
          }) ;
        }
    });
  }

  obtenhaPedido(id: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        this.instance.get(String(`/order/v1.0/orders/${id}`), this.getHeader())
          .then((response: any) => {
            if(response.status === 200) return  resolve(response.data)

            reject( this.retorneErro(response, 'obter pedido'))
          }).catch((resp: any) => {
            if(resp.response && resp.response.status === 404) return resolve(null);

          reject(this.retorneErro(resp, 'obter pedido'))
        }) ;
      }
    });
  }

  obtenhaStatusLoja(merchant_id: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        this.instance.get(String(`/merchant/v1.0/merchants/${merchant_id}/status/delivery`), this.getHeader())
          .then((response: any) => {
            if(response.status === 200) return  resolve(response.data[0]);

            reject( this.retorneErro(response, 'obter status loja'))
          }).catch((resp: any) => {

          reject(this.retorneErro(resp, 'obter status loja'))
        }) ;
      }
    });
  }

  confirmePedido(id: string){
    console.log('confirmar pedido ifood: ' + id);
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        let url  = String(`/order/v1.0/orders/${id}/confirm`);
        console.log(url)
        this.instance.post(url, {}, this.getHeader())
          .then((response: any) => {
            if(response.status === 200 || response.status === 202)
              return  resolve(true)

            reject( this.retorneErro(response, 'confirmar pedido'))
          }).catch((resp: any) => {
          reject(this.retorneErro(resp, 'confirmar pedido'))
        }) ;
      }
    });
  }

  obtenhaCotacaoDaEntregaDaOrder( orderId: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        let url  = String(`/shipping/v1.0/orders/${orderId}/deliveryAvailabilities`);
        console.log(url)
        this.instance.get(url, this.getHeaderJson())
          .then((response: any) => {
            if(response.status === 200 || response.status === 202 ){
              console.log(response.data)
              return  resolve(response.data)
            }
            reject( this.retorneErro(response, 'consultar develiry availabilities'))
          }).catch((resp: any) => {
            if(resp.status === 400 && resp.data.message)
               return reject(resp.data.message)

          reject(this.retorneErro(resp, 'consultar develiry availabilities'))
        }) ;
      }
    });
  }

  obtenhaCotacaoDaEntrega(merchant_id: string,  endereco: any){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        let cordenadas = endereco.localizacao.split(',');

        let lat = Number(cordenadas[0])
        let long = Number(cordenadas[1])

        if(merchant_id === fiboAppTeste.lojaTeste.id){
          console.log('setar localização endereço teste ifood')
          lat = enderecoTeste.coordinates.latitude
          long = enderecoTeste.coordinates.longitude
        }


        const query = `latitude=${lat}&longitude=${long}`;
        let url  = String(`/shipping/v1.0/merchants/${merchant_id}/deliveryAvailabilities?${query}`);

        console.log(url)
        this.instance.get(url, this.getHeaderJson())
          .then((response: any) => {
            if(response.status === 200 ){
               console.log(response.data)
              return  resolve(response.data)
            }
            reject( this.retorneErro(response, 'obter cotação'))
          }).catch((resp: any) => {

          reject(this.retorneErro(resp, 'obter cotação'))
        }) ;
      }
    })
  }

  setFormaPagamento(pedido: any, empresa: any, cotacao: any){
    let erroPagamento: string;

    let pagamentosEnviar = pedido.pagamentos.filter((item: any) => !item.pagoPorPlanoFidelidade() );

    let metodosPagamentos = cotacao.paymentMethods || [];

    pagamentosEnviar.forEach((pagamento: any) => {
      let formaDePagamento = empresa.formasDePagamento.find((item: any) => item.id === pagamento.formaDePagamento.id)

      if(!formaDePagamento.online  && !formaDePagamento.pixManual()){
        let metodoPagamento = metodosPagamentos.find((item: any) => formaDePagamento.compativelComIfood(item));

        if (metodoPagamento) {
          pagamento.ifood = metodoPagamento;
        } else {
          let descricaoPagamento = formaDePagamento.getDescricaoPeloMetodo() ;
          let disponiveis = metodosPagamentos.map((item: any) => `${item.paymentType} ${item.method} ${item.brand || ''} `);
          erroPagamento = `Pagamento presencial não disponível: "${descricaoPagamento}", disponiveis são: ${disponiveis.join(', ')}`;
        }
      } else {
       /* let pix = formaDePagamento.pix || formaDePagamento.pixManual();
        let onlinesDisponveis  =  metodosPagamentos.filter((item: any) => item.paymentType ===  'ONLINE' );
        let metodoPagamento =  metodosPagamentos.find((item: any) => item.method ===  pix ? 'PIX' : "CREDIT");

        if (metodoPagamento)
          pagamento.ifood = metodoPagamento;
        else {
          let descricaoPagamento =  pix ? 'PIX' : 'Cartão';
          erroPagamento = `Pagamento online não disponível: "${descricaoPagamento}"`;
          if(onlinesDisponveis.length)  erroPagamento = `${erroPagamento}. Disponiveis são: ${onlinesDisponveis.join(', ')}`;
        } */
      }
    })

    if (erroPagamento)  return  erroPagamento;

  }

  private async soliciteEntregadorPedidoMeucardapio(empresa: any, operador: any, idLoja: string, pedido: Pedido): Promise<DeliveryPedido>{
    return new Promise(async (resolve, reject) => {
      let cotacao: any = pedido.deliveryPedido ? pedido.deliveryPedido.getCotacao() : null;

      if(!cotacao) {
        cotacao = await this.obtenhaCotacaoDaEntrega(idLoja, pedido.endereco).catch((erroEntrega) => {
          reject(erroEntrega)
        });
      }

      if(cotacao){
        let erroPagamento: any = this.setFormaPagamento(pedido, empresa, cotacao)

        if(erroPagamento) return reject(erroPagamento)

        let order: any = await this.criePedidoExterno(idLoja, pedido, cotacao.id).catch((errPedido) => {
          reject( errPedido)
        });

        if(order){
          pedido.referenciaExterna = order.id;
          await new MapeadorDePedido().atualizeReferenciaExterna(pedido);
          let novoDevivery = new DeliveryPedidoIfood(pedido, operador, order);
          await novoDevivery.salve();
          resolve(novoDevivery)
        }
      }
    })
  }

  private async  soliciteEntregadorPedidoIfood(pedido: Pedido, operador: any): Promise<DeliveryPedido>{
    return new Promise(async (resolve, reject) => {
      let orderId: string = pedido.referenciaExterna;
      let cotacao: any = pedido.deliveryPedido ? pedido.deliveryPedido.getCotacao() : null;


      if(!cotacao){
        cotacao = await this.obtenhaCotacaoDaEntregaDaOrder(orderId).catch((erroEntrega) => {
          reject( erroEntrega)
        });
      }

      if(cotacao){
        let url  = String(`/shipping/v1.0/orders/${orderId}/requestDriver`);
        const dados: any =  {quoteId: cotacao.id};
        console.log(url);
        console.log(dados);

        this.instance.post(url, dados, this.getHeaderJson()).then(async (response: any) => {
          if(response.status === 200 || response.status === 202){
            let order: any = { id: orderId};
            let novoDevivery = new DeliveryPedidoIfood(pedido, operador, order);
            await novoDevivery.salve();
            return  resolve(novoDevivery)
          }
          reject( this.retorneErro(response, 'solicitar entregador'))
        }).catch((resp: any) => {
          reject(this.retorneErro(resp, 'solicitar entregador'))
        }) ;
      }
    })
  }

  async facaCotacaoEntregaDoPedido(empresa: any, operador: any, loja: any, pedido: Pedido): Promise<DeliveryPedido> {
    return new Promise(async (resolve, reject) => {
      let cotacao: any;

      if(pedido.doIfood()){
        let orderId: string = pedido.referenciaExterna;
        cotacao = await this.obtenhaCotacaoDaEntregaDaOrder(orderId).catch((erroEntrega) => {
          reject( erroEntrega)
        });
      } else {
        cotacao = await this.obtenhaCotacaoDaEntrega(loja.id, pedido.endereco).catch(
          (erroEntrega) => {  reject( erroEntrega)
        });

        if(cotacao){
          //tiver com falha metodo pagamento nao suportado
          let erroPagamento = this.setFormaPagamento(pedido, empresa, cotacao)

          if(erroPagamento) return reject(erroPagamento)
        }
      }

      if(cotacao){
        cotacao.loja = loja  ;
        let novoDevivery = new DeliveryPedidoIfood(pedido, operador, null , cotacao);
        await novoDevivery.salve();
        resolve(novoDevivery)
      }
    })
  }

  async notifiqueNovaEntregaPedido(empresa: any, operador: any, idLoja: string, pedido: Pedido): Promise<DeliveryPedido>{
    //ja tem solicitaçao aceita...
    if(pedido.deliveryPedido && pedido.deliveryPedido.foiAceita) return Promise.resolve(pedido.deliveryPedido)

    if(pedido.doIfood()) return this.soliciteEntregadorPedidoIfood(pedido, operador)

    return this.soliciteEntregadorPedidoMeucardapio(empresa, operador, idLoja, pedido)
  }

  canceleEntregador(orderId: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if (token) {
        let url  = String(`/shipping/v1.0/orders/${orderId}/cancelRequestDriver`);
        console.log(url);
        this.instance.post(url, {}, this.getHeaderJson()).then((response: any) => {
          if(response.status === 200 || response.status === 202)
            return  resolve(true)
          reject( this.retorneErro(response, 'cancelar entregador'))
        }).catch(async (resp: any) => {
          reject(this.retorneErro(resp, 'cancelar entregador'))
        }) ;
      }
    });
  }

  criePedidoExterno(merchant_id: string, pedido: Pedido, cotacaoId: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        const dtoOrder: any = new DTOOrderIfood(pedido, cotacaoId);

        if(merchant_id === fiboAppTeste.lojaTeste.id){
          console.log('setar localização endereço teste ifood')
          dtoOrder.delivery.deliveryAddress.coordinates = enderecoTeste.coordinates
        }

        let url  = String(`/shipping/v1.0/merchants/${merchant_id}/orders`);
        console.log(url)
        console.log(JSON.stringify(dtoOrder))

        this.instance.post(url, dtoOrder, this.getHeaderJson()).then(async (response: any) => {
          if(response.status === 200 || response.status === 202){
            let order: any = response.data;
            console.log(order)
            await this.confirmePedido(order.id).catch((err) => {
              console.log(err)
            });
            return  resolve(order)
          }
          reject( this.retorneErro(response, 'criar pedido no ifood'))
        }).catch((resp: any) => {
          reject(this.retorneErro(resp, 'criar pedido no ifood'))
        }) ;
      }

    })
  }

  aceiteMudancaEnderecoEntrega(orderId: string){

    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        let url  = String(`/shipping/v1.0/orders/${orderId}/acceptDeliveryAddressChange`);
        console.log(url);
        this.instance.post(url, {}, this.getHeaderJson()).then((response: any) => {
          if(response.status === 200 || response.status === 202)
            return  resolve(true)

          reject( this.retorneErro(response, 'aceitar delivery change'))
        }).catch((resp: any) => {
          reject(this.retorneErro(resp, 'aceitar delivery changer'))
        }) ;
      }
    });

  }

  rejeiteMudancaEnderecoEntrega(orderId: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        let url  = String(`/shipping/v1.0/orders/${orderId}/denyDeliveryAddressChange`);
        console.log(url);
        this.instance.post(url, {}, this.getHeaderJson()).then((response: any) => {
          if(response.status === 200 || response.status === 202)
            return  resolve(response.data)

          reject( this.retorneErro(response, 'rejeitar delivery change'))
        }).catch((resp: any) => {
          reject(this.retorneErro(resp, 'rejeitar delivery changer'))
        }) ;
      }
    });
  }

  async notifiqueAlteracaoStatus(pedido: any){
    let erroAlteracaoStatus;

    if(pedido.entrouEmPreparacao()){
      await this.notifiqueInicioPreparoPedido(pedido.referenciaExterna).catch((erro) => {
        erroAlteracaoStatus = erro
      })
    }

    if(pedido.saiuParaEntrega()){
      await this.notifiqueDespachoPedido(pedido.referenciaExterna).catch((erro) => {
        erroAlteracaoStatus = erro
      })
    }

    if(pedido.ficouPronto() ){//&& pedido.retirarPessoalmente()
      await this.notifiquePedidoProntoRetirada(pedido.referenciaExterna).catch((erro) => {
        erroAlteracaoStatus = erro
      })
    }

    return erroAlteracaoStatus;

  }

  notifiquePedidoProntoRetirada(id: string){
   return new Promise(async (resolve, reject) => {
     let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

     if(token){
       this.instance.post(String(`/order/v1.0/orders/${id}/readyToPickup`), {}, this.getHeader())
         .then((response: any) => {
           if(response.status === 200 || response.status === 202)
             return  resolve(response.data)

           reject( this.retorneErro(response, 'atualizar status pedido'))
         }).catch((resp: any) => {
         reject(this.retorneErro(resp, 'atualizar status pedido'))
       }) ;
     }
   });
 }

  notifiqueDespachoPedido(id: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        this.instance.post(String(`/order/v1.0/orders/${id}/dispatch`), {}, this.getHeader())
          .then((response: any) => {
            if(response.status === 200 || response.status === 202)
              return  resolve(response.data)

            reject( this.retorneErro(response, 'atualizar status pedido'))
          }).catch((resp: any) => {
          reject(this.retorneErro(resp, 'atualizar status pedido'))
        }) ;
      }
    });
  }

  notifiqueInicioPreparoPedido(id: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        this.instance.post(String(`/order/v1.0/orders/${id}/startPreparation`), {}, this.getHeader())
          .then((response: any) => {
            if(response.status === 200 || response.status === 202)
              return  resolve(response.data)

            reject( this.retorneErro(response, 'atualizar status pedido'))
          }).catch((resp: any) => {
          reject(this.retorneErro(resp, 'atualizar status pedido'))
        }) ;
      }
    });
  }

  notifiqueCancelamentoPedido(pedido: any, motivo: any){
    return new Promise(async (resolve, reject) => {
      let id: string = pedido.referenciaExterna,
        reason = motivo.description, cancellationCode = motivo.cancelCodeId

      if(!reason || !cancellationCode) return reject('Informe a razão do cancelamento para enviarmos ao Ifood ');

      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        let payload = {
          "reason": reason,
          "cancellationCode": cancellationCode
        }

        let url  = String(`/order/v1.0/orders/${id}/requestCancellation`);
        console.log(url)
        console.log(payload)

        this.instance.post(url, payload, this.getHeaderJson()).then(async (response: any) => {
            if(response.status === 200 || response.status === 202){

              let orderEvent = await OrderEvent.novoCancelamentoIfood(pedido, reason);

              resolve(orderEvent)
            } else {
              reject( this.retorneErro(response, 'cancelar pedido'))
            }
          }).catch((resp: any) => {
          reject(this.retorneErro(resp, 'cancelar pedido'))
        }) ;
      }
    });
  }

  aceiteCancelamento(orderId: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => resolve(erro));

      if(token){
        let url  =   String(`/order/v1.0/orders/${orderId}/acceptCancellation`)

        console.log(url);

        this.instance.post(url, {}, this.getHeaderJson()).then(async (response: any) => {
          if(response.status === 200 || response.status === 202){
            resolve('')
          } else {
            resolve( this.retorneErro(response, 'aceitar disputa'))
          }
        }).catch((resp: any) => {
          resolve(this.retorneErro(resp, 'aceitar disputa'))
        }) ;
      }
    })
  }

  rejeiteCancelamento(orderId: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => resolve(erro));

      if(token){
        let url  =   String(`/order/v1.0/orders/${orderId}/denyCancellation`)

        console.log(url);

        this.instance.post(url, {}, this.getHeaderJson()).then(async (response: any) => {
          if(response.status === 200 || response.status === 202){
            resolve('')
          } else {
            resolve( this.retorneErro(response, 'aceitar disputa'))
          }
        }).catch((resp: any) => {
          resolve(this.retorneErro(resp, 'aceitar disputa'))
        }) ;
      }
    })
  }

  facaProposta(disputeId: string, alternative: any, valor: number){
    return new Promise(async (resolve, reject) => {
      if(alternative.type) return resolve('Tipo da proposta não informado');
      if(!valor) return resolve('Valor da proposta não informado');

      let token: any = await this.obtenhaToken().catch((erro) => resolve(erro));

      if(token){
        let url  = String(`/order/v1.0/disputes/${disputeId}/alternatives/${alternative.id}`);

        let dados = {
          "type": alternative.type,
          "metadata": {
            "amount": {
              "value": Number(valor * 100),
              "currency": "BRL"
            }
          }
        }

        console.log(url);
        console.log(dados);
        this.instance.post(url, {}, this.getHeaderJson()).then(async (response: any) => {
          if(response.status === 200 || response.status === 201 || response.status === 202){
            resolve('')
          } else {
            resolve( this.retorneErro(response, 'propor alternatina na disputa'))
          }
        }).catch((resp: any) => {
          resolve(this.retorneErro(resp, 'propor alternatina na disputa'))
        }) ;
      }
    });
  }

  aceiteDisputa(disputeId: string ){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => resolve(erro));

      if(token){
        let url  = String(`/order/v1.0/disputes/${disputeId}/accept`);

        console.log(url);

        this.instance.post(url, { reason : "cancelamento aceito"}, this.getHeaderJson()).then(async (response: any) => {
          if(response.status === 200 || response.status === 201 || response.status === 202){
            resolve('')
          } else {
            resolve( this.retorneErro(response, 'aceitar disputa'))
          }
        }).catch((resp: any) => {
          resolve(this.retorneErro(resp, 'aceitar disputa'))
        }) ;
      }

    })
  }

  rejeiteDisputa(disputeId: string, reason: string){
    return new Promise(async (resolve, reject) => {
      if(!reason) return resolve('Informe um motivo para rejeitar a disputa')

      let token: any = await this.obtenhaToken().catch((erro) => resolve(erro));

      if(token){
        let url  = String(`/order/v1.0/disputes/${disputeId}/reject`);
        console.log(url);

        this.instance.post(url, {reason: reason}, this.getHeaderJson()).then(async (response: any) => {
          if(response.status === 200 || response.status === 201 || response.status === 202){
            resolve('')
          } else {
            resolve( this.retorneErro(response, 'aceitar disputa'))
          }
        }).catch((resp: any) => {
          resolve(this.retorneErro(resp, 'aceitar disputa'))
        }) ;
      }
    })
  }

  listeRazoesCancelamentos(id: string){

    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        //TODODO: PEDIDO JA TIVER SIDO CANCELADO, ELE RETORNA: "Order 752c1c8a-3d2f-4e34-a349-b7bb199eb650 is already cancelled"
        //LER ESSA RESPOSTA E RETORNAR RAZAO FIXA: Pedido ja cancelado no ifood
        this.instance.get(String(`/order/v1.0/orders/${id}/cancellationReasons`), this.getHeader())
          .then((response: any) => {
            if(response.status === 204) return resolve([])

            if(response.status === 200)  return  resolve(response.data)

            reject( this.retorneErro(response, 'consultar razões cancelamento'))
          }).catch((resp: any) => {
          reject(this.retorneErro(resp, 'consultar razões cancelamento'))
        }) ;
      }
    });

  }

  obtenhaProduto(code: string){
    return new Promise(async (resolve, reject) => {
      let token: any = await this.obtenhaToken().catch((erro) => reject(erro));

      if(token){
        let url = String(`/catalog/v2.0/merchants/${this.integracaoIfood.idLoja}/products/externalCode/${code}`);
        console.log(url)
        this.instance.get(url, this.getHeader())
          .then((response: any) => {
            if(response.status === 200) return  resolve(response.data)

            reject( this.retorneErro(response, 'obter produto'))
          }).catch((resp: any) => {
          reject(this.retorneErro(resp, 'obter produto'))
        }) ;
      }
    });
  }

  public  ehUmEventoEsperado(value: string): boolean {
    const eventosOrdersApi = Object.values(EventsIfoodEnum).includes(value as EventsIfoodEnum);
    const eventosDeliveryApi =  Object.values(EventsIfoodDeliveryEnum).includes(value as EventsIfoodDeliveryEnum);


    return eventosOrdersApi || eventosDeliveryApi
  }

  async executeNotificacoesPendente(){
    let notificacoes = await new MapeadorDeNotificacaoIfood().listeAsync({ naoExecutadas: true})

    console.log('total notificação pedido executar: ' + notificacoes.length)

    await this.executeNotificacoes(notificacoes);


    let notificacoesDelivery = await new MapeadorDeNotificacaoDelivery().listeAsync({ naoExecutadas: true, origem: 'ifood'});

    console.log('total notificação pedido executar: ' + notificacoesDelivery.length)

    await this.executeNotificacoes(notificacoesDelivery);

    if(notificacoesDelivery.length)
      notificacoes.push(...notificacoesDelivery)

    return notificacoes;
  }

  async facaPollingDaLoja(   ){
    if(this.integracaoIfood.desativado) return;

    let resourceId: string = this.integracaoIfood.idLoja;
    let resposta: any, lock: any;

    try {
      // Acesse o recurso protegido aqui
      console.log(`obter lock no resource ${resourceId}...`);
      const resourceLockKey = `ifoodmerchant:${resourceId}`;
      // locak morrerr depois de 60s se nao liberado
      lock = await redlock.acquire([resourceLockKey], 60000);

      // Acesse o recurso protegido aqui
      console.log(`Accessing resource ${resourceId}...`);
      let novosEventos: any = await this.obtenhaNovosEventos([this.integracaoIfood.idLoja]);

      if (novosEventos)
        resposta = await this.salveNovasNotificaoes(novosEventos);

    }catch (err){
      console.log('Falha ao tentar pegar novos eventos')
      console.error(err);
    } finally {
      console.log(`release lock resource ${resourceId}...`);
      if(lock){
        try {
          await lock.unlock()
        } catch (er){
          console.log('falha ao liberar lock')
          console.error(er)
        }
      }

      if(resposta && resposta.novasNotificacoes.length)
        await this.executeNotificacoes(resposta.novasNotificacoes);
    }
  }

  async salveNovasNotificaoes(novosEventos: any){
    let resposta: any = {};

    resposta.novasNotificacoes = [];
    resposta.notificacoesIgnorar  = [];

    console.log('Total de novos eventos: ' + novosEventos.length)
    for(let i = 0; i < novosEventos.length; i++){
      let event: any = novosEventos[i];
      console.log('Novo evento ifood: ' + event.code)
      console.log(event)
      const notificacaoDelivery = NotificacaoIfoodDelivery.listeTiposDelivery().indexOf(event.code) >= 0;

      let notificacao = notificacaoDelivery ? new NotificacaoIfoodDelivery(null, event)
        : new NotificacaoIfood(event);

      if(this.ehUmEventoEsperado(event.code)){
        console.log('novo evento ifood: ' + event.fullCode)
        resposta.novasNotificacoes.push(notificacao);
      } else {
        console.log('Ignorar evento ifood: ' + event.code)
        console.log(event);
        notificacao.ignorar = true;
        resposta.notificacoesIgnorar.push(event)
      }

      try{
        console.log('inserir notificacao: ' + notificacao.id)
        if(notificacaoDelivery)
          await notificacao.salve();
        else
          await notificacao.insira();
      } catch (err){
        console.log('deu erro insert: ')
        console.error(err)
      }
    }

    if(resposta.novasNotificacoes.length)
      await this.facaAckDasNotificacoes(resposta.novasNotificacoes)

    if(resposta.notificacoesIgnorar.length)
      await this.facaAckDasNotificacoes(resposta.notificacoesIgnorar)

    return resposta;
  }

  async facaPollingEventos( merchant: string = null ){
  let merchants = [];

  if(merchant)
    merchants = [merchant]
  else
    merchants =    await CacheService.getListaLojasPingando();

   if(!merchants.length) {
     let msg = 'Nenhuma loja integrada ifood pingando  ainda no promokit';
     return { mensagem: msg};
   }

    let  novosEventos: any, resposta: any = {}, lock: any;
    const resourceLockKey = `ifoodcentralizado`;

    try {
      // lock morrerr depois de 60s se nao liberado
      lock = await redlock.acquire([resourceLockKey], 30000);
      novosEventos  = await this.obtenhaNovosEventos(merchants);

    }catch (err){
      console.log('Falha ao tentar pegar novos eventos')
      console.error(err);
    } finally {
      console.log(`release lock resource ${resourceLockKey}...`);
      if(lock){
        try {
          await lock.unlock()
        } catch (er){
          console.log('falha ao liberar lock')
          console.error(er)
        }
      }
      if(novosEventos){
        resposta = await this.salveNovasNotificaoes(novosEventos);

        if(resposta.novasNotificacoes.length){
          ExecutorAsync.execute( async (cbAsync: any) => {
            await this.executeNotificacoes(resposta.novasNotificacoes);
            cbAsync();
          }, (err: any) => {
            console.log('Erro ao executar notificaçoes')
            console.error(err)
          });
        }
      } else {
        resposta.mensagem = 'Nenhum novo event retornado'
      }
    }


    return resposta;
  }

  private getHeader(merchants: any = null){
    let headers: any  =     {
      'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': "Bearer " + this.integracaoIfood.token
    }

    if(merchants)
      headers['x-polling-merchants'] = merchants.join(',')

    return  {  headers: headers  }
  }

  async executeNotificacoes(notificacacoes: Array<NotificacaoIfood>){
    console.log('total notificaçoes executar: ' + notificacacoes.length)
    //garantir primiero seja executada notificaçoes de novos pedidos
    let notificacoesNovosPedidos =  notificacacoes.filter((item: any) => item.ehNovoDePedido());
    let outrasNotificacoes =  notificacacoes.filter((item: any) => !item.ehNovoDePedido());

    for(let i = 0 ; i < notificacoesNovosPedidos.length; i++){
      let notificacao: NotificacaoIfood  = notificacoesNovosPedidos[i];

      await NotificacaoPedidoService.executeIfood(notificacao, this)
    }

    try{
      outrasNotificacoes.sort((a: NotificacaoIfood, b: NotificacaoIfood) => a.horarioCriacao.getTime() - b.horarioCriacao.getTime());
    } catch (er){
      console.log('nao ordenou', er)
    }


    for(let i = 0 ; i < outrasNotificacoes.length; i++){
      let notificacao: NotificacaoIfood  = outrasNotificacoes[i];

      await NotificacaoPedidoService.executeIfood(notificacao, this)
    }

  }

  private getHeaderJson(){
    return  {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': "Bearer " + this.integracaoIfood.token
      }
    }
  }

  private retorneErro(data: any, operacao: string, prefixo = 'Falha ao '){
    let msgErro =  prefixo + operacao + '(Ifood)';
    console.error(msgErro);

    if( data.response) {
      if (data.response.status === 403)
        msgErro = String(`${msgErro}: Permissão negada (HTTP 403)`)

      if (data.response.status === 401)
        msgErro = String(`${msgErro}: Não autorizado (HTTP 401)`)

      if (data.response.status === 404)
        msgErro = String(`${msgErro}: Não encontrada (HTTP 404)`)

      if (data.response.data) {
        let dados: any = data.response.data;
        console.error(dados);

        if(dados.message){
          msgErro = String(`${msgErro}: ${dados.message}`)
        } else if(dados.error_description)
          msgErro = String(`${msgErro}: ${dados.error_description}`)
        else if(dados.error){
          console.log(dados.error)
          msgErro = String(`${msgErro}: ${ dados.error.message ? dados.error.message : dados.error}`)
        }  else {
          console.log(dados)
          let erro = data.response.statusText || 'Http status ' + data.response.status ;

          msgErro = String(`${msgErro}: ${erro}`)
        }
        if(dados.details && Array.isArray(dados.details))
          msgErro = String(`${msgErro}: ${dados.details.map((item: any) => item)}`)

      }
    } else {
      console.error(data);
      if(typeof data === 'string'){
        msgErro = String(`${msgErro}: ${data}`)
      } else {
        if(data.message)
          msgErro = String(`${msgErro}: ${data.message}`)
      }
    }


    console.log(msgErro)
    return msgErro;
  }


}
