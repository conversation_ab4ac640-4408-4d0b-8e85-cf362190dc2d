import {IServiceIntegracaoExternaERP} from "../../domain/integracoes/IServiceIntegracaoExternaERP";
import {Pedido} from "../../domain/delivery/Pedido";
import {Produto} from "../../domain/Produto";
import axios from "axios";
import {DTOPedidoSaipos} from "../../lib/integracao/saipos/DTOPedidoSaipos";
import {Ambiente} from "../Ambiente";
import {EnumTipoDeOrigem} from "../../lib/emun/EnumTipoDeOrigem";
import {Comanda} from "../../domain/comandas/Comanda";
import {DTOPedidoMesaSaipos} from "../../lib/integracao/saipos/DTOPedidoMesaSaipos";
import {RequestParceiro} from "../../domain/integracoes/RequestParceiro";

//Ambiente: https://homolog.saipos.com/
// Usuário: <EMAIL>
// Senha: MeuCardapio.ai
//cod_store: 123 // Esse campo deverá conter o ID da loja no seu sistema, configuração feita posteriormente
let credenciasTeste = {
  host: 'https://homolog-order-api.saipos.com',
  cod_store: '123',
  idPartner: 'a0e858f3c8c53f113299fa4e4858b54b',
  secret: '3c514d1c40969236b6123fcc5bdaba78'
}
export class SaiposERPService  implements IServiceIntegracaoExternaERP{
  private urlApi = '';
  private token: string;
  private secret: string;
  constructor(private integracao: any, private codStore: string, lojaHomologacao: boolean = false) {
    this.secret = '1ca9cd38c6db533bc184f96d0f45bb61';
    this.urlApi = 'https://order-api.saipos.com';

    if(!Ambiente.Instance.producao || lojaHomologacao){
      this.secret =  credenciasTeste.secret;
      this.urlApi = credenciasTeste.host
      if(this.integracao){
        this.integracao.configuracoesEspecificas.cod_store = credenciasTeste.cod_store;
        this.integracao.configuracoesEspecificas.idPartner =   credenciasTeste.idPartner;

      //  delete   this.integracao.configuracoesEspecificas.token;
      }
    }
    axios.defaults.headers.common["Content-Type"] = "application/json";
    axios.defaults.headers.common["Accept"] = "application/json";

  }

  obtenhaToken(idPartner: string){
    return new Promise(async (resolve, reject) => {
      axios.post(String(`${this.urlApi}/auth`), {"secret": this.secret,
        "idPartner": idPartner }).then((res: any) => resolve(res.data.token))
        .catch((err: any) => reject(this.retornoErro('obter token ', err)));
    })

  }

  executeRequest(nomeMetodo: string, data: any, pedido: any = null){
    return new Promise(async (resolve, reject) => {

      let token = await this.integracao.obtenhaCredencial();

      let url = String(`${this.urlApi}/${nomeMetodo}`)
      console.log(url)
      console.log(data)
      axios.post(url, data, { headers: {"Authorization": token }}).then(  async (response: any) => {
        console.log('retorno saipos:')
        console.log(response.data);
        if(pedido)
          await new RequestParceiro(pedido, 'saipos', data).saveRetornoHttp(response);
        resolve(response.data)
      }).catch( async (error: any) => {
        if(pedido)
          await new RequestParceiro(pedido, 'saipos', data).saveRetornoHttp(error);

        if(error.response && error.response.status === 401){
          await this.integracao.renoveToken();
          let respostaNovaTentativa = await this.executePut(nomeMetodo, data).catch((novoErro) => {
            console.log(novoErro)
            reject(error)
          })
          if(respostaNovaTentativa) return resolve(respostaNovaTentativa)
        } else {
          reject(error)
        }
      })
    })
  }

  executePut(nomeMetodo: string, data: any){
    return new Promise(async (resolve, reject) => {

      let token = await this.integracao.obtenhaCredencial();

      let url = String(`${this.urlApi}/${nomeMetodo}`)
      console.log(url)
      axios.put(url, data, { headers: {"Authorization": token }}).then(   (response: any) => {
        console.log(response.data);
        resolve(response.data)
      }).catch( (response: any) => {
        reject(response)
      })
    })
  }



  valideToken(): Promise<any> {
    return Promise.resolve(this.token != null); //ja pegou token esta ok
  }

  fecheComanda(comanda: Comanda, empresa: any){
    return new Promise(async (resolve, reject) => {
      let order_id = comanda.obtenhaCodigoExterno();

      let dados: any = {order_id: order_id, cod_store: empresa.id.toString()};

      console.log(dados)

      let result: any = await this.executePut('close-sale', dados).catch( (erro) => {
        reject(this.retornoErro('fechar comanda', erro))
      } )

      if(result){
        resolve(order_id);
      }
    })
  }

  obtenhaDTOPedido(pedido: any, empresa: any){
    let dadosPedido: any;

    if(pedido.mesa && pedido.mesa.id){
      dadosPedido = new DTOPedidoMesaSaipos(pedido, empresa, this.codStore );
    } else {
      dadosPedido = new DTOPedidoSaipos(pedido, empresa, this.codStore );
    }


    return dadosPedido;
  }

  adicionePedido(pedido: Pedido, empresa: any): Promise<string> {
    return new Promise(async (resolve, reject) => {
      try {
        if (pedido.referenciaExterna) {
          console.log('Já foi notificado para o Saipos' + pedido.referenciaExterna)
          return resolve(pedido.referenciaExterna)
        }

        if(pedido.foiCanceladoOuDevolvido())
          return  reject('Pedido foi cancelado, não pode ser enviado.')

        await pedido.carregueDeParaTamanhoSabores();

        let dadosPedido = this.obtenhaDTOPedido(pedido, empresa);

        console.log(dadosPedido)

        dadosPedido.items.forEach((item: any) => {
          if(item.choice_items && item.choice_items.length ){
            item.choice_items.forEach((itemMontado: any) => {
              console.log(itemMontado)
            })
          }
        })

        let result: any = await this.executeRequest('order', dadosPedido, pedido).catch( (erro) => {
          reject(this.retornoErro('enviar pedido', erro))
        } )

        if(result){
          console.log(result);
          //table_number
          if(result.sale_number){
            resolve(result.sale_number.toString());
          } else if (result.table_number) {
            resolve(result.table_number.toString());
          } else {
            reject(this.retornoErro('enviar pedido', JSON.stringify(result)))
          }
        }

      } catch (execption){
        console.warn(execption)

        reject(this.retornoErro('enviar pedido', execption.message))
      }
    });
  }

  alterePedido(pedidoNovo: Pedido, pedidoAntigo: Pedido, empresa: any): Promise<string> {
    return Promise.reject(this.retornoErro('altera pedido' , "Não é possivel alterar o pedido no Saipos"));
  }

  cancelePedido(pedido: Pedido): Promise<any> {
    return new Promise(async (resolve, reject) => {
      let dadosPedido: any = { cod_store: this.codStore, order_id: pedido.id.toString()}
      let result: any = await this.executeRequest('cancel-order', dadosPedido, pedido).catch( (erro) => {
        reject(this.retornoErro('cancelar pedido', erro))
      } )

      if(result) resolve(null);
    });
  }

  listeBandeiras(tipo: string): Promise<Array<any>> {
    return Promise.resolve([]);
  }

  listePrecosProdutos(ultimaSincronizacaoPrecos: any): Promise<Array<Produto>> {
    return Promise.resolve([]);
  }

  obtenhaProduto(codigo: string){
    return Promise.resolve(null)
  }

  obtenhaProdutoConvertido(){
    return Promise.resolve(null)
  }


  listeProdutos(): Promise<any> {
    return Promise.resolve([]);
  }

  listeProdutosConvertidos(ultimaSincronizacaoProdutos: any): Promise<Array<Produto>> {
    return Promise.resolve([]);
  }

  listeProdutosIndisponiveis(): Promise<any>{
    return Promise.resolve([])
  }

  listeProdutosDisponiblidadeAtualizada(): Promise<Array<any>> {
    return Promise.resolve([]);
  }


  veririqueUpdates(data: any): Promise<any> {
    return Promise.resolve(null);
  }

  private retornoErro(operacao: string, erro: any) {
    console.log(erro)
    let msgErro = String(`Falha ao ${operacao} (Saipos):`);

    if( erro.response){
      if(erro.response.status === 403)
        msgErro  =  String(`${msgErro}: Permissão negada (HTTP 403)`)

      if(erro.response.status === 401)
        msgErro =  String(`${msgErro}: Não autorizado (HTTP 401)`)

      if(erro.response.data ){
        if(erro.response.data.errorMessage)
          msgErro =  String(`${msgErro}: ${erro.response.data.errorMessage}`)

        if(  erro.response.data.erros){
          console.log(erro.response.data.erros)
          let erroDestalhes: any = erro.response.data.erros[0];

          if(erroDestalhes.campo)
            msgErro =   String(`${msgErro}: ${erroDestalhes.campo} - ${erroDestalhes.mensagem}`)
          else
            msgErro =  String(`${msgErro}: ${erroDestalhes.mensagem}`)
        }
      }

    } else  if(erro.message){
      msgErro =  String(`${msgErro}: ${erro.message}`)
    } else {
      msgErro =  String(`${msgErro}: ${erro.toString()}`)
    }

    console.log(msgErro)

    return msgErro;
  }

  obtenhaTipoDeOrigem(): EnumTipoDeOrigem {
    return EnumTipoDeOrigem.ImportadoSaipos;
  }
}
