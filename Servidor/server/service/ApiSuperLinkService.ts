import {Pedido} from "../domain/delivery/Pedido";
import {PagamentoPedido} from "../domain/delivery/PagamentoPedido";

import {Empresa} from "../domain/Empresa";
import {MapeadorDeFormaDePagamento} from "../mapeadores/MapeadorDeFormaDePagamento";
import {FormaDePagamento} from "../domain/delivery/FormaDePagamento";
import {ConfigMeioDePagamento} from "../domain/delivery/ConfigMeioDePagamento";
import {VariaveisDeRequest} from "./VariaveisDeRequest";
import {AxiosInstance} from "axios";

import {IMeioPagamentoService} from "./meiospagamentos/IMeioPagamentoService";
import * as moment from "moment";
import {EnumStatusPagamento, StatusPagamentoDeParaCielo} from "../lib/emun/EnumStatusPagamento";
import {PedidoService} from "./PedidoService";
import {NotificacaoMeioPagamentoService} from "./NotificacaoMeioPagamentoService";
const axios = require('axios');

let credenciasSandbox  = {
  ClientId: '180b88cd-ce88-4f88-82dd-9995435bb55e',
  ClientSecret: 'XOXMQycagSH4/FG+OfwHvraVYR2dH5EeGAUfLA71FUY='
}


//configurar dentro Cielo, fica em Ecommerce -> Super link -> Configurações.
//retorno padrao
//https://promokit.meucardapio.ai/retorno/pagamento/externo    -> URL de Retorno  (redirect depois finaliza)
//retorno multiplas empresas
//https://promokit.meucardapio.ai/cielo/retorno/superlink    -> URL de Retorno  (redirect depois finaliza)


//Notificaoes:
//https://fibo.meucardapio.ai/pagamentos/notificacao/cielo  ->  URL de Notificação (quando associa link pagamento ao pagamento criado
//https://fibo.meucardapio.ai/pagamentos/notificacao/cielo/status // -> URL de Mudança de Status  ( pagamento sofre alteração no status)
export class ApiSuperLinkService implements IMeioPagamentoService{
  host = 'https://cieloecommerce.cielo.com.br';
  urlApiV1 = String(`${this.host}/api/public/v1`)
  urlApiV2 = String(`${this.host}/api/public/v2`)
  instanceV1: AxiosInstance;
  instanceV2: AxiosInstance;
  constructor(public empresa: Empresa, private configMeioDePagamento: ConfigMeioDePagamento = null) {
    this.instanceV1 = axios.create({
      baseURL: this.urlApiV1
    });
    this.instanceV2 = axios.create({
      baseURL: this.urlApiV2
    });
  }

  estornePagamento(id: any, valor: number): Promise<string> {
    //todo: fazer estorno para superlink
    return Promise.reject("Execute storno diretamente no portal da Cielo")
  }

  private otenhaToken() {
    return new Promise<void>(async (resolve, reject) => {
      if(! this.configMeioDePagamento){
        let formaDePagamento: any = this.empresa.obtenhaFormaPagamentoOnline('cielo');
        let formaPagamentoComConfig: FormaDePagamento = await new MapeadorDeFormaDePagamento().selecioneSync(formaDePagamento.id)

        if(!formaPagamentoComConfig.configMeioDePagamento)
          return resolve();

        this.configMeioDePagamento = formaPagamentoComConfig.configMeioDePagamento;
      }

      const dadosCielo = {
        ClientID:  this.configMeioDePagamento.clientID,
        ClientSecret:  this.configMeioDePagamento.clientSecret
      };

      const base64 = dadosCielo.ClientID + ":" + dadosCielo.ClientSecret;

      if(this.empresa.tokenApiSuperLink && moment().isAfter(this.empresa.tokenApiSuperLink.dataValidade))
        return resolve(this.empresa.tokenApiSuperLink.access_token)

      this.instanceV2.post('/token', { }, {
        headers: {"Authorization": "Basic " + Buffer.from(base64).toString('base64')}
      }).then( (response: any) => {
        this.empresa.tokenApiSuperLink  = response.data;
        this.empresa.tokenApiSuperLink.dataValidade  = moment().add(Number(response.data.expires_in) - 60, 's' ).toDate();
        resolve(this.empresa.tokenApiSuperLink.access_token)
      }).catch( (erro: any) => {
        console.log('Falha obtenção do token acesso da cielo');
        console.log(dadosCielo)
        console.log(base64)
        console.log(erro)
        resolve()
      })

    })

  }

  obtenhaPagamentos(linkPagamento: any){

    return new Promise(async (resolve, reject) => {
      this.otenhaToken().then( (accessToken) => {
        this.instanceV1.get(String(`/products/${linkPagamento}/payments`),
          { headers: { "Authorization": "Bearer " + accessToken } }).then( (response: any) => {

          resolve(response.data.orders)
        })
      })
    })

  }
  obtenhaOrderPeloLink(link: string){
    return new Promise(async (resolve, reject) => {
      console.log(link)
      this.otenhaToken().then( (accessToken) => {
        axios.get(link,
          { headers: { "Authorization": "Bearer " + accessToken } }).then( (response: any) => {
          resolve(response.data)
        }).catch( (result: any) => {
          console.log('Erro  buscar order')
          console.log(result.response.data)
          reject('Erro  buscar order')
        })
      })
    })
  }

  obtenhaPedido(codigo: any){

    return new Promise(async (resolve, reject) => {
      this.otenhaToken().then( (accessToken) => {
        this.instanceV2.get(String(`/merchantOrderNumber/${codigo}`),
          { headers: { "Authorization": "Bearer " + accessToken } }).then( (response: any) => {

          resolve(response.data)
        }).catch((response: any) => {
          reject(this.trateErroRequest(response, 'obter pedido'))
        })
      })
    })

  }

  obtenhaLinkPagamento(codigo: any){

    return new Promise(async (resolve, reject) => {
      this.otenhaToken().then( (accessToken) => {
        this.instanceV1.get(String(`/products/${codigo}`),
          { headers: { "Authorization": "Bearer " + accessToken } }).then( (response: any) => {

          resolve(response.data)
        }).catch((response: any) => {
          reject(this.trateErroRequest(response, 'obter link pagamento'))
        })
      })
    })

  }

  obtenhaLinkRedirectUnico(pedido: any, pagamento: any){
    let linkRedirectSessao =  String(`/cielo/redirect/superlink/${pedido.guid}`)
    let host = (new VariaveisDeRequest()).obtenhaUrlCardapio( Object.assign({ dominio: 'promokit'} , new Empresa()));

    return String(`${host}${linkRedirectSessao}?t=${pagamento.link}`)
  }

  crieLinkPagamento(pedido: Pedido): Promise<any>{

    return new Promise(async (resolve, reject) => {
      let pagamento: PagamentoPedido = pedido.pagamentos[0];

      if(pagamento.link){
        console.log('Pagamento já possui link gerado: ')
       return  resolve({ link: this.obtenhaLinkRedirectUnico(pedido, pagamento)})
      }

      this.otenhaToken().then( accessToken => {
        const dadosProduto = {
          "Type": "Digital",
          "name":  String(`Pedido #${pedido.codigo} -  ${pedido.empresa.nome}`),
          "description": String(``),
          "show_description": true,
          "price": pedido.obtenhaTotalPagar() * 100,
          "maxNumberOfInstallments": "1",
          "quantity": 1,
          "shipping": {
            "type": "WithoutShipping"
          },
          "SoftDescriptor": this.obtenhaDescricaoLancamentoCartao()
        };

        this.instanceV1.post('/products', dadosProduto,
          { headers: { "Authorization": "Bearer " + accessToken } }).then( async (response: any) => {
          console.log(response.data);
          await pagamento.atualizeComDadosLinkPagamento(response.data)
          resolve({ link: this.obtenhaLinkRedirectUnico(pedido, pagamento)})
        }).catch((response: any) => {
          reject(this.trateErroRequest(response, 'criar link de pagamento'));
        })
      })

    })
  }

  sincronizePagamento(pedido: Pedido, pagamentoOnline: PagamentoPedido, empresa: any): Promise<string> {
    return new Promise(async (resolve, reject) => {
       if(pagamentoOnline.idLink){
         let pagamentos: any = await this.obtenhaPagamentos(pagamentoOnline.idLink);

         let pagamentoRealizado = pagamentos.find((item: any) => item.payment && item.payment.status === 'Paid')

         if(pagamentoRealizado){
           let linkOrder = pagamentoRealizado.links.find((item: any) => item.method === 'GET');

           let order: any = await this.obtenhaOrderPeloLink(linkOrder.href);

           if(order && order.payment){
             let novoStatus = EnumStatusPagamento.Aprovado

             if(novoStatus.toString() !== pagamentoOnline.status.toString()){
               await pagamentoOnline.atualizeRetornoOrderSuperlink(order);
               pedido.empresa = empresa;
               await new PedidoService().mudouStatusPagamento(pedido, pagamentoOnline, novoStatus);
               pagamentoOnline.pedido = pedido;
               await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoOnline, empresa)
             }
           }
         }
       }
       resolve('')
    })
  }


  private trateErroRequest(req: any, operacao: string): string{
    if(req.response)
      console.log('Erro na request + '  + req.response.status)

    if(typeof req.toJSON === 'function')
      console.log(req.toJSON())
    let detalhesErro = '';


    if(req.response.statusText)
      detalhesErro = req.response.statusText
    else if(req.message) detalhesErro = req.message;

    let erro = 'Nao foi possivel ' + operacao +  ""

    if(detalhesErro) erro = erro + ": " + detalhesErro

    return erro;
  }


  private obtenhaDescricaoLancamentoCartao() {
    let descricao =  this.empresa.nome.toUpperCase();

    if(this.configMeioDePagamento && this.configMeioDePagamento.nomeFaturaCartao)
       descricao = this.configMeioDePagamento.nomeFaturaCartao;

    return descricao.replace(/\s/gm, "").substr(0, 13);
  }
}
