import Queue = require("bull");
import moment = require("moment");
import {Ambiente} from './Ambiente';
import {MapeadorDeEmpresa} from '../mapeadores/MapeadorDeEmpresa';
import {ExecutorAsync} from '../utils/ExecutorAsync';
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {SessaoLinkSaudacao} from "../domain/SessaoLinkSaudacao";
import {Empresa} from "../domain/Empresa";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {NotificacaoService} from "./NotificacaoService";
import {MapeadorDeNotificacao} from "../mapeadores/MapeadorDeNotificacao";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {Notificacao} from "../domain/Notificacao";
import {StatusDeMensagem} from "./StatusDeMensagem";
import {MapeadorDeMensagemEnviada} from "../mapeadores/MapeadorDeMensagemEnviada";
const async = require('async');

export class EnviadorDeCarrinhoAbandonadoService {

  private static _instancia: EnviadorDeCarrinhoAbandonadoService
    = new EnviadorDeCarrinhoAbandonadoService();

  private filaCarrinho: any;
  private constructor() {
    this.crieFilaCarrinhoAbandonado();
  }

  static Instancia(): EnviadorDeCarrinhoAbandonadoService {
    console.log('criando');
    return EnviadorDeCarrinhoAbandonadoService._instancia;
  }

  async execute(sessaoLinkSaudacao: SessaoLinkSaudacao, tempoEmSegundos: number) {
    if ( !sessaoLinkSaudacao ) {
      return null;
    }

    console.time('Adicionando na fila');

    return await this.filaCarrinho.add(sessaoLinkSaudacao, { delay: tempoEmSegundos * 1000 });
  }

  private crieFilaCarrinhoAbandonado() {
    this.filaCarrinho = new Queue('fila-carrinho', { defaultJobOptions: {
        attempts: 90,
        backoff: 2000,
        removeOnComplete: true,
        removeOnFail: true
      }
    });

    this.filaCarrinho.process( async (job: any, done: any) => {
      ExecutorAsync.execute( async (callback: Function) => {
        const sessao: SessaoLinkSaudacao = job.data;
        if(!sessao.contato || !sessao.contato.id) return;

        const empresa: Empresa = await new MapeadorDeEmpresa().selecioneSync(sessao.empresa.id);

        require('domain').active.contexto.idEmpresa = empresa.id;
        require('domain').active.contexto.empresa = empresa;

        const contato = await new MapeadorDeContato().selecioneSync({id: sessao.contato.id});

        console.time('Executando a tarefa');
        console.log('tarefa');
        console.log(sessao);

        if(!contato){
          console.log('Nenhum contato encontrado: ' + sessao.contato.id);
          return;
        }

        const pedido = await new MapeadorDePedido().selecioneSync({
          idContato: contato.id, inicio: 0, total: 1,
          dataRealizacao: moment().add('-60', 'm').format('YYYY-MM-DD 00:00:00'),
        });

        if( pedido ) {
          return; //cliente fez pedido
        }

        let mapeador = new MapeadorDeNotificacao();

        let notificacao: Notificacao = await
          mapeador.selecioneSync( { tipoDeNotificacao: TipoDeNotificacaoEnum.CarrinhoAbandonado, ativa: true })

        const mensagensEnviadas = contato.id ? await new MapeadorDeMensagemEnviada().listeAsync({
          contato: contato,
          tipo: TipoDeNotificacaoEnum.CarrinhoAbandonado,
          enviadasNosultimosSegundos: 60 * 60,
          statusDiferente: StatusDeMensagem.Cancelada
        }) : [];

        if( mensagensEnviadas.length > 0 ) {
          callback();
          done();
          return;
        }

        const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

        await notificacaoService.envieNotificacaoCarrinhoAbandonado(contato);

        callback();
        done();
      }, (erro: Error) => {
        done(erro);
      }, 0);

      done();
    });
  }
}
