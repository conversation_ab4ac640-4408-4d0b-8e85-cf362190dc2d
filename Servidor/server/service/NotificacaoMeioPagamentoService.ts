import {MercadoPagoUtils} from "../lib/MercadoPagoUtils";
import {Pagamento} from "../domain/faturamento/Pagamento";
import {
  EnumStatusPagamento, StatusChargeDeParaPagseguro,
  StatusPagamentoDeParaCielo,
  StatusPagamentoDeParaPagarme, StatusPagamentoDeParaRede, StatusPaymentDeParaTunaPay, StatusPaymentDeParaTunaPayWebhook
} from "../lib/emun/EnumStatusPagamento";
import {ContratoService} from "./ContratoService";
import {NotificacaoMeioPagamento} from "../domain/faturamento/NotificacaoMeioPagamento";
import {Assinatura} from "../domain/faturamento/Assinatura";
import {IuguService} from "./IuguService";
import {Contrato} from "../domain/faturamento/Contrato";
import {MapeadorDeNotificacaoMeioPagamento} from "../mapeadores/MapeadorDeNotificacaoMeioPagamento";
import {NotificacaoCielo} from "../domain/faturamento/NotificacaoCielo";
import {PagamentoPedido} from "../domain/delivery/PagamentoPedido";
import {PedidoService} from "./PedidoService";
import {NotificacaoIugu} from "../domain/faturamento/NotificacaoIugu";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {IServiceIntegracaoExternaERP} from "../domain/integracoes/IServiceIntegracaoExternaERP";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {NotificacaoPagarme} from "../domain/faturamento/NotificacaoPagarme";
import {NotificacaoCieloCheckout} from "../domain/faturamento/NotificacaoCieloCheckout";
import {IntegradorUtils} from "../utils/IntegradorUtils";
import {NotificacaoPagSeguro} from "../domain/faturamento/NotificacaoPagSeguro";
import {NotificacaoERede} from "../domain/faturamento/NotificacaoERede";
import {ERedeItauApi} from "../lib/ERedeItauApi";
import {Pedido} from "../domain/delivery/Pedido";
import {NotificacaoTunaPay} from "../domain/faturamento/NotificacaoTunaPay";

let errosIgnorar: any = ['Fatura não tem assinatura vinculada'];

export class NotificacaoMeioPagamentoService {
  static async execute(notificacao: NotificacaoMeioPagamento){
    if(notificacao.meio === 'iugu' )
      await NotificacaoMeioPagamentoService.executeDoIugu(notificacao)

    if(notificacao.meio === 'cielo' )
      await NotificacaoMeioPagamentoService.executeDaCieloSuperlink(notificacao as NotificacaoCielo)

    if(notificacao.meio === 'cielocheckout' )
      await NotificacaoMeioPagamentoService.executeDaCieloCheckout(notificacao as NotificacaoCieloCheckout)

    if(notificacao.meio === 'pagarme' )
      await NotificacaoMeioPagamentoService.executeDaPagarme(notificacao as NotificacaoPagarme)

    if(notificacao.meio === 'pagseguro' )
      await NotificacaoMeioPagamentoService.executeDoPagseguro(notificacao as NotificacaoPagSeguro)
  }

  static async executeDoPagseguro(notificacao: NotificacaoPagSeguro){
    return new Promise<void>( async(resolve, reject) => {
      try {
        let contexto =   require('domain').active.contexto;
        let pagamentoPedido: PagamentoPedido;
        pagamentoPedido  = await PagamentoPedido.get({ codigo: notificacao.codigo });

        if(!pagamentoPedido) throw Error ('pagamento não existe: ' + notificacao.codigo);

        let empresa = await new MapeadorDeEmpresa().selecioneSync(pagamentoPedido.pedido.empresa.id)

        contexto.empresa =  empresa;
        contexto.idEmpresa = empresa.id;

        let novoStatus = notificacao.getStatus();

        await new PedidoService().mudouStatusPagamento(pagamentoPedido.pedido, pagamentoPedido, novoStatus)

        await notificacao.foiExecutada( );

        await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoPedido, empresa)
      } catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0)
      }

      resolve();
    })
  }

  static async executeDaRede(notificacao: NotificacaoERede){
    return new Promise<void>( async(resolve, reject) => {
      try {
        let contexto =   require('domain').active.contexto;

        if(notificacao.tipo !== 'tokenization'){
          let pagamentoPedido: PagamentoPedido;

          pagamentoPedido  = await PagamentoPedido.get({ codigoTransacao: notificacao.codigo });

          if(!pagamentoPedido) throw Error ('pagamento não existe: ' + notificacao.codigo);
          let empresa = await new MapeadorDeEmpresa().selecioneSync(pagamentoPedido.pedido.empresa.id)

          contexto.empresa =  empresa;
          contexto.idEmpresa = empresa.id;

          if(notificacao.tipo === 'transaction'){
            let novoStatus = StatusPagamentoDeParaRede.get(notificacao.status as any);

            await new PedidoService().mudouStatusPagamento(pagamentoPedido.pedido, pagamentoPedido, novoStatus)
          }

          if(notificacao.tipo === 'refund'){
            if(notificacao.status ===  "Done"){       //status: Done, Denied, Processing
              await new PedidoService().mudouStatusPagamento(pagamentoPedido.pedido, pagamentoPedido, EnumStatusPagamento.Reembolsado)
              // processar reembolso
            } else if(notificacao.status === 'Denied') {
              await new PedidoService().pagamentoReembolsoNegado(pagamentoPedido.pedido, pagamentoPedido);
            } else { //Processing
              console.log('Estorno ainda sera processado' + notificacao.status)
            }
            await notificacao.foiExecutada( );

            await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoPedido, empresa)
          }
        } else{
          let pagamentoPedido: PagamentoPedido;

          pagamentoPedido  = await PagamentoPedido.get({ tokenizacaoId: notificacao.codigo });

          if(!pagamentoPedido) throw Error ('pagamento não existe: ' + notificacao.codigo);

          let empresa = await new MapeadorDeEmpresa().selecioneSync(pagamentoPedido.pedido.empresa.id)

          contexto.empresa =  empresa;
          contexto.idEmpresa = empresa.id;

          let events: any = notificacao.getDados().events;
          let eventoTokenizar = events && events[0] ===  "PV.TOKENIZACAO-BANDEIRA";

          if(eventoTokenizar ){
            if(pagamentoPedido.aguardandoTokenizar()){
              let pedido: Pedido = await new MapeadorDePedido().selecioneSync(pagamentoPedido.pedido.id);

              let formaDepagamento = empresa.formasDePagamento.find((item: any) => item.id === pagamentoPedido.formaDePagamento.id);

              let service: ERedeItauApi = await new ERedeItauApi(formaDepagamento.configMeioDePagamento);

              let erroExecutar: any;
              await service.executeOperacoesTokenizacaoPendente( pedido, pagamentoPedido, empresa).catch((err) => {
                erroExecutar = err
              });

              if(erroExecutar) throw Error (erroExecutar);

              await notificacao.foiExecutada( );
            } else {
              throw Error ('pagamento já foi processado');
            }
          } else {
            let msgErro: string = 'events não implementado: ' + events[0];
            console.log(msgErro)
            await notificacao.registreErro(msgErro, true)
          }
        }
      }catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0)
      }

      resolve();
    })
  }

  static async executeDoTunapay(notificacao: NotificacaoTunaPay ){
    return new Promise<void>( async(resolve, reject) => {
      try {
        let contexto =   require('domain').active.contexto;
        let pagamentoPedido: PagamentoPedido;

        pagamentoPedido  = await PagamentoPedido.get({ codigo: notificacao.codigo });

        if(!pagamentoPedido) throw Error ('pagamento não existe: ' + notificacao.codigo);

        let empresa = await new MapeadorDeEmpresa().selecioneSync(pagamentoPedido.pedido.empresa.id)

        contexto.empresa =  empresa;
        contexto.idEmpresa = empresa.id;

        let novoStatus = StatusPaymentDeParaTunaPayWebhook.get(notificacao.status as any);

        await new PedidoService().mudouStatusPagamento(pagamentoPedido.pedido, pagamentoPedido, novoStatus)

        await notificacao.foiExecutada( );

        await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoPedido, empresa)
      }catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0)
      }

      resolve();
    })
  }
  static async executeDaPagarme(notificacao: NotificacaoPagarme){
    return new Promise<void>( async(resolve, reject) => {
      try {
        let contexto =   require('domain').active.contexto;
        let pagamentoPedido: PagamentoPedido;

        pagamentoPedido  = await PagamentoPedido.get({ codigo: notificacao.codigo });

        if(!pagamentoPedido) throw Error ('pagamento não existe: ' + notificacao.codigo);

        let empresa = await new MapeadorDeEmpresa().selecioneSync(pagamentoPedido.pedido.empresa.id)

        contexto.empresa =  empresa;
        contexto.idEmpresa = empresa.id;

        let novoStatus = StatusPagamentoDeParaPagarme.get(notificacao.status as any)

        if(novoStatus === EnumStatusPagamento.Cancelado && pagamentoPedido.foiAprovado())
           novoStatus = EnumStatusPagamento.Reembolsado

        //pegar dados pagamento
      //  if(novoStatus === EnumStatusPagamento.Aprovado)
         // await pagamentoPedido.atualizeRetornoPagarme(notificacao.getDados());

        await new PedidoService().mudouStatusPagamento(pagamentoPedido.pedido, pagamentoPedido, novoStatus)

        await notificacao.foiExecutada( );

        await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoPedido, empresa)

      }catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0)
      }

      resolve();
    })
  }

  static executeDaCieloCheckout(notificacao: NotificacaoCieloCheckout){
    return new Promise<void>( async(resolve, reject) => {
      try {
        let contexto = require('domain').active.contexto;
        let pagamentoPedido: PagamentoPedido;

        pagamentoPedido  = await PagamentoPedido.get({ codigo: notificacao.codigo });

        if(!pagamentoPedido) throw Error ('pagamento não existe: ' + notificacao.codigo);

        let empresa = await new MapeadorDeEmpresa().selecioneSync(pagamentoPedido.pedido.empresa.id)

        contexto.empresa =  empresa;
        contexto.idEmpresa = empresa.id;

        let novoStatus = StatusPagamentoDeParaCielo.get(Number(notificacao.status))

        await new PedidoService().mudouStatusPagamento(pagamentoPedido.pedido, pagamentoPedido, novoStatus)

        await notificacao.foiExecutada( );

        await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoPedido, empresa);

      }catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0)
      }

      resolve();
    })
  }
  static async executeDaCieloSuperlink(notificacao: NotificacaoCielo){
    return new Promise<void>( async(resolve, reject) => {
      try{
        let contexto =   require('domain').active.contexto;
        let pagamentoPedido: PagamentoPedido;


        if(notificacao.ehDeTransacao()){
          pagamentoPedido  = await PagamentoPedido.get({ idLink: notificacao.codigo });

          if(!pagamentoPedido) throw Error ('pagamento não existe: ' + notificacao.codigo);

          contexto.idEmpresa = pagamentoPedido.pedido.empresa.id;

          await new PedidoService().gerouNovoPagamentoCielo(pagamentoPedido,
            notificacao.obtenhaCodigoPagamento(),   notificacao.obtenhaBandeira(),
            notificacao.obtenhacodigoTipoPagamento(), notificacao.obtenhaMetodoPagamento());

        } else if(notificacao.ehDeMundancaStatus()){
          let codigoPagamento = notificacao.obtenhaCodigoPagamento();
          pagamentoPedido  = await PagamentoPedido.get({ codigo: codigoPagamento });

          if(!pagamentoPedido) throw Error ('pagamento não existe: ' + codigoPagamento);

        } else {
          await notificacao.registreErro('Tipo de notificacao não esperado', true)
        }

        if(pagamentoPedido){
          let empresa = await new MapeadorDeEmpresa().selecioneSync(pagamentoPedido.pedido.empresa.id)

          contexto.empresa =  empresa;
          contexto.idEmpresa = empresa.id;

          let novoStatus = StatusPagamentoDeParaCielo.get(Number(notificacao.status))

          await new PedidoService().mudouStatusPagamento(pagamentoPedido.pedido, pagamentoPedido, novoStatus)

          await notificacao.foiExecutada( );

          await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoPedido, empresa)
        }
      }catch (e) {
        await notificacao.registreErro(e.message, errosIgnorar.indexOf(e.message) >= 0)
      }

      resolve();
    });

  }

  static async notifiqueIntegracaoAtiva(pagamento: any, empresa: any){
    if(pagamento.pedido.pago && empresa.integracaoPDVParceiroAtiva()){
      let pedido = await new MapeadorDePedido().selecioneSync(pagamento.pedido.id);

      if(!pedido.referenciaExterna)
        IntegradorUtils.notifiqueNovoPedidoAsync(empresa, pedido);
    }
  }

  static async executeDoIugu(notificacao: NotificacaoIugu){
    return new Promise<void>( async(resolve, reject) => {
      let contratoService: ContratoService =  new ContratoService();
      let iuguService: IuguService = new IuguService();

      try{
        if(notificacao.ehDeFatura()){
          let invoice: any = await iuguService.obtenhaFatura(notificacao.codigo);

          if(!invoice) throw Error ('Fatura não existe no Iugu: ' + notificacao.codigo);

          let codigoAssinatura = notificacao.getCodigoAssinatura();

          if(!codigoAssinatura)   throw Error (errosIgnorar[0]);

          let assinatura: Assinatura = await  Assinatura.get({ codigo: codigoAssinatura});

          if(!assinatura) throw Error ('Assinatura não encontrada: ' + notificacao.getCodigoAssinatura());

          let contrato: any = await  Contrato.get({ idEmpresa: assinatura.empresa.id});

          await contratoService.sincronizeFaturaIugu(invoice, contrato);

        } else if (notificacao.ehDeAssinatura()){
          let subscription: any = await  iuguService.obtenhaAssinatura( notificacao.codigo);
          if(!subscription) throw Error ('Assinatura não existe no Iugu: ' + notificacao.codigo)

          let assinatura: Assinatura = await Assinatura.get( { codigo: notificacao.codigo });

          if(!assinatura) throw Error ('Assinatura não encontrada: ' + notificacao.codigo);

          let contrato: any = await  Contrato.get({ idEmpresa: assinatura.empresa.id});

          await contratoService.sincronizeAssinaturaIugu(contrato,  subscription)
        } else {
          await notificacao.registreErro('Tipo de notificacao não esperado', true)
        }

        await notificacao.foiExecutada( );

      }catch (e) {
        console.log(e)
        let erro = e.message ? e.message : e.toString();

        await notificacao.registreErro(erro, errosIgnorar.indexOf(erro) >= 0)
      }

      resolve();
    })
  }

  static async executeDoMercadoPago(notificacao: any): Promise<any> {

    return new Promise<string>( async(resolve, reject) => {
      if (!notificacao)
        return reject('Nenhuma notificação informada.')

      if (notificacao.executada)
        return resolve('');

      let pagamento: Pagamento =  await Pagamento.get(notificacao.codigo);

      if(!pagamento)
        return resolve('Nenhum pagamento identificado com esse codigo: ' + notificacao.codigo);

      let novoStatus: EnumStatusPagamento = MercadoPagoUtils.getStatus(notificacao.status);

      let erro: string  = await new ContratoService().mudouStatusPagamento(pagamento, novoStatus)

      if(erro){
        console.log(erro)
        await notificacao.registreErro(erro)
      } else {
        await notificacao.foiExecutada( )
      }
      resolve('');
    })
  }

  static async executePendentes( ) {

    return new Promise<void>( async(resolve, reject) => {
      let notificacoes = await new MapeadorDeNotificacaoMeioPagamento().listeAsync({ naoExecutadas: true})

      console.log('executar  notificações pagamento pendentes: ' + notificacoes.length)
      console.log(new Date())

      let contador = 1;

      for(let notificacao of notificacoes){
        console.log('notificação executar: ' + contador + ' => ' + notificacao.id);

        contador++

        await NotificacaoMeioPagamentoService.execute(notificacao)

      }

      console.log('terminou de executar')
      console.log(new Date())

      resolve();
    })
  }
}
