import {IServiceIntegracaoExternaERP} from "../domain/integracoes/IServiceIntegracaoExternaERP";
import {Pedido} from "../domain/delivery/Pedido";
import {DTOProdutoSincronizar} from "../lib/integracao/ecletica/DTOProdutoSincronizar";
import {Produto} from "../domain/Produto";
import {EnumTipoDeOrigem} from "../lib/emun/EnumTipoDeOrigem";
import {Comanda} from "../domain/comandas/Comanda";
import {TotvsChefUtils} from "../lib/integracao/totvs/TotvsChefUtils";
import {DTOPedidoTotvsChef} from "../lib/integracao/totvs/DTOPedidoTotvsChef";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {MapStatusPedidoMenew, MapStatusPedidoTotvs} from "./EnumStatusPedidosIntegrados";
import {MapeadorDeEndereco} from "../mapeadores/MapeadorDeEndereco";
import {EnumDisponibilidadeProduto} from "../lib/emun/EnumDisponibilidadeProduto";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {PedidoService} from "./PedidoService";
import {EnumStatusPedido} from "../lib/emun/EnumStatusPedido";
import {IntegracaoFoodyDelivery} from "../domain/integracoes/IntegracaoFoodyDelivery";
import {RequestParceiro} from "../domain/integracoes/RequestParceiro";

declare const global: any;

const axios = require('axios'),
      moment = require('moment'),
      crypto = require('crypto');

export class TotvsFoodService implements IServiceIntegracaoExternaERP{
  private host = 'chefweb.chef.totvs.com.br';
  private chave =  'lTIagogp';
  private codigoIntegracao = '115';

  private urlApiCadastro: string;
  private urlApiDelivery: string;

  private codigoEstabelecimento: string;
  constructor(public integracao: any) {
    if(this.integracao)
      this.codigoEstabelecimento = this.integracao.codigoEstabelecimento();

    let lojaHomologacao = (this.codigoEstabelecimento === '96721089PC4');

    if(global.desenvolvimento ){ //
      this.host = 'hml-integracaopedidosonline.azurewebsites.net'
      this.chave = 'L0IVKG3N'
      this.codigoIntegracao = '115';
    }

    this.urlApiCadastro = String(`http://${this.host}/IntegracaoPedidosOnline/CadastroService.svc`)
    this.urlApiDelivery = String(`http://${this.host}/IntegracaoPedidosOnline/DeliveryService.svc`)
  }

  static async  obtenhaAlteracoesPedidos(pedidos: any) {
    let listaPedidosAlterados: any = [];

    let promisses = [], ultimoPingMysql = new Date();

    for (let i = 0; i < pedidos.length; i++) {
      promisses.push( new Promise( async (resolve, reject) => {
        let pedido = pedidos[i];
        let empresa = pedido.empresa;
        empresa.setHorariosFuncionamento();

        if(empresa.estaAberta) {
          let serviceInstance: TotvsFoodService = empresa.integracaoDelivery.obtenhaService();

          let dados: any = await serviceInstance.obtenhaStatusPedido(pedido.codigo).catch((erroConsultar) => {
            console.log('#erro consulta pedido:' + pedido.id)
            console.log(erroConsultar)
          });

          if(moment().diff(ultimoPingMysql, 's') >= 30){
            console.log('#Fazendo ping no mysql para conexão nao morrer')
            await new MapeadorDePedido().facaPing();
            ultimoPingMysql = new Date();
          }

          if(dados) {
            console.log(String(`Pedido ${pedido.id} esta no status ${dados.status}`))
            let novoStatus = MapStatusPedidoTotvs.get( dados.status );
            if(novoStatus && novoStatus !== pedido.status){
              console.log('novo status veio do Totvs: ' + novoStatus)
              listaPedidosAlterados[pedido.id] = novoStatus;
            } else {
              console.log('monitorar depois, não realizar baixa agora.....')
            }
          }

          resolve('');
        }
      }))
    }

    await Promise.all(promisses);

    return listaPedidosAlterados;
  }

  obtenhaToken(fuso = -3): string{
    const dataToken =  String(`${moment().format('YYYYMMDDHHmmss')}${this.formatFuso(fuso)}00`)

    const token = String(`${this.codigoIntegracao}|${this.chave}|${dataToken}`),
      hashMd5 = crypto.createHash('md5').update(token).digest('hex'),
      basic = dataToken + ":" + hashMd5

    console.log('token totvs: ' + basic)

    return Buffer.from(basic).toString('base64');
  }

  formatFuso(number: any) {
    if (number < 0) {
      return '-' + String(-number).padStart(2, '0');
    } else {
      return '+' + String(number).padStart(2, '0');
    }
  }

  valideToken(): Promise<any> {
    return new Promise(async (resolve, reject) => {
      let dados: any = {
        parametros: {
          CodigoEstabelecimento: this.codigoEstabelecimento,
          CodigoIntegracao: this.codigoIntegracao,
          RequestID: null,
          TipoServico: null
        }
      }

      axios.post(String(`http://${this.host}/IntegracaoPedidosOnline/LojaService.svc/StatusEstabelecimento`), dados, {
        headers: {"Authorization": "Basic " +  this.obtenhaToken()}
      })
        .then((response: any) => {
          let result: any = response.data.StatusEstabelecimentoResult;

          if(result.Erros)
            return reject(this.retornoErro('validar token', result.Erros))

          resolve(true)
        }).catch( (response: any) => {
        reject(this.retornoErro('validar token', response))
      })
    })
  }


  private notifiquePedidoEmPreparacao(pedido: any, empresa: any) {

    if(pedido.fazParteMultipedido()) return;

    ExecutorAsync.execute( async (cbAsync: any) => {
      let contexto = require('domain').active.contexto;
      contexto.empresa = empresa
      contexto.idEmpresa =  empresa.id;
      await new PedidoService().altereStatus(pedido, empresa, EnumStatusPedido.EmPreparacao, true, false);
      await IntegracaoFoodyDelivery.notifiqueSistemaDelivery(pedido, empresa, null);
      cbAsync();
    }, () => {});
  }


  adicionePedido(pedido: Pedido, empresa: any): Promise<string> {
    return new Promise(async (resolve, reject) => {
      try{

        if(!pedido.ehDelivery()){
          if(!(pedido.contato as any).enderecos){
            let enderecosCadastro = await new MapeadorDeEndereco().listeAsync({ idContato: pedido.contato.id});
            if(enderecosCadastro.length)
              (pedido.contato as any).enderecos = enderecosCadastro
          }
        }

        let dados: any = {
          parametros: {
            Pedido:  this.obtenhaDTOPedido(pedido, empresa),
            CodigoEstabelecimento: this.codigoEstabelecimento,
            CodigoIntegracao: this.codigoIntegracao
          }
        }

        console.log(JSON.stringify(dados));

        axios.post(String(`${this.urlApiDelivery}/EnviarPedido`), dados,
          {  headers: {"Authorization": "Basic " +  this.obtenhaToken(empresa.fusoHorario)}})
          .then(async (response: any) => {
            await new RequestParceiro(pedido, 'totvs', dados).saveRetornoHttp(response);

            let result: any = response.data.EnviarPedidoResult;

            console.log(result)

            if(result.NumeroPedido){
              this.notifiquePedidoEmPreparacao(pedido, empresa)
              resolve(result.NumeroPedido);
            }  else {
              reject(this.retornoErro('enviar pedido', result.Erros && result.Erros.length ?  result.Erros : result))
            }
          }).catch( async (response: any) => {
            let msgErro: any = this.retornoErro('enviar pedido', response);
            await new RequestParceiro(pedido, 'totvs', dados).saveRetornoHttp(response, msgErro);
            reject(msgErro)

          })
      } catch (execption){
        console.log('**erro enviar pedido**')
        console.log(execption)
        reject(this.retornoErro('enviar pedido', execption))
      }
    })

  }

  obtenhaStatusPedido(referenciaExterna: string){
    return new Promise(async (resolve, reject) => {
      let dados: any = {
        parametros: {
          CodigoExternoPedido: referenciaExterna,
          CodigoEstabelecimento: this.codigoEstabelecimento,
          CodigoIntegracao: this.codigoIntegracao
        }
      }

      axios.post(String(`${this.urlApiDelivery}/StatusPedido`), dados,
        {  headers: {"Authorization": "Basic " +  this.obtenhaToken()}})
        .then((response: any) => {
          let result: any = response.data.StatusPedidoResult;
          //  "CodigoCancelamento": 0,
          //         "CodigoEntregador": null,
          //         "DataHoraConcluido": null,
          //         "DataHoraEntrega": null,
          //         "DescricaoCancelamento": "TESTANDO HOMOLOGACAO",
          //         "NomeEntregador": null,
          //         "Status": 4
          if(result.Status)
            return   resolve({ status: result.Status, motivoCancelamento: result.DescricaoCancelamento });

          reject(this.retornoErro('consultar status pedido', result.Erros && result.Erros.length ?  result.Erros : result))

        }).catch( (response: any) => { reject(this.retornoErro('consultar status pedido', response))      })
    })
  }


  alterePedido(pedidoNovo: Pedido, pedidoAntigo: Pedido, empresa: any): Promise<string> {
    return Promise.resolve("");
  }

  cancelePedido(pedido: Pedido): Promise<any> {
    return Promise.resolve(undefined);
  }

  fecheComanda(comanda: Comanda, empresa: any): Promise<any> {
    return Promise.resolve(undefined);
  }

  listeBandeiras(tipo: string): Promise<Array<any>> {
    return Promise.resolve(undefined);
  }



  listeProdutos(dataAtualizacao: any = null): Promise<any> {
    return new Promise(async (resolve, reject) => {
      let dados: any = {
        parametros: {
          CodigoEstabelecimento: this.codigoEstabelecimento,
          CodigoIntegracao: this.codigoIntegracao,
          RequestID: null,
          TipoServico: null
        }
      };

      if(dataAtualizacao) dados.parametros.DataAtualizacao =  String(`/Date(${new Date(dataAtualizacao).getTime()})/`)  ;

      console.log(dados)

      axios.post(String(`${this.urlApiCadastro}/ObterCardapio`), dados,
        {  headers: {"Authorization": "Basic " +  this.obtenhaToken() }})
        .then((response: any) => {
          let result: any = response.data.ObterCardapioResult;

          if(result.Erros)
            return reject(this.retornoErro('listar produtos', result.Erros))

          resolve(result.Produtos)
        }).catch( (response: any) => {
        reject(this.retornoErro('validar token', response))
      })
    });
    //

  }

  listeProdutosConvertidos(ultimaSincronizacao: any): Promise<Array<Produto>> {
    return new Promise(async (resolve, reject) => {
      let produtos: any = await this.listeProdutos(ultimaSincronizacao).catch((erroIntegracao: any) => {
        reject(erroIntegracao)
      })

      if(produtos){
        let produtosCatalogoDelivery: any =  TotvsChefUtils.convertaParaProdutos(produtos)

        resolve(produtosCatalogoDelivery)
      }

    })
  }

  listePrecosProdutos(ultimaSincronizacaoPrecos: any): Promise<Array<Produto>> {
    return this.listeProdutosConvertidos(ultimaSincronizacaoPrecos);
  }

  listeProdutosDisponiblidadeAtualizada(produtosIntegrados: any, ultimaSincronizacao: Date): Promise<Array<DTOProdutoSincronizar>> {
    let produtosAtualizados: any = [], sincronizandoTudo = !ultimaSincronizacao;

    return new Promise(async (resolve, reject) => {
      if(produtosIntegrados && produtosIntegrados.length){
        let produtosEstoque: any = await this.listeProdutosConvertidos(ultimaSincronizacao).catch(erro => reject(erro));

        if(produtosEstoque && produtosEstoque.length){
          produtosIntegrados.forEach( (produto: any) => {
            let produtoEstoque: any = produtosEstoque.find((item: any) => item.codigoPdv === produto.codigoPdv);

            if(produto.estaDisponivel()) {
              if ( (produtoEstoque && produtoEstoque.disponibilidade === EnumDisponibilidadeProduto.NaoDisponivel) ||
                (!produtoEstoque  && sincronizandoTudo))
                produtosAtualizados.push(new DTOProdutoSincronizar(produto, null, null, true))
            } else {
              if(produtoEstoque && produtoEstoque.disponibilidade === EnumDisponibilidadeProduto.SempreDisponivel)
                produtosAtualizados.push( new DTOProdutoSincronizar(produto, produto.obtenhaPrecos(), null, false))
            }
          })
        }
      }
      resolve(produtosAtualizados)
    })
  }

  listeProdutosIndisponiveis(): Promise<Array<any>> {
    return Promise.resolve( []);
  }

  obtenhaDTOPedido(pedido: Pedido, empresa: any): any {
    return  new DTOPedidoTotvsChef(pedido, empresa);
  }

  obtenhaProduto(codigo: any): Promise<any> {
    return Promise.resolve(undefined);
  }

  obtenhaProdutoConvertido(codigo: any): Promise<any> {
    return Promise.resolve(undefined);
  }

  obtenhaTipoDeOrigem(): EnumTipoDeOrigem {
    return undefined;
  }



  veririqueUpdates(data: any): Promise<any> {
    return Promise.resolve(undefined);
  }

  private retornoErro(operacao: string, erro: any) {
   // console.log(erro)
    let msgErro = String(`Falha ao ${operacao} (Totvs):`);

    if(erro.response){
      let response = erro.response;

      if(response.status === 403)
        msgErro = String(`${msgErro} Permissão negada (HTTP 403)`)
      else if(response.status === 401)
        msgErro = String(`${msgErro} Não autorizado (HTTP 401)`)
      else
        msgErro = String(`${msgErro} ${response.statusText}`)

      if(response.data)
        console.error(response.data)

    } else {
      if(Array.isArray(erro)){
        msgErro = String(`${msgErro} ${erro.join(', ')}`)
      }
      if(erro.message)
        msgErro = String(`${msgErro} ${erro.message}`)
    }

    console.log(msgErro)
    console.error(msgErro)

    return msgErro;
  }

}
