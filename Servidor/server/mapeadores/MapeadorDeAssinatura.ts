import {MapeadorBasico} from './MapeadorBasico';

export class MapeadorDeAssinatura extends MapeadorBasico {
  constructor() {
    super('assinatura', false);
  }

  async atualizeCartaoCredito(assinatura: any) {
    return new Promise<void>((resolve, reject) => {
      this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizeCartaoCredito'), assinatura, () => {
        resolve();
      });
    });
  }

  async atualizeDatasPagamento(assinatura: any){
    return this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizeDatasPagamento'), assinatura);
  }

  async atualizeDataVencimento(assinatura: any){
    return this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizeDataVencimento'), assinatura);
  }
}
