import {MapeadorBasico} from './MapeadorBasico';
import {TemplateDePromptDB} from "../domain/ia/TemplateDePromptDB";
import {MapeadorDeTrechoDePrompt} from "./MapeadorDeTrechoDePrompt";

export class MapeadorDeTemplatePromptDB extends MapeadorBasico {
  constructor() {
    super('templateDePromptDB', false);
  }

  async listeAsync(query: any): Promise<any> {
    const templates: Array<any> = await super.listeAsync(query);

    templates.forEach( template => {
      MapeadorDeTrechoDePrompt.processeTrechos(template.trechosDePrompt);
    });

    return templates;
  }
}
