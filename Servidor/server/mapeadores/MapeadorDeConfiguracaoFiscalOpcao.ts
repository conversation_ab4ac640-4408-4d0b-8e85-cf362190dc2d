import { MapeadorBasico } from './MapeadorBasico';
import { ConfiguracaoFiscalOpcao } from '../domain/nfce/configuracoes/ConfiguracaoFiscalOpcao';

export class MapeadorDeConfiguracaoFiscalOpcao extends MapeadorBasico {

    constructor() {
        super('configuracaoFiscalOpcao',  false);
    }

    selecionePorOpcaoAdicionalId(opcaoAdicionalProdutoId: any) {
        return this.selecioneSync({opcaoAdicionalProdutoId: opcaoAdicionalProdutoId});
    }

    persista(configuracoesNotaFiscal: any) {
        if(!configuracoesNotaFiscal) return Promise.reject('Configurações não informadas')

        if(!configuracoesNotaFiscal.id) {
            return this.insiraSync(configuracoesNotaFiscal)
        }

        return this.atualizeSync(configuracoesNotaFiscal)
    }

    remova(params: any) {
        return new Promise<void>((resolve, reject) => {
            if (!params || !params.id) return reject('ID não informado');
            
            this.gerenciadorDeMapeamentos.atualize(this.metodo('remova'), params, () => {
                resolve();
            });
        });
    }
} 