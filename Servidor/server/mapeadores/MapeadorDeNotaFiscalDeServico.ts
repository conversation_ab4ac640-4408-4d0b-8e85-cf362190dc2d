import {MapeadorBasico} from "./MapeadorBasico";

export class MapeadorDeNotaFiscalDeServico extends MapeadorBasico {
  constructor() {
    super('notaFiscalDeServico', false);
  }


  obtenhaUltimoRps(): Promise<number> {
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneUltimoRps'), {});
  }

  foiAprovada(nota: any): Promise<any> {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('foiAprovada'), nota);
  }
}
