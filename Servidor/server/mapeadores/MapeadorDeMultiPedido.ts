import {MapeadorBasico} from "./MapeadorBasico";
import {MultiPedido} from "../domain/delivery/MultiPedido";
import {MapeadorDePedido} from "./MapeadorDePedido";


export class MapeadorDeMultipedido extends MapeadorBasico {
  constructor() {
    super('multipedido');
  }

  selecioneSync(query: any): Promise<any> {
    if (typeof query !== 'object')
      query = {id: query};

    return new Promise<any>( (resolve, reject) => {
      this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecione'), query).
      then( async (multipedido: MultiPedido) => {
        if(!multipedido) return resolve(null);

        let mapeadorDePedido = new MapeadorDePedido();

        mapeadorDePedido.desativeMultiCliente();

        for(let i = 0; i < multipedido.pedidos.length; i++){
          let pedido: any = multipedido.pedidos[i];
          await mapeadorDePedido.carregueItensPedido(pedido, {idPedido: pedido.id , idEmpresa: pedido.empresa.id});
        }
        resolve(multipedido)
      })
    })
  }
}
