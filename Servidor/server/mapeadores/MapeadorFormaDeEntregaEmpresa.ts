import {MapeadorBasico} from "./MapeadorBasico";
import {FormaDeEntregaEmpresa} from "../domain/delivery/FormaDeEntregaEmpresa";

export class MapeadorFormaDeEntregaEmpresa   extends MapeadorBasico {
  constructor() {
    super('formaEntregaEmpresa', false);
  }

  atualizeArquivoKML(formaDeEntrega: FormaDeEntregaEmpresa) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeArquivoKML'), formaDeEntrega);
  }

  atualizeTipoCobranca(formaDeEntrega: FormaDeEntregaEmpresa) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeTipoCobranca'), formaDeEntrega);
  }


  atualizeCepObigatorio(formaDeEntrega: FormaDeEntregaEmpresa){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeCepObigatorio'), formaDeEntrega);
  }
}
