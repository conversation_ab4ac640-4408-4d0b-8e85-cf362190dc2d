import {MapeadorBasico} from "./MapeadorBasico";

export class MapeadorDeClienteApi extends MapeadorBasico {
  constructor() {
    super('clienteApi');
  }

  atualizeAtivo(clienteApi: any){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeAtivo'),
      clienteApi);
  }


  atualizeAcessoDireto(clienteApi: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeAcessoDireto'),
      clienteApi);

  }

  definaSistema(clienteApi: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('definaSistema'),
      clienteApi)
  }
}
