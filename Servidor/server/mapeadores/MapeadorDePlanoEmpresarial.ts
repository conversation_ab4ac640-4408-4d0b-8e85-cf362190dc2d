import {MapeadorBasico} from './MapeadorBasico';
import {PlanoVantagem} from "../domain/faturamento/PlanoVantagem";

export class MapeadorDePlanoEmpresarial extends MapeadorBasico {
  constructor() {
    super('planoempresarial', false);
  }


  async atualizePublico(planoEmpresarial: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizePublico'), planoEmpresarial);
  }
}
