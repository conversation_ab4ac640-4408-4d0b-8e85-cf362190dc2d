import {Empresa} from "../Empresa";
import {<PERSON><PERSON><PERSON>} from "../Usuario";
import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorDeCancelamentoEmpresa} from "../../mapeadores/MapeadorDeCancelamentoEmpresa";

export class CancelamentoEmpresa extends ObjetoPersistente{
  horario: Date;
  motivosCancelamento: Array<any> = []
  constructor(public empresa: Empresa, public operador: Usuario, public motivo: string) {
    super();
    this.horario = new Date();
  }

  mapeador(): any {
    return new MapeadorDeCancelamentoEmpresa();
  }
}
