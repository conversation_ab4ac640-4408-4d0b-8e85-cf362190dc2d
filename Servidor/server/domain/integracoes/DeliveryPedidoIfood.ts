import {DeliveryPedido} from "./DeliveryPedido";
import {EnumStatusEntregaIfood} from "../../lib/integracao/ifood/EnumStatusEntregaIfood";

export class DeliveryPedidoIfood extends DeliveryPedido {
  constructor(pedido: any, operador: any, order: any, cotacao: any = null) {
    super('ifood', pedido, operador, cotacao)
    this.status = EnumStatusEntregaIfood.Pendente;
    if(cotacao){
      this.deliveryId = cotacao.id;
      this.status = EnumStatusEntregaIfood.Cotado
      if(cotacao.loja){
        this.dados.cotacao.idLoja = cotacao.loja.id;
        this.dados.cotacao.nomeLoja = cotacao.loja.nome;
      }
    } else {
      if(pedido && !pedido.doIfood()) {
        ///  pedidos de fora ifood e automatico aceito quando registra order
        this.status = EnumStatusEntregaIfood.Solicitado;
        this.foiAceita = true;
      }

      if(order) {
        this.deliveryId = order.id;
        this.dados = { orderId: order.id, trackingUrl: order.trackingUrl}
      }
    }
  }


  setDados(dados: any): void {
    if(!this.dados)   this.dados = {}
    if(typeof this.dados === 'string')
      this.dados = JSON.parse(this.dados)

    if(dados.quote){
      this.dados.cotacao = {
        id: dados.id,
        distancia: dados.distance,
        tempo: dados.deliveryTime,
        valorBruto: dados.quote.grossValue,
        desconto: dados.quote.discount,
        valor: dados.quote.netValue,
        expiracao: dados.expirationAt,
        paymentMethods: dados.paymentMethods
      }
    }

    if(dados.pickupCode  )
      this.dados.codigoRetirada = dados.pickupCode

    if(dados.CODE)
      this.dados.codigoConfirmacaoEntrega = dados.CODE;

    if(dados.rejectReason)
      this.dados.motivoRejeicao = dados.rejectReason

    if(dados.nextRetry)
      this.dados.nextRetry = dados.nextRetry

    if(dados.workerName){
      this.dados.courier  = {
        name: dados.workerName,
        externalUuid: dados.workerExternalUuid,
        foto: dados.workerPhotoUrl,
        tipo: dados.workerVehicleType
      }
    }

    if(dados.HANDSHAKE_CODE)
      this.dados.codigoCancelamentoRetorno = dados.HANDSHAKE_CODE

    if(dados.operador) this.dados.operador = dados.operador
  }


  getCotacao(){
    if(this.status === EnumStatusEntregaIfood.Cotado)
      return this.getDados().cotacao

    return null;
  }

  encerrouPrazo(): boolean {
    return false;
  }

  estaPendente(): boolean {
    return this.status === EnumStatusEntregaIfood.Pendente;
  }

  foiCancelado(): boolean {
    return this.status === EnumStatusEntregaIfood.Cancelado;
  }

  foiFinalizado(): boolean {
    return  this.status === EnumStatusEntregaIfood.EntregaConfirmada;
  }

  getEntregador(): any {
    return  this.getDados().courier;
  }

  getMotivoCancelamento(): any {
    //SAFE_MODE_ON, OFF_WORKING_SHIFT_POST, CLOSED_REGION, SATURATED_REGION
    let motivoRejeicao = this.getDados().motivoRejeicao;

    if(motivoRejeicao) return motivoRejeicao;
  }

  getPincodeEntrega(): string {
    return  this.getDados().codigoConfirmacaoEntrega
  }

  getUrlRastreamento(): string {
    return this.getDados().trackingUrl;
  }

  getVeiculo(): any {
    let entregador: any = this. getEntregador();

    let veiculo: any = {};

    if(entregador && entregador.tipo){
      veiculo = {
        tipo: entregador.tipo,
        carro: entregador.tipo === 'CAR'
      }

      veiculo.descricao = String(`${veiculo.tipo}`)
    }

    return veiculo;
  }

  obtenhaListaStatus(): any {
    return EnumStatusEntregaIfood
  }

  podeCancelar(): boolean {
    return this.status === EnumStatusEntregaIfood.Solicitado ||  this.status === EnumStatusEntregaIfood.Pendente;
  }

  podeSolicitarNovamente(): boolean {
    return    this.status === EnumStatusEntregaIfood.Cancelado
  }

  setFoiAceita(): void {
    let statusAntesConfirmacao: any = [ EnumStatusEntregaIfood.Cancelado,  EnumStatusEntregaIfood.Pendente,
      EnumStatusEntregaIfood.Cotado, EnumStatusEntregaIfood.Negado];

    this.foiAceita =  !statusAntesConfirmacao.includes(this.status)
  }

  listaStatusAntesColeta(){
    return  [EnumStatusEntregaIfood.Solicitado, EnumStatusEntregaIfood.Pendente, EnumStatusEntregaIfood.Negado,
      EnumStatusEntregaIfood.EntregadorAlocado, EnumStatusEntregaIfood.AcaminhoDaLoja]

  }
}
