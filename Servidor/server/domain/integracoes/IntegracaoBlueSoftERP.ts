import {IServiceIntegracaoExternaERP} from "./IServiceIntegracaoExternaERP";
import {BlueSoftERPService} from "../../service/integracoes/BlueSoftERPService";
import {EnumStatusPedido} from "../../lib/emun/EnumStatusPedido";
import {IntegracaoDeliveryComJson} from "./IntegracaoDeliveryComJson";

export class IntegracaoBlueSoftERP extends IntegracaoDeliveryComJson{
  loja: number;
  constructor(dados: any) {
    if(dados){
      super(dados.id, dados.empresa, dados.token, 'bluesoft');
      this.loja = dados.loja.lojaKey;
      this.configuracoesEspecificas = {
         loja: {
           id: dados.loja.lojaKey,
           nome: dados.loja.nomeFantasia,
           cpfCnpj: dados.loja.cpfCnpj
         },
         grupo: dados.configuracoesEspecificas.grupo,
         idEcommerce: dados.configuracoesEspecificas.idEcommerce,
         canalVendaKey: dados.configuracoesEspecificas.canalVendaKey
      }
    } else {
      super( null, null, null, 'bluesoft');
    }
  }

  notificarNovo(pedido: any): boolean {
    return Number(pedido.status) === EnumStatusPedido.Novo;
  }


  obtenhaCredencial(): any {
    let config = this.configuracoesEspecificas;

    let credencial: any =  {
       token: this.token,
       loja: this.loja,
       cpfCnpj: config.loja.cpfCnpj,
       grupo: config.grupo || config.loja.grupo || 'soneda' , //fixado para soneda, usado url da api bluesoft,
       idEcommerce: config.idEcommerce  || 1,   /// 1 geralmente o ecomerce padrao
       canalVendaKey: config.canalVendaKey ||  2   // 1 - Ecommerce, 2 - Delivery no soneda
    }

    return credencial;
  }

  obtenhaService(): IServiceIntegracaoExternaERP {
    return new BlueSoftERPService(this.obtenhaCredencial());
  }

  cepObrigatorio(){
    return true;
  }

}
