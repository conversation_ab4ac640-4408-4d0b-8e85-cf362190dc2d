import {NotificacaoSistemaExterno} from "./NotificacaoSistemaExterno";
import {MapeadorDeNotificacaoMesa} from "../../mapeadores/MapeadorDeNotificacaoMesa";
import {MapeadorDeComanda} from "../../mapeadores/MapeadorDeComanda";
import {MapeadorDeMesa} from "../../mapeadores/MapeadorDeMesa";

export class NotificacaoMesa extends NotificacaoSistemaExterno{
  comando: string;
  horarioNotificado: Date;
  public origem: string;
  public comanda: any;
  constructor(public empresa: any = null,  public numero: string = null, public operacao: string = null,
              dados: any = null) {
    super(null)
    this.origem = 'Ecletica';
    this.horarioNotificado = new Date();
    this.comando  = 'fechar';
    this.dados   = dados;
  }

  async obtenhaComanda(): Promise<any>{
    return new Promise<any>( async(resolve, reject) => {
      if(!this.numero) return reject('Nenhuma mesa/comanda informada');

      let mesa = await new MapeadorDeMesa().selecioneSync({codigoPdv: this.numero, idEmpresa: this.empresa.id});

      if(!mesa)
        return reject(String(`${this.operacao ? this.operacao.toUpperCase() : 'mesa/comanda'} número "${this.numero}" não encontrada` ))

      if(this.carregarPedidos()){
        let comanda  = await  new MapeadorDeComanda().obtenhaComanda(
          {  idEmpresa: this.empresa.id, idMesa: mesa.id, naoFechada: true})

        return resolve(comanda);
      } else {
        let comanda  = await  new MapeadorDeComanda().selecioneSync(
          {  idEmpresa: this.empresa.id, idMesa: mesa.id, naoFechada: true})

        return resolve(comanda);
      }
    });
  }

  carregarPedidos(){
    return false;
  }

  async valide(contexto: any = null, catalogo: any = null): Promise<any> {
    if(!this.operacao)
      return Promise.resolve('tipo_operacao não informado')

    if(!this.numero)
      return  Promise.resolve(`numero da ${this.operacao} não informado`);

    let dados =  this.getDados();

    if(!dados.rede || !dados.loja)
      return  Promise.resolve('Rede ou Loja não informado')

    let ehDinheiro = dados.pagamento.dinheiro > 0;

    if(!ehDinheiro){
      if(!dados.formasDePagamento)
        return Promise.resolve('Forma de pagamento não informado')

      if(  !Array.isArray(dados.formasDePagamento) || !dados.formasDePagamento.length)
        return Promise.resolve('Informe pelo menos uma forma de pagamento')
    }

    let errosPagamentos = []

    if(dados.formasDePagamento){
      for(let i  = 0; i < dados.formasDePagamento.length; i++){
        let formaPagamento: any = dados.formasDePagamento[i];

        if(!formaPagamento.flag_de_pagamento)  errosPagamentos.push(String(`Nenhuma "flag_de_pagamento" na forma de pagamento "${i}" foi informado`))
        if(!formaPagamento.valor) errosPagamentos.push(String(`Nenhum "valor" na forma de pagamento "${i}" foi informado`))
        if(!formaPagamento.codigo_bandeira)
          errosPagamentos.push(String(`Nenhum "codigo_bandeira" na forma de pagamento "${i}" foi informado`))
      }
    }



    if(errosPagamentos.length) return Promise.resolve(errosPagamentos.join(', '))

    if(!dados.pagamento)
      return  Promise.resolve('Pagamento não informado')

    if(typeof this.dados !== 'string')
      this.dados = JSON.stringify(this.dados)

    return Promise.resolve();

  }

  mapeador(): any {
    return new MapeadorDeNotificacaoMesa();
  }


}
