
import {EnumStatusPedido} from "../../lib/emun/EnumStatusPedido";
import {IServiceIntegracaoExternaERP} from "./IServiceIntegracaoExternaERP";
import {EcleticaService} from "../../service/EcleticaService";
import {IntegracaoDeliveryComJson} from "./IntegracaoDeliveryComJson";

export class IntegracaoEcleticaERP extends IntegracaoDeliveryComJson{
  rede: number;
  loja: number;
  unidadeChina: string;
  unidadeGendai: string;
  naoSincronizarGlobal: boolean;
  naoSincronizarDisponiveis: boolean;
  constructor(dados: any) {
    if(dados){
      super(dados.id, dados.empresa, '', 'ecletica');
      this.rede = dados.rede;
      this.loja = dados.loja;
      this.unidadeChina = dados.unidadeChina;
      this.unidadeGendai = dados.unidadeGendai;
      this.naoSincronizarGlobal = dados.naoSincronizarGlobal;
      this.naoSincronizarDisponiveis = dados.naoSincronizarDisponiveis;
      this.configuracoesEspecificas = {
        observacoesNoInicio : dados.observacoesNoInicio,
        integrarComComandas : dados.integrarComComandas ? true : false,
        integrarComDMWS: dados.integrarComDMWS ? true : false
      }
    } else {
      super( null, null, null, 'ecletica');
    }
  }

  obtenhaCredencial(): any{
    let credencial: any =  {
      token: this.token,
      rede: this.rede,
      loja: this.loja,
      comDMWS: this.integradoComServicoDMWS()
    }

    return credencial;
  }

  inicializeToken(): Promise<any> {
    return this.obtenhaService().valideToken();
  }

  notificarNovo(pedido: any): boolean {
    return Number(pedido.status) === EnumStatusPedido.Novo;
  }

  obtenhaService(): IServiceIntegracaoExternaERP {
    return new  EcleticaService(this.obtenhaCredencial());
  }

  cepObrigatorio(){
    return true;
  }

  enviarObservacoesNoInicio(){
    return this.configuracoesEspecificas && this.configuracoesEspecificas.observacoesNoInicio
  }

  integrarComComandas(){
    return this.configuracoesEspecificas && this.configuracoesEspecificas.integrarComComandas
  }

  integradoComServicoDMWS(){
    return this.configuracoesEspecificas && this.configuracoesEspecificas.integrarComDMWS
  }

}
