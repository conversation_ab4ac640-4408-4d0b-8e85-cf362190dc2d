import {IServiceIntegracaoExternaERP} from "./IServiceIntegracaoExternaERP";
import {IntegracaoDeliveryComJson} from "./IntegracaoDeliveryComJson";
import {SaiposERPService} from "../../service/integracoes/SaiposERPService";
import * as moment from "moment";
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
import {MapeadorDeIntegracaoDelivery} from "../../mapeadores/MapeadorDeIntegracaoDelivery";
import {EnumStatusPedido} from "../../lib/emun/EnumStatusPedido";

export class IntegracaoSaiposERP extends IntegracaoDeliveryComJson{

  constructor(dados: any) {
    if(dados){
      super(dados.id, dados.empresa, '' , 'saipos');
      this.configuracoesEspecificas = {
        cod_store:  dados.cod_store,
        idPartner:  dados.idPartner,
        secret:  dados.secret,
        idEmpresa: dados.empresa ? dados.empresa.id : null
      }
    } else {
      super( null, null, null, 'saipos');
    }
  }

  notificarNovo(pedido: any): boolean {
    return Number(pedido.status) === EnumStatusPedido.Novo;
  }


  inicializeToken(): Promise<any> {
    return new Promise((resolve, reject) => {
      let service = new SaiposERPService(null, null, this.lojaHomologacao());

      let idPartner = this.configuracoesEspecificas.idPartner;

      (service as SaiposERPService).obtenhaToken(idPartner).then( (token: string) => {
        this.setNovoToken(token);
        this.obtenhaService().valideToken().then( () => {
          resolve(true);
        }).catch(erro => reject(erro))
      }).catch(erro => reject(erro))
    })
  }

  setNovoToken(token: string){
    let validade = moment().add(23, 'hours').toDate(); // valido por 24hs
    this.configuracoesEspecificas.token = token;
    this.configuracoesEspecificas.validadeToken = validade;
  }

  obtenhaCredencial(): any {
    let config = typeof this.configuracoesEspecificas === 'string' ?
      JSON.parse(this.configuracoesEspecificas) : this.configuracoesEspecificas;

    if (config.token  && moment(config.validadeToken).diff(moment(), 'hours') > 0) {
      console.log('Usando token válido, até ' + config.validadeToken)
      return Promise.resolve(config.token)
    }

    return this.renoveToken();
  }

  renoveToken(){
    return new Promise((resolve, reject) => {
      let config = typeof this.configuracoesEspecificas === 'string' ?
        JSON.parse(this.configuracoesEspecificas) : this.configuracoesEspecificas;

      console.log('Renovar token..')
      new SaiposERPService(null, null, this.lojaHomologacao()).
      obtenhaToken(config.idPartner).then( async ( novoToken: string) => {
        this.setNovoToken(novoToken);
        if(!this.empresa || !this.empresa.id)
          this.empresa = await new MapeadorDeEmpresa().selecioneSync(this.configuracoesEspecificas.idEmpresa);
        await new MapeadorDeIntegracaoDelivery().atualizeSync(this);
        await new MapeadorDeEmpresa().removaDasCaches(this.empresa)
        console.log('salvou novo token: ' +  this.configuracoesEspecificas.validadeToken)
        resolve(novoToken)
      }).catch(erro => {
        console.log('Falha ao obter novo Token')
        console.log(erro)
        resolve(null)
      });
    })
  }

  obtenhaService(): IServiceIntegracaoExternaERP {
    return new SaiposERPService(this, this.codigoLoja(), this.lojaHomologacao());
  }

  codigoLoja(){
    return this.configuracoesEspecificas.cod_store
  }

  tempoEstimadoEntregaObrigatorio(){
    return true;
  }

  private lojaHomologacao() {
    return this.configuracoesEspecificas.idEmpresa === 985
  }
}
