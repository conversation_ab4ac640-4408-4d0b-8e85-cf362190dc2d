import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorIfoodUserCode} from "../../mapeadores/MapeadorIfoodUserCode";
import * as moment from "moment";

export class IfoodUserCode extends ObjetoPersistente{
  public expiresIn: Date;
  constructor( public empresa: any, public userCode: string, public authorizationCodeVerifier: string,
                public verificationUrl: string, expiresIn: number) {
    super();

    if(expiresIn)
      this.expiresIn = moment().add(expiresIn, 'seconds').toDate();
  }

  mapeador(): any {
    return new MapeadorIfoodUserCode();
  }

  expirou(){
    return moment().diff(moment(this.expiresIn), 's' ) >= 0;
  }
}
