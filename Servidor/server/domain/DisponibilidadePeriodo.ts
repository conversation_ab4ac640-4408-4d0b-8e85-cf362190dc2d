import {ObjetoPersistente} from "./ObjetoPersistente";
import { MapeadorDeDisponibilidadePeriodo } from "../mapeadores/MapeadorDeDisponibilidadePeriodo";
import {DateUtils} from "../lib/DateUtils";
import * as moment from "moment";
import {DisponibilidadePeriodoDia} from "./DisponibilidadePeriodoDia";

export class DisponibilidadePeriodo extends ObjetoPersistente{
  public disponibilidade: any;
  constructor(public dias: Array<DisponibilidadePeriodoDia> = [],
              public horaInicio: any = null, public horaFim: any = null) {
    super( );
    if(horaInicio)
      this.horaInicio = String(`${this.horaInicio.substr(0, 5)}:00`)  //garantir sempre 00 no fim
    if(horaFim)
      this.horaFim = String(`${this.horaFim.substr(0, 5)}:00`)  //garantir sempre 00 no fim
  }

  async salve(){

    await super.salve(true)
    await this.mapeador().insiraDias(this)
  }

  clone(){
    let periodoCopia = new DisponibilidadePeriodo([], this.horaInicio, this.horaFim)

    this.dias.forEach((periodoDia: DisponibilidadePeriodoDia) => {
      periodoCopia.dias.push(periodoDia.clone())
    })

   return periodoCopia;
  }

  mapeador(): any {
    return  new MapeadorDeDisponibilidadePeriodo();
  }

  temODia(dia: number){
    return this.dias.find((_dia: any) => _dia.dia === dia) != null;
  }

  ehOMesmo(outro: DisponibilidadePeriodo){
    if(this.horaInicio !== outro.horaInicio)
      return false;
    if(this.horaFim !== outro.horaFim)
      return false;

    let temTodosDias = true;

    this.dias.forEach((dia: DisponibilidadePeriodoDia) => {
        if(!outro.temODia(dia.dia))
          temTodosDias = false;
    })

    outro.dias.forEach((dia: DisponibilidadePeriodoDia) => {
      if(!this.temODia(dia.dia))
        temTodosDias = false;
    })


    return temTodosDias;

  }

  estaDisponivel(fusoHorario: any){
    let agoraNoFuso: Date = DateUtils.agoraNofuso(fusoHorario);

    let dia = agoraNoFuso.getDay();

    if(!this.dias || !this.dias.length) return false;

    let horarioHoje = this.dias.find( (horario: any) => horario.dia === dia)

    if(!horarioHoje) return false;

    if(!this.horaInicio || !this.horaFim) return true;

    let agora = Number(moment(agoraNoFuso).format('HHmm'));

    return Number(this.horaInicio.substr(0, 5).replace(/\D/g, '')) <=  agora  &&
      agora  <= Number(this.horaFim.substr(0, 5).replace(/\D/g, ''))
  }

}
