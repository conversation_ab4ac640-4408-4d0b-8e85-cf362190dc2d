import {Empresa} from "./Empresa";
import {EnumStatusCampanha} from "./EnumStatusCampanha";
import {Filtro} from "./Filtro";
import {EnumTipoDeEnvioCampanha} from "./EnumTipoDeEnvioCampanha";
import {EnumOrigemContatosCampanha} from "./EnumOrigemContatosCampanha";
import {Rede} from "./Rede";

export class CampanhaRede {
  id: number;
  nome: string;
  mensagem: string;
  dataCriacao: Date;
  empresa: Empresa;
  rede: string;
  status: EnumStatusCampanha;
  tipoDeEnvio: EnumTipoDeEnvioCampanha;
  horarioEnvio: Date;
  ativa = true;
  foiTestada = false;
  origemContatos: EnumOrigemContatosCampanha;
  foiReplicada = false;
  redes: Array<Rede>;
  linkImagem: string;
  constructor() {
    this.dataCriacao = new Date();
  }

  async carregueContatos() {
    return new Promise( (resolve, reject) => {
      return resolve([]);
    });
  }
}
