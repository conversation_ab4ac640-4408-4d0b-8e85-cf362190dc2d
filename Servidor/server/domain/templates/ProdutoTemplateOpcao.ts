export class ProdutoTemplateOpcao{
  disponivel  = true;
  template: any;
  idAdicional: any;
  codigoPdv: string;
  semborda: boolean
  constructor(public id: number, public nome: string, public valor: number,
              public descricao: string = '', public tamanho: any = null){

  }

  clone(): ProdutoTemplateOpcao {
    let cloneOpcao = new ProdutoTemplateOpcao(null, this.nome, this.valor, this.descricao, this.tamanho)
    cloneOpcao.disponivel = this.disponivel

    return cloneOpcao
  }
}
