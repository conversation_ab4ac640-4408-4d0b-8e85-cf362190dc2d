import { PizzaTamanhoSaboresDePara} from "../integracoes/PizzaTamanhoSaboresDePara";


export class ProdutoTemplateTamanho {
  disponivel  = true;
  template: any;
  deParaTamanhoSabores: Array<PizzaTamanhoSaboresDePara> = [];
  pontosGanhos: number;
  cashback: number;
  constructor(public id: number, public descricao: string,
              public qtdePedacos: number,   public qtdeSabores: number ) {
  }


  clone(): ProdutoTemplateTamanho {
    let tamanhoClone: any = new ProdutoTemplateTamanho(null, this.descricao, this.qtdePedacos, this.qtdeSabores)
    let eu: any =  this
    eu.templateClonado = tamanhoClone
    tamanhoClone.templateTamanhoOriginal = eu;
    return tamanhoClone
  }
}
