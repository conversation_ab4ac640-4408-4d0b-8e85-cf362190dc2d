import {AdicionalDeProdutoMultiplaEscolha} from "./AdicionalDeProdutoMultiplaEscolha";
import {Empresa} from "../Empresa";
import {OpcaoDeAdicionalDeProduto} from "./OpcaoDeAdicionalDeProduto";
import {EnumTipoDeCobrancaDeAdicional} from "../../lib/emun/EnumTipoDeCobrancaDeAdicional";

export class AdicionalDePedidoMultiplaEscolha extends AdicionalDeProdutoMultiplaEscolha {
  empresa: Empresa;

  constructor(nome: string, obrigatorio: boolean, opcoesDisponiveis: Array<OpcaoDeAdicionalDeProduto>,
              public qtdMinima: number, public qtdMaxima: number, public podeRepetirItem: boolean,
              public tipoDeCobranca: EnumTipoDeCobrancaDeAdicional = EnumTipoDeCobrancaDeAdicional.SOMA) {
    super(nome, obrigatorio, opcoesDisponiveis, qtdMinima, qtdMaxima, podeRepetirItem, tipoDeCobranca, 'pedido');
  }
}
