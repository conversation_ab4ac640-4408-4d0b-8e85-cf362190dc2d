import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {MapeadorDeCashbackReserva} from "../../mapeadores/MapeadorDeCashbackReserva";

export class CashbackReserva extends ObjetoPersistente {
  public idReserva: number;
  constructor(public sistema: string,  public valor: number, public idCliente: number,
              public idEmpresa: number, public idMarca: number, public idLoja: number,
              public idFidelidade: number, public versaoFidelidade: number) {
    super()
  }

  static novaReserva( valor: number, dados: any){
    return new CashbackReserva( dados.sistema, valor, dados.id_cliente,
                               dados.id_empresa, dados.id_marca, dados.id_loja,
                                dados.id_programa_fidelidade, dados.id_programa_fidelidade_versao)
  }

  mapeador(): MapeadorBasico {
    return  new MapeadorDeCashbackReserva();
  }
}
