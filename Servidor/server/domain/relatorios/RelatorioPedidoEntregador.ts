import {Entregador} from "../delivery/Entregador";
import {Endereco} from "../delivery/Endereco";

export class RelatorioPedidoEntregador {
  id: number;
  guidPedido: string;
  codigoPedido: number;
  valorPedido: number;
  taxaEntrega: number;
  descontoTaxaEntrega: number;
  ultimaAtualizacao: Date;
  nomeCliente: string;
  telefoneCliente: string
  enderecoEntrega: Endereco;
  entregador: Entregador;
  enderecoEntregaCompleto: string;

  calculeEnderecoCompleto() {
    this.enderecoEntregaCompleto = this.enderecoEntrega.obtenhaEnderecoCompleto()
  }
}
