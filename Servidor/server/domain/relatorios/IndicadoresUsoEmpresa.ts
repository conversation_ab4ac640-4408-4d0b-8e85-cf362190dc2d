import {Empresa} from "../Empresa";

export class IndicadoresUsoEmpresa {
  empresa: Empresa;
  dataCalculo: Date; //data de
  mensalidade: number //valor que o cliente paga hoje na mensalidade

  //Indicadores gerais
  qtdAcoesUltimos4dias: number
  //selecT count(*) qtde_ultimos_4_dias, count(*)  > 0 usou_ultimos_4_dias from acao_contato where empresa_id = 14 and horario >= curdate() - interval 4 day

  mediaUsosMes: number;

  //Indicadores pedidos

  valorTotalPedidosMes: number //faturamento dos pedidos confirmados

  //Indicadores fidelidade
  valorTotalFidelidadeMes: number //faturamento fidelidade


  qtdPedidosMes: number;
  qtdPedidos24horas: number;
  qtdPedidos7dias: number;


  //Indicador de engajamento
  fezLogin7dias: boolean //fez login dos últimos 7 dias todos os dias
  mediaUsosPorDia: number //quantos usos fez por dia no mes
  qtdLoginsNoMes: number //quantos logins fez nesse mes

  //Indicador de campanha
  fezCampanhaEsseMes:boolean
  qtdCampanhasMes: number
  mensagensPendentes: number //número de mensagens não enviadas


  //INDICADORES DE RISCO
  usouNosUltimos4dias: boolean;
  pedidosCompensamMensalidade: boolean //Quantidade de pedidos maior que 10% da mensalidade proporcional no mes
  fidelidadeCOmpensaMensalidade: boolean //quantidade de fidelidade maior que 10% da mensalidade proporcional do mes

}

/*
select
	e.id empresa_id,
    e.nome empresa_nome,
    if(ig.qtde_ultimos_4_dias, ig.qtde_ultimos_4_dias, 0)  indicadores_qtde_ultimos_4_dias,
    if(ig.usou_ultimos_4_dias, ig.usou_ultimos_4_dias, false) indicadores_usou_ultimos_4_dias,
    if(mu.media_usos_mes, mu.media_usos_mes, 0) indicadores_media_usos_mes
from empresa e
 left join (selecT empresa_id, count(*) qtde_ultimos_4_dias, count(*)  > 0 usou_ultimos_4_dias from acao_contato where horario >= curdate() - interval 4 day group by empresa_id) ig
			on e.id = ig.empresa_id
 left join (select empresa_id, count(*) / datediff(now(), DATE_ADD(LAST_DAY(DATE_SUB(NOW(), INTERVAL 1 MONTH)), INTERVAL 1 DAY)) media_usos_mes
			from acao_contato where month(horario) =  month(now()) and year(horario) = year(now()) and day(horario) < day(now()) group by empresa_id) mu
			on e.id = mu.empresa_id
  order by indicadores_media_usos_mes desc

 */
