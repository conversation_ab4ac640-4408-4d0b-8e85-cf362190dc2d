import {<PERSON>du<PERSON>} from "./Produto";
import {Foto} from "./Foto";
import {EnumMeioDeEnvio} from "./EnumMeioDeEnvio";
import {Usuario} from "./Usuario";
import {Modu<PERSON>} from "./Modulo";
import * as _ from 'underscore';
import {Cardapio} from "./delivery/Cardapio";
import {Categoria} from "./delivery/Categoria";
import {DateUtils} from "../lib/DateUtils";
import {FormaDeEntregaEmpresa} from "./delivery/FormaDeEntregaEmpresa";
import {ConfigImpressao} from "./ConfigImpressao";
import {NumeroWhatsapp} from "./NumeroWhatsapp";
import {EnumTipoDeLoja} from "../lib/emun/EnumTipoDeLoja";
import {IntegracaoPedidoFidelidade} from "./IntegracaoPedidoFidelidade";
import {EnumMeioDePagamento} from "./delivery/EnumMeioDePagamento";
import {DominioDaEmpresa} from "./DominioDaEmpresa";
import {EnumMomentoImprimirAuto} from "../lib/emun/EnumMomentoImprimirAuto";
import {ConfigWhatsapp} from "./ConfigWhatsapp";
import {Endereco} from "./delivery/Endereco";
import {DTOObjetoComNome} from "./DTOObjetoComNome";
import {DiaDaSemanaEnumLabel} from "./DiaDaSemanaEnum";
import {Localizacao} from "../utils/Localizacao";
import {EnumTemas} from "./temas/EnumTemas";
import {Catalogo} from "./catalogo/Catalogo";
import {PedidoGenerico} from "./delivery/PedidoGenerico";
import * as moment from "moment";
import {EnumStatusPedido} from "../lib/emun/EnumStatusPedido";
import {Rede} from "./Rede";
import {IntegracaoOpendelivery} from "./integracoes/IntegracaoOpendelivery";
import {IntegracaoOpendeliveryLogistica} from "./integracoes/IntegracaoOpendeliveryLogistica";
import {IntegracaoUberdirect} from "./integracoes/IntegracaoUberdirect";
import {IntegracaoFoodyDelivery} from "./integracoes/IntegracaoFoodyDelivery";
import {IntegracaoIfood} from "./integracoes/IntegracaoIfood";
import {EnumServicoHorarioFuncionamento} from "../lib/emun/EnumServicoHorarioFuncionamento";
import {IntegracaoGatewayPagamento} from "./integracoes/IntegracaoGatewayPagamento";
import {IFoodUtils} from "../service/integracoes/IFoodUtils";
import {FormaDePagamentoPdv} from "./pdv/FormaDePagamentoPdv";
import {MapeadorDeFormaDePagamentoPdv} from "../mapeadores/MapeadorDeFormaDePagamentoPdv";
import {Bandeira} from "./pdv/Bandeira";
import {EnumTipoBandeira} from "../lib/integracao/pagamentos/EnumTipoBandeira";
import {FormaDePagamento} from "./delivery/FormaDePagamento";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import { TemaPersonalizado } from "./TemaPersonalizado";
import {MeucardapioPay} from "./integracoes/MeucardapioPay";

let path = require('path');

const diasDaSemana = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

export class Empresa {
  static SUFIXOFULL = '_full'

  constructor() {
    this.bloqueada = false;
    this.meioDeEnvio = EnumMeioDeEnvio.Mock;
    this.aceitarPedidoAutomatico = true;
    this.usarCartaoCliente = false;
    this.associarCartaoFechamentoPedido = false;
    this.permitirMultiplasComandasMesa = false;
  }

  // tslint:disable-next-line:member-ordering
  static DiasAntesBloqueio = 5 ;


  static urlsCardapio = ['meucardapio.ai', 'meucatalogo.ai', 'samsmashburger.com.br', 'burritosfantastic.com',
    'burritosfantasticos.com.br', 'delivery.winecloud.com.br', 'pizzagourmetalphaville.com.br',
    'regalliaoficial.com.br', 'familiadohamburguer.com',
    'quitandapomar.com.br', 'pizzapratiko.com.br', 'meudominio.com.br']
  id: number;
  nome: string;
  razaoSocial: string;
  cnpj: string;
  email: string;
  dominio: string;
  cep: string;
  endereco: string;
  enderecoCompleto: Endereco;
  whatsapp: string;
  linkMaps: string;
  descricao: string;
  tituloDestaques: string;
  tituloFotos: string;
  instagram: string;
  facebook: string;
  logo: string;
  capa: string;
  qtdeDeMensagens: number;
  latitudeLongitude: string;
  qtdPedidosMes: number;
  qtdPedidos24horas: number;
  qtdPedidos7dias: number;
  accessTokenAPIConversoes: string;
  //não é salvo
  codigoTesteAPIConversoes: string;
  pixelFacebook: string;
  analytics: string;

  urlDaEmpresa: DominioDaEmpresa

  destaques: Array<Produto>;
  ambiente: Array<Foto>;
  meioDeEnvio: EnumMeioDeEnvio;
  horariosFuncionamento: Array<any> = [];
  pausasProgramadas: Array<any> = [];
  camposExtras: Array<any> = [];
  formasDeEntrega: Array<FormaDeEntregaEmpresa> = [];
  modulos: Array<Modulo> = [];
  categorias: Array<Categoria> = [];
  formasDePagamento: Array<any> = [];
  contrato: any;
  segmento: any;
  estaAberta: boolean;
  estaRecebendoPedidos: boolean;
  fechadoTemporariamente: boolean;
  mensagemFechada: string;
  horarioAbertura: string;
  sempreReceberPedidos: boolean;
  //impressaoTXT: boolean;
  configImpressao: ConfigImpressao;
  horariosHoje: Array<any>;
  ativarIndicacoes: boolean;
  bloqueada: boolean;
  responsavel: Usuario;
  aceitarPedidoAutomatico: boolean;
  statusPedidoAoAceitar: EnumStatusPedido;
  qtdeVisitasRecorrente: number;
  qtdeDiasEmRisco: number;
  qtdeDiasPerdido: number;
  qtdeComprasVIP: number;
  ticketMedioVIP: number;
  qtdeDiasPeriodo: number;
  codigoCliente: string;
  cardapio: Cardapio;
  ultimaAcao: any;
  dataBloqueioAuto: Date;
  numeroWhatsapp: NumeroWhatsapp;
  numeroWhatsappCampanhas: NumeroWhatsapp;
  tokenApiSuperLink: any
  tipoDeLoja: EnumTipoDeLoja;
  permiteAgendamento: boolean;
  mensagemPausaProgramada: string;
  descricaoEndereco: string;
  pedidoMesaNaoIdentificado: boolean
  avisosDeMesa: boolean
  identificadorMesa = 'Mesa';
  integracaoPedidoFidelidade: IntegracaoPedidoFidelidade;
  integracaoFidelidade: any;
  integracaoOpendelivery: IntegracaoOpendelivery;
  integracaoOpendeliveryLogistica: IntegracaoOpendeliveryLogistica;
  integracaoUberdirect: IntegracaoUberdirect;
  integracaoDelivery: any;
  integracaoFoodyDelivery: IntegracaoFoodyDelivery;
  integracoesIfood: Array<IntegracaoIfood> = [];
  integracaoGatewayPagamento: IntegracaoGatewayPagamento;
  meucardapioPay: MeucardapioPay;
  nomeCategoriaDestaques: string;
  imagemCategoriaDestaque: string;
  rede = '';
  cobrarTaxaServico: boolean
  permitirCupomMesas: boolean
  valorTaxaServico = 10
  fusoHorario = DateUtils.utcServer();
  saldoMensagens: number;
  appIos: string;
  configWhatsapp: ConfigWhatsapp;
  dark = false;
  darkPrincipal = false;
  empresaPrincipal: DTOObjetoComNome;
  agruparAdicionais = false;
  camposAdicionais: any[];
  idRede: number;
  tema: EnumTemas;
  catalogo: Catalogo;
  gtm: string;
  gtag: string;
  promocoes: any;
  favicon: string;
  dadosRede: Rede;
  agruparCategoriasPizza: boolean;
  modeloCatalogoDaRede: any;
  exibirBandeiras = false;
  horariosDoServico: any = [];
  googleMapsKey: string;
  enviarLinksBotao = false;
  temaPersonalizado: TemaPersonalizado;
  estoqueVinculadoProduto = false;
  usarCartaoCliente = false;
  associarCartaoFechamentoPedido = false;
  permitirMultiplasComandasMesa = false;
  cadastroPagamentoOnline = false;
  garcomFecharComandas = true;
  ehMeuCardapio(host: any): boolean {

    if (host.indexOf('meucardapio.ai') >= 0)
      return true;

    if (host.indexOf('meucatalogo.ai') >= 0)
      return true;

    if (this.urlDaEmpresa && host.indexOf(this.urlDaEmpresa.hostname) >= 0)
      return true;

    let meuCardapio = false;


    for(let i = 0; i < Empresa.urlsCardapio.length; i++)
      if(host.indexOf(Empresa.urlsCardapio[i]) >= 0) {
        meuCardapio = true;
        break;
      }
    return meuCardapio;
  }
  obtenhaMensagemFechado(){
    let agora =  DateUtils.agoraNofuso(this.fusoHorario);
    let horarioAbertura = DateUtils.agoraNofuso(this.fusoHorario);
    let horarioFechamento = DateUtils.agoraNofuso(this.fusoHorario);

    let mensagem = 'Que pena! Não estamos abertos no momento.';

    if( this.horariosHoje &&  this.horariosHoje.length > 0) {
      horarioAbertura.setHours(parseInt(this.horariosHoje[0].horarioAbertura.substring(0, 2), 10))
      horarioFechamento.setHours(parseInt(this.horariosHoje[0].horarioFechamento.substring(0, 2), 10))

      if(agora < horarioAbertura)
        return String(`${mensagem} Abriremos às ${this.horariosHoje[0].horarioAbertura.substring(0, 5)}`)

      return String(`${mensagem} Só recebemos pedidos até ${this.horariosHoje[0].horarioFechamento.substring(0, 5)}`)
    }

    return mensagem;
  }

  setHorariosFuncionamento(servico: any = EnumServicoHorarioFuncionamento.Site){
    this.mensagemPausaProgramada = this.estaEmPausaProgramada();

    this.setHorariosPorServico();
    let empresa: any = this;

    if(  this.horariosDoServico.length){
      let horarioDoServico: any = this.horariosDoServico.find((horarioServico: any) => horarioServico.servico === servico)

      if(servico === EnumServicoHorarioFuncionamento.Presencial)
        if(!horarioDoServico || !horarioDoServico.horarios.length) //nao configurou nenhum, pegar site
          horarioDoServico =
            this.horariosDoServico.find((horarioServico: any) => horarioServico.servico === EnumServicoHorarioFuncionamento.Site)

      this.estaAberta = horarioDoServico.estaAberta;
      this.estaRecebendoPedidos = horarioDoServico.estaRecebendoPedidos;
      this.horariosHoje = horarioDoServico.horariosHoje;
      this.horarioAbertura = horarioDoServico.horarioAbertura;

      empresa.mensagemAbrirPedidos = horarioDoServico.mensagemAbrirPedidos;
      empresa.descricaoHorario = horarioDoServico.descricaoHorario;
    }


    empresa.permiteAgendamento = false;

    for(let formaDeEntrega of this.formasDeEntrega)
      if(formaDeEntrega.permiteAgendamento) this.permiteAgendamento = true;
  }

  obtenhaMensagemNaoRecebendoPedidos(horarioEntregaAgendada: any){
    if(this.estaRecebendoPedidos) return null;

    if(!this.permiteAgendamento || !horarioEntregaAgendada)
      return  this.obtenhaMensagemFechado();

    let pausaProgramada = this.obtenhaPausaProgramada(horarioEntregaAgendada);

    if(pausaProgramada)
      return  pausaProgramada.obtenhaMensagemFechado();


  }

  setFormasPagamentosDaLoja(){
    if(this.exibirBandeiras)
      this.formasDePagamento = this.formasDePagamento.filter((forma: any) => !forma.nome.startsWith('ifood'));
  }

  retireZonasDesativadas(){
    for(let formaDeEntrega of this.formasDeEntrega){
      if(formaDeEntrega.zonasDeEntrega)
        formaDeEntrega.zonasDeEntrega = formaDeEntrega.zonasDeEntrega.filter((item: any) => !item.desativada)
    }
  }

  fazDelivery(){
    let formaDelivery = this.obtenhaFormaReceberEmCasa();

    return formaDelivery && formaDelivery.ativa
  }

  setHorariosPorServico(){
    let horariosPresenciais = this.horariosFuncionamento.filter((item: any) =>
      item.funciona && item.servico === EnumServicoHorarioFuncionamento.Presencial);
    let horariosSite = this.horariosFuncionamento.filter((item: any) =>
      item.funciona &&  item.servico !== EnumServicoHorarioFuncionamento.Presencial);

    this.horariosDoServico  =  [];

    let agora = DateUtils.agoraNofuso(this.fusoHorario);

    let servico: any  = {
      servico:  EnumServicoHorarioFuncionamento.Site,
      label: 'Delivery',
      descricao: 'Delivery e retirada de pedidos',
      horarios: horariosSite,
      icone: 'fa fa-solid fa-motorcycle'
    }

    if(!this.fazDelivery()){
      servico.label = 'Retirada';
      servico.descricao = 'Retirada de pedidos';
      servico.icone = 'fas fa-shopping-bag';
    }

    this.horariosDoServico.push(servico )

    if(horariosPresenciais.length){
      this.horariosDoServico.push( {
        servico:  EnumServicoHorarioFuncionamento.Presencial,
        label: 'Loja',
        descricao: 'Atendimento presencial da loja',
        horarios: horariosPresenciais,
        icone: 'fa fa-solid fa-building'
      })
    }

    this.horariosDoServico.forEach((horarioDoServico: any) => {
      let servicoAberto = false;
      let horariosHoje = [];

      if(!this.estaBloqueada() && !this.mensagemPausaProgramada) {
        for(let i = 0; i  < horarioDoServico.horarios.length; i++) {
          let horarioAvaliado =  horarioDoServico.horarios[i];

          if(horarioAvaliado.estaDentroDoHorario(agora))  {
            servicoAberto = true;
            horariosHoje.push(horarioAvaliado);
            horarioAvaliado.hoje = true;
          } else {
            if(!horarioAvaliado.ehMadrugada(agora)) //se for madrugada, vai usar apenas o horário aberto da madrugada;
              if(horarioAvaliado.mesmoDiaDaSemana(agora)) {
                horariosHoje.push(horarioAvaliado);
                horarioAvaliado.hoje = true;
              }
          }
        }

        if(!servicoAberto)
          horarioDoServico.motivo = 'Loja fora do horário de funcionamento'
      } else {
        horarioDoServico.estaAberta = false;
        horarioDoServico.estaRecebendoPedidos = false;

        if(this.mensagemPausaProgramada)
          horarioDoServico.motivo = 'Loja está em pausa programada'

        if(this.estaBloqueada())
          horarioDoServico.motivo = 'Loja bloqueada por debitos'
      }

      horarioDoServico.horariosHoje = horariosHoje;
      horarioDoServico.estaAberta = servicoAberto;
      horarioDoServico.estaRecebendoPedidos = this.sempreReceberPedidos || servicoAberto;

      horariosHoje.forEach((horarioHoje: any) => {
        if (horarioHoje.funciona && !horarioHoje.jaPassouDoHorario(agora) && horarioHoje.ehAntesDo(horarioDoServico.horarioAbertura))
          horarioDoServico.horarioAbertura = horarioHoje.horarioAbertura.substr(0, 5);
      })

      if(horariosHoje.length){
        horarioDoServico.descricaoHorario = diasDaSemana[horariosHoje[0].diaDaSemana];
        horarioDoServico.turnos = horariosHoje.length > 1;

        let descricoesHorario = [];

        for(let i = 0; i < horarioDoServico.horariosHoje.length; i++) {
          let horarioHoje = horarioDoServico.horariosHoje[i];
          if(!horarioHoje.funciona) {
            descricoesHorario.push("Fechado");

            if(!horarioDoServico.estaRecebendoPedidos)
              horarioDoServico.mensagemAbrirPedidos = this.mensagemPausaProgramada || 'Não estamos recebendo pedidos hoje'

            break;
          }

          descricoesHorario.push(horarioHoje.horarioAbertura.substr(0, 5)  + " - " + horarioHoje.horarioFechamento.substr(0, 5) );

        }
        if(!horarioDoServico.mensagemAbrirPedidos && horarioDoServico.horarioAbertura)
          horarioDoServico.mensagemAbrirPedidos = 'Abre às ' + horarioDoServico.horarioAbertura;

        horarioDoServico.descricaoHorario += " " + descricoesHorario.join(" | ");
        horarioDoServico.descricaoHorarioSemDia = descricoesHorario.join(" | ");
      } else {
        horarioDoServico.descricaoHorario = "Fechado";
        if(!horarioDoServico.estaRecebendoPedidos)
          horarioDoServico.mensagemAbrirPedidos = this.mensagemPausaProgramada || 'Não estamos recebendo pedidos hoje'
      }

    })
  }

  setEmpresaFechadaPeloUsuario(mensagem: string){
    let empresa: any  = this;

    empresa.estaRecebendoPedidos = false;
    empresa.fechadoTemporariamente = true;
    empresa.mensagemFechada = mensagem  || 'Fechamos por alguns minutos';

    if(empresa.estaAberta)
        empresa.mensagemAbrirPedidos = empresa.mensagemFechada;

    this.horariosDoServico.forEach((horarioDoServico: any) => {
      horarioDoServico.motivo = 'Loja fechada temporariamente pelo usuário'
      horarioDoServico.estaRecebendoPedidos = false;
    });
  }

  temModulo(modulo: any) {
    return this.modulos.find((item: any) => item.id === modulo.id) != null
  }

  completouWizard(){
    if(!this.temPedidos() || this.id < 89)  return true;

    return this.capa && this.logo &&  this.formasDeEntrega.length > 0
  }

  obtenhaHostLoja() {
    return 'https://' + this.dominio + '.' +
      (this.tipoDeLoja === EnumTipoDeLoja.Catalogo ? 'meucatalogo' : 'meucardapio') + '.ai'
  }

  obtenhaEnderecoSite(producao: boolean) {
    if( producao ) {
      return 'https://' + this.dominio + '.' +
        (this.tipoDeLoja === EnumTipoDeLoja.Catalogo ? 'meucatalogo' : 'meucardapio') + '.ai'
    }

    return 'https://localhost:8443';
  }

  obtenhaLinkLoja(producao: boolean){
    if(this.urlDaEmpresa) return "https://" + this.urlDaEmpresa.hostname

    return producao ? this.obtenhaHostLoja()  : 'https://localhost:8443/loja';
  }

  obtenhaLinkLogo(producao: boolean){
    let host: string = producao ?  this.obtenhaLinkLoja(producao) : 'http://localhost:3100';

    return String(`${host}/images/empresa/${this.logo}`)
  }

  obtenhaLinkPagamentoPedidoNaLoja(pedido: any, pagamento: any, producao: any){
    return `${this.obtenhaLinkLoja(producao)}`;
  }

  temPedidos( ){
    return  this.temModulo(Modulo.Pedido) || this.temCardapio() // carpadios e pedido
  }

  temCardapio() {
    return this.temModulo(Modulo.Cardapio)
  }

  temModuloControleEstoque() {
    return this.temModulo(Modulo.ControleEstoque)
  }

  temFidelidade( ){
    return  this.temModulo(Modulo.Fidelidade) ;
  }

  estaBloqueada(){
    return this.bloqueada || this.venceuDataBloqueio();
  }


  estaEmPausaProgramada(){
    let agora = DateUtils.agoraNofuso(this.fusoHorario)

    let pausaProramada: any =  this.obtenhaPausaProgramada(agora)

    if(pausaProramada){
      pausaProramada.agora = true;
      return pausaProramada.obtenhaMensagemFechado();
    }


    return null;
  }

  obtenhaPausaProgramada(horario: any){
    if(!this.pausasProgramadas ||  !this.pausasProgramadas.length) return  null;

    return  this.pausasProgramadas.find( (pausaProgramada: any) => pausaProgramada.estaVigente(horario));

  }

  venceuDataBloqueio(){
    if(!this.dataBloqueioAuto) return false;

    //moment dentro da Empresa nao compila...
    return this.diasDoBloqueio() <= 0
  }

  diasDoBloqueio(){
    if(!this.dataBloqueioAuto) return null;

    return DateUtils.diasParaData(this.dataBloqueioAuto)
  }

  obtenhaFormaPagamentoMercadoPago(){
    return this.formasDePagamento.find(forma =>
              forma.configMeioDePagamento && forma.configMeioDePagamento.meioDePagamento === EnumMeioDePagamento.MercadoPago);

  }


  obtenhaFormaPagamentoOnline(meioDePagamento: string) {
    return this.formasDePagamento.find(formaDePagamento =>
      formaDePagamento.online && formaDePagamento.configMeioDePagamento &&
      formaDePagamento.configMeioDePagamento.meioDePagamento === meioDePagamento);
  }

  obtenhaFormaEntrega(formaDeEntregaString: string) {
    return this.formasDeEntrega.find( formaDeEntregaEmpresa => {
      return formaDeEntregaEmpresa.formaDeEntrega.nome === formaDeEntregaString
    });
  }

  obtenhaFormaEntregaRetirada() {
    return this.formasDeEntrega.find( formaDeEntregaEmpresa => {
      return formaDeEntregaEmpresa.formaDeEntrega.nome === 'Retirar'
    });
  }

  obtenhaFormaReceberEmCasa(): FormaDeEntregaEmpresa {
    return this.formasDeEntrega.find( formaDeEntregaEmpresa => {
      return formaDeEntregaEmpresa.formaDeEntrega.nome === 'Receber em casa'
    });
  }

  obtenhaFormaDeEntrega(pedido: PedidoGenerico): FormaDeEntregaEmpresa {
    if( !pedido.formaDeEntrega ) {
      return null;
    }

    return this.formasDeEntrega.find( formaDeEntregaEmpresa => {
      return formaDeEntregaEmpresa.formaDeEntrega.id === pedido.formaDeEntrega.id
    });
  }

  obtenhaFormaDePagamentoPagseguro() {
    return this.obtenhaFormaDePagamentoDoMeio(EnumMeioDePagamento.Pagseguro)
  }

  obtenhaFormaDePagamentoPorNome(nomeFormaDePagamento: string) {
    for( let i = 0; i < this.formasDePagamento.length; i ++ ) {
      const forma: any = this.formasDePagamento[i];

      if( forma.descricao === nomeFormaDePagamento ) {
        return forma;
      }
    }

    return null;
  }

  obtenhaFormaDePagamentoDoMeio(meio: EnumMeioDePagamento) {
    for( let i = 0; i < this.formasDePagamento.length; i ++ ) {
      const forma: any = this.formasDePagamento[i];

      if( forma.online && forma.configMeioDePagamento.meioDePagamento === meio ) {
        return forma;
      }
    }

    return null;
  }


  cobraPorApi(tipoCobranca: any){
    let formaEntregaCasa = this.obtenhaFormaReceberEmCasa();

    return formaEntregaCasa && formaEntregaCasa.tipoDeCobranca === tipoCobranca
  }

  obtenhaCaminhoCardapioPDF(caminhoImagens: string) {
    return path.join(caminhoImagens, `empresa/pdf/cardapio${this.catalogo.id}.pdf`);
  }

  obtenhaUrlCardapioPDF(producao: boolean) {
    return `/images/empresa/pdf/cardapio${this.catalogo.id}.pdf?v=` + new Date().getTime();
  }

  notificarNovoPedidoAoSistemaIntegrado(pedido: any){
    if(!this.integracaoPDVParceiroAtiva() || pedido.foiCanceladoOuDevolvido() || pedido.pagarOnline())
      return false;

    if( !pedido.aceito){
      console.log('Pedido ainda não foi aceito, nao notificar.')
      return false;
    }

    if(pedido.referenciaExterna) // Ja notificou
      return false; //

    return this.integracaoDelivery.notificarNovo(pedido);
  }


  integracaoPDVParceiroAtiva(){
    return this.integracaoDelivery && this.integracaoDelivery.ativa
  }

  integracaoOpenDeliveryComercianteAtiva(){
    return this.integracaoOpendelivery && this.integracaoOpendelivery.ativa
  }

  integracaoDeliveryOpendeliveryAtiva(){
    return this.integracaoOpendeliveryLogistica && this.integracaoOpendeliveryLogistica.ativa
  }

  integracaoOpenDeliveryAtiva(){
    return this.integracaoOpenDeliveryComercianteAtiva ||  this.integracaoDeliveryOpendeliveryAtiva();
  }

  integarcaoComFoodyDeliveryAtiva(){
    return this.integracaoFoodyDelivery && this.integracaoFoodyDelivery.ativa
  }

  integracaoUberAtiva(){
    return this.integracaoUberdirect && this.integracaoUberdirect.ativa;
  }

  integracaoIfoodDeliveryAtiva(){
    return this.integracoesIfood.find((item: any) => item.shippingApi && !item.desativado)
  }

  fechaComandaNoParceiro(){
    if(!this.integracaoPDVParceiroAtiva()) return false;

    const sistema = this.integracaoDelivery.sistema;

    return sistema === 'saipos' ||  sistema === 'ecletica'
  }

  integracaoPdvTemPrechamento(){
     return this.integradoComEcleticaDWMS();
  }

  integradoComEcleticaDWMS(){
    if(!this.integracaoPDVParceiroAtiva()) return false;

    return  this.integracaoDelivery.sistema === 'ecletica' && this.integracaoDelivery.integradoComServicoDMWS() ;
  }


  integracaoPdvTemPrechamentoObrigatorio(){
    return this.integracaoPdvTemPrechamento() && !this.integracaoDelivery.integrarComComandas()
  }

  integradoComComandas(){
    return this.integracaoDelivery && this.integracaoDelivery.integrarComComandas();
  }

  ehDaRedeEcletica(rede: string, loja: string){
     if(!this.integracaoDelivery) return  false;

     return this.integracaoDelivery.rede === Number(rede) && this.integracaoDelivery.loja === Number(loja)
  }

  obtenhaSaldoMensagens() {
    return this.saldoMensagens ? this.saldoMensagens : 0;
  }

  fazParteDaRedeChinaInBox(): boolean {
     return this.idRede === 1
  }

  fazParteDaRedeTrendsFoood(): boolean {
    return this.rede === 'chinainbox'
  }

  imprimeNovosPedidos(){
    return this.configImpressao && this.configImpressao.momentoImprimirAuto === EnumMomentoImprimirAuto.NovoPedido
  }

  obtenhaDescricaoHorarioAtendimento() {
    let horariosFuncionamento: any[] = this.horariosFuncionamento

    // let frase = '_Horário de funcionamento_:\n xx ás xxx / xx ás xxx ( Segunda á domingo )'

    horariosFuncionamento.sort((horario1: any, horario2: any) => {
      let diaDaSemana1 = horario1.diaDaSemana
      let diaDaSemana2 = horario2.diaDaSemana

      if(diaDaSemana1 === 0) diaDaSemana1 = 7
      if(diaDaSemana2 === 0) diaDaSemana2 = 7

      if(diaDaSemana1 !== diaDaSemana2)
        return diaDaSemana1 - diaDaSemana2

      let horaAbertura1 = Number.parseInt(horario1.horarioAbertura.substring(0, 2), 10)
      let horaAbertura2 = Number.parseInt(horario2.horarioAbertura.substring(0, 2), 10)

      return horaAbertura1 - horaAbertura2
    })

    let horariosDoDia: any = {}
    let diaAtual = 0
    let textoAtual = []
    let menorDia = 0
    for(let horarioFuncionamento of horariosFuncionamento) {

      if(horarioFuncionamento.diaDaSemana !== diaAtual) {
        if(textoAtual.length > 0) horariosDoDia[diaAtual] = textoAtual.join(" / ")

        textoAtual = []


        diaAtual = horarioFuncionamento.diaDaSemana

        if(horarioFuncionamento.funciona && menorDia === 0)
          menorDia = diaAtual

      }

      if(!horarioFuncionamento.funciona)
        continue;

      let horarioAbertura = horarioFuncionamento.horarioAbertura.substring(0, 5)
      let horarioFechamento = horarioFuncionamento.horarioFechamento.substring(0, 5)



      let horarioDoTurno = horarioAbertura + " às " + horarioFechamento

      textoAtual.push(horarioDoTurno)
    }

    if(diaAtual === 0) diaAtual = 7
    if(menorDia === 0) menorDia = 7

    if(textoAtual.length > 0) horariosDoDia[diaAtual] = textoAtual.join(" / ")

    let frase: string = horariosDoDia[menorDia]
    let rangeAtual: string = frase
    let menorDiaRange: any = menorDia
    let maiorDiaRange: any = menorDia

    for(let i = menorDia + 1; i <= 7; i++) {
      if(!horariosDoDia[i] || horariosDoDia[i] !== rangeAtual) {
        if(rangeAtual) {
          frase += " (" + DiaDaSemanaEnumLabel.get(menorDiaRange)

          if(maiorDiaRange > menorDiaRange)
            frase += " a " + DiaDaSemanaEnumLabel.get(maiorDiaRange)

          frase += ")" + "\n"
        }


        menorDiaRange = i
        rangeAtual = horariosDoDia[i]
        if(rangeAtual)
          frase += rangeAtual
      }
      if(horariosDoDia[i])
        maiorDiaRange = i
    }

    if(horariosDoDia[menorDiaRange]) {
      frase +=  " (" + DiaDaSemanaEnumLabel.get(menorDiaRange)

      if(maiorDiaRange > menorDiaRange)
        frase += " a " + DiaDaSemanaEnumLabel.get(maiorDiaRange)

      frase += ")" + "\n"
    }

    return frase;
  }

  obtenhaLocalizacaoEmpresa() {
    const pedacos = this.latitudeLongitude.split(',');

    if( pedacos.length < 2 ) {
      return null;
    }

    const lat = pedacos[0].trim();
    const lng = pedacos[1].trim();

    return new Localizacao(lat, lng);
  }

  lojaEcommerce(): boolean{
    return this.tipoDeLoja  === EnumTipoDeLoja.Ecommerce
  }

  lojaMercado(): boolean{
    return this.tipoDeLoja  === EnumTipoDeLoja.Mercado
  }

  temCategoriasVariosNiveis(){
   return  this.catalogo.categorias.find((cat: any) => cat.categoriaPai != null) != null
  }

  possuiModulo(nomeModulo: string) {
    if(!this.modulos) return false;

    return this.modulos.some(modulo =>   modulo.nome === nomeModulo);
  }

  possuiObjetoModulo(objetoModulo: Modulo) {
    if(!this.modulos) return false;

    return this.modulos.some(modulo =>   modulo.id === objetoModulo.id);
  }

  ehUmaCib(){
    return this.idRede === 1 && this.rede === 'chinainbox'
  }

  ehUmaGedai(){
    return this.idRede === 2 && this.rede === 'chinainbox'
  }

  ehDarkPrincipal(){
    return this.dark && !this.empresaPrincipal;
  }

  cpfObrigatorio(){
    return this.camposExtras.find((item: any) => item.nome === 'cpf') != null;
  }

  dataNascimentoObrigatoria(){
    return this.camposExtras.find((item: any) => item.nome === 'datanascimento') != null;
  }


  vendeOnline(){
    let formaDePagamentoOnline = this.formasDePagamento.find((formaDePagamento: any) =>  formaDePagamento.online )


    return formaDePagamentoOnline != null;
  }

  removaFormasNaoMapeadas(grupoDeLojas: any){
    if(!grupoDeLojas || !grupoDeLojas.multipedido) return;

    let deParas: Array<any> = grupoDeLojas.deParas,
         empresasGrupos: Array<any> = grupoDeLojas.empresas;

    this.formasDePagamento = this.formasDePagamento.filter((formaDePagamento: any) => {
      let existe = true;
       empresasGrupos.forEach((empresaDoGrupo: any) => {
           if(empresaDoGrupo.empresa.id !== this.id){
             let deParaForma =
               deParas.find((dePara: any) =>  dePara.de === formaDePagamento.id &&  dePara.paraEmpresa === empresaDoGrupo.empresa.id)

             if(!deParaForma) existe = false;
           }

       })

       return existe;
    })
  }

  ehHops(){
    return this.id === 14;
  }

  calculeProximoBloqueio(dataVencimento: Date) {
    let dataBloqueio: any  = moment(dataVencimento).add(Empresa.DiasAntesBloqueio, 'days')

    DateUtils.garantaVencimentoSexaASabado(dataBloqueio)

    this.dataBloqueioAuto = dataBloqueio.toDate();
    console.log('Nova data bloqueio: ' + dataBloqueio.format('DD/MM/YYYY'))
  }

  temMaisDeUmaIntegracaoDeliveryAtiva(){
     let integracoes = [];

     if(this.integracaoDeliveryOpendeliveryAtiva())
       integracoes.push(this.integracaoOpendeliveryLogistica)

     if(this.integarcaoComFoodyDeliveryAtiva())
       integracoes.push(this.integracaoFoodyDelivery)

     if(this.integracaoUberAtiva())
       integracoes.push(this.integracaoUberdirect)

    if(this.integracaoIfoodDeliveryAtiva())
      integracoes.push(...this.integracoesIfood)


     return  integracoes.length > 1
  }

  obtenhaIntegracaoIfood(idLoja: string){
    if(this.integracoesIfood.length === 1) return this.integracoesIfood[0];

    return this.integracoesIfood.find((item: any) => item.idLoja === idLoja)
  }


  async obtenhaOuCrieFormaDePagamentoIfood(metodoPagamentoIfood: any){
    return  new Promise( async (resolve: any) => {
      let fomarDePagamento: any =  this.formasDePagamento.find((item: any) => item.doIfoodLoja(metodoPagamentoIfood));

      if(!fomarDePagamento){
        new MapeadorDeFormaDePagamentoPdv().transacao(async (conexao: any, commit: any) => {
          let brandeiraIfood: string = metodoPagamentoIfood.card ? metodoPagamentoIfood.card.brand : '';
          //: criar pagamento do ifood novo...
          let ifoodLoja: FormaDePagamentoPdv =  await IFoodUtils.obtenhaFormaPagamentoLoja();
          let bandeiraPdv: any = ifoodLoja.bandeiras.find((item: any) => item.bandeira.nome.toUpperCase() === brandeiraIfood.toUpperCase())
          let bandeira: Bandeira;
          if(bandeiraPdv){
            bandeira = bandeiraPdv.bandeira;
          } else {
            bandeira  = new Bandeira(brandeiraIfood, ' ', EnumTipoBandeira.Cartao);
            await bandeira.salve(true)
          }

          await ifoodLoja.ativeBandeira(bandeira);

          fomarDePagamento = FormaDePagamento.novaPdv(ifoodLoja,  bandeira, this)
          await FormaDePagamento.ativeNovaForma(fomarDePagamento);
          commit( async () => {
            await new MapeadorDeEmpresa().removaDasCaches(this)
            resolve(fomarDePagamento);
          })
        })
      } else {
        resolve(fomarDePagamento);
      }
    })
  }

  resgatarBrindes(): any {
    return this.integracaoPedidoFidelidade && this.integracaoPedidoFidelidade.resgatarBrinde;
  }

  acumulaCashback(){
    return this.integracaoPedidoFidelidade && this.integracaoPedidoFidelidade
  }

  ocultarPontosFidelidade(){
    return this.integracaoPedidoFidelidade && this.integracaoPedidoFidelidade.ocultarPontos;
  }

  getCashback(){
    return this.integracaoPedidoFidelidade ? this.integracaoPedidoFidelidade.atividade.cashback : null;
  }

  integradoComPdvPorComandas(){
    let integracaoPdv: any  = this.integracaoDelivery;


    return integracaoPdv && integracaoPdv.configuracoesEspecificas && integracaoPdv.configuracoesEspecificas.integrarComComanda
  }

  getLogoFull(){
    if(!this.logo) return ''

    const partes = this.logo.split('.');

    const logoFull = `${partes[0]}${Empresa.SUFIXOFULL}.${partes[1]}`;

    return logoFull;
  }

}

