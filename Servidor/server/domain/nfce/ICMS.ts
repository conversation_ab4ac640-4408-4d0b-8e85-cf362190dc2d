import {ICMSXml} from "../../utils/nfce/xml/ICMSXml";
import {XmlUtils} from "../../utils/nfce/comunicacao/XmlUtils";
import {FormatadorDeNumeros} from "../../utils/nfce/utils/FormatadorDeNumeros";
import {TributacaoNaturezaOperacao} from "./TributacaoNaturezaOperacao";
import {Ipi} from "./Ipi";
import {ItemNFe} from "./ItemNFe";
import {ProdutoConfigFiscal} from "../ProdutoConfigFiscal";

export class ICMS {
  id: number
  origem: number;
  cst: string;
  modbasecalcIcms: number;
  aliquotaICMS: number;
  valorOperacao: number;
  percentualDiferimento: number;
  valorDiferido: number;
  valorICMS: number;
  valorDesonerado: number;
  motivoDesoneracao: string;
  modBaseDeCalculoICMSSt: number;
  percentualMargemValorICMSSt: number;
  percentualReducaoBaseDeCalculoICMSSt: number;
  valorBaseDeCalculoICMSSt: number;
  aliquotaICMSSt: number;
  valorICMSSt: number;
  percentualBCOperacaoPropria: number;
  ufPartilha: string;
  valorBCICMSSTRet: number;
  valorICMSSTRet: number;
  valorBCICMSSTDest: number;
  valorICMSSTDest: number;
  percentualReducaoBaseDeCalculoICMS: number;
  valorBaseDeCalculoICMS: number;
  percentualFCP: number;
  valorFCP: number;
  valorBaseDeCalculoFCP: number;
  valorBaseDeCalculoFCPST: number;
  percentualFCPST: number;
  valorFCPST: number;
  valorBCFCPSTRet: number;
  percentualFCPSTRet: number;
  valorFCPSTRet: number;
  percentualConsumidorST: number;
  percentualReducaoBCEfetiva: number;
  valorBCEfetiva: number;
  percentualICMSEfetivo: number;
  valorICMSEfetivo: number;
  valorICMSSubstituto: number;
  partilhaICMS: boolean;
  icmsSTRet: boolean;
  quantidadeTributadaMono: number;
  aliquotaAdRem: number;
  valorICMSMonoOperacao: number;
  valorICMSMonoDiferido: number;
  valorICMSMono: number;
  quantidadeTributadaMonoReten: number;
  aliquotaAdRemReten: number;
  valorICMSMonoReten: number;
  percentualReducaoAdRem: number;
  motivoReducaoAdRem: number;
  quantidadeTributadaMonoRetida: number;
  aliquotaAdRemRetido: number;
  valorICMSMonoRetido: number;

  static criePeloItem(itemNFe: ItemNFe, configuracaoFiscal: ProdutoConfigFiscal,  tributacaoNaturezaOperacao: TributacaoNaturezaOperacao, ipi: Ipi): ICMS {
    if(tributacaoNaturezaOperacao.tipoDeTributacaoICMS.simplesNacional) return undefined;

    const icms = new ICMS();

    let valorICMS = 0
    let valorICMSSemReducao = 0

    icms.cst = tributacaoNaturezaOperacao.tipoDeTributacaoICMS.codigo;

    icms.origem = configuracaoFiscal.origem.codigo; //00, 20, 40, 41, 50, 60

    if(["00"].includes(icms.cst)) {
      icms.modbasecalcIcms = parseInt(tributacaoNaturezaOperacao.modalidadeBaseDeCalculoICMS.codigo, 10);
      icms.valorBaseDeCalculoICMS = ICMS.calculeBaseDeCalculoICMS(itemNFe,
      tributacaoNaturezaOperacao.ipiCompoeBaseCalculoICMS, ipi);

      if(tributacaoNaturezaOperacao.percentualFundoCombatePobreza) {
        icms.percentualFCP = tributacaoNaturezaOperacao.percentualFundoCombatePobreza;
        icms.valorFCP = icms.valorBaseDeCalculoICMS * icms.percentualFCP / 100;
      }

      valorICMS = this.calculeICMSNormal(icms.valorBaseDeCalculoICMS, tributacaoNaturezaOperacao.aliquotaICMS);
    }

    if(["20", "40", "41", "50"].includes(icms.cst)) {
      valorICMSSemReducao = this.calculeICMSNormal(itemNFe.valorTotalBruto - itemNFe.valorDesconto,
        tributacaoNaturezaOperacao.aliquotaICMS);

      if(tributacaoNaturezaOperacao.percentualReducaoBaseCalculoICMS) {
        icms.valorBaseDeCalculoICMS =  ICMS.calculeBaseDeCalculoICMS(itemNFe, tributacaoNaturezaOperacao.ipiCompoeBaseCalculoICMS,
            ipi) *
          (1 - icms.percentualReducaoBaseDeCalculoICMS / 100);
        icms.percentualReducaoBaseDeCalculoICMS = tributacaoNaturezaOperacao.percentualReducaoBaseCalculoICMS;
        valorICMS = this.calculeICMSNormal(icms.valorBaseDeCalculoICMS, tributacaoNaturezaOperacao.aliquotaICMS);

      } else
        valorICMS = 0

      icms.valorDesonerado = valorICMSSemReducao - valorICMS;
    }

    if(["00", "20"].includes(icms.cst)) {
      icms.aliquotaICMS = tributacaoNaturezaOperacao.aliquotaICMS
      icms.valorICMS = valorICMS;
    }

    if(tributacaoNaturezaOperacao.tipoDeTributacaoICMS.codigo === '60') {
      //incluir campos da aliquota efetiva
      icms.percentualICMSEfetivo = tributacaoNaturezaOperacao.aliquotaICMSEfetivo;
      icms.percentualReducaoBCEfetiva = tributacaoNaturezaOperacao.percentualReducaoBCEfetiva;
      icms.valorBCEfetiva = itemNFe.valorTotalBruto - itemNFe.valorDesconto

      if(icms.percentualReducaoBCEfetiva)
        icms.valorBCEfetiva = icms.valorBCEfetiva * (1 - icms.percentualReducaoBCEfetiva / 100);

      icms.valorICMSEfetivo = icms.valorBCEfetiva * icms.percentualICMSEfetivo / 100;
    }

    return icms
  }

  static calculeBaseDeCalculoICMS(itemNFe: ItemNFe, ipiCompoeBaseCalculoICMS: boolean, ipi: Ipi) {
    let baseCalculo = itemNFe.valorTotalBruto - itemNFe.valorDesconto

    if(ipiCompoeBaseCalculoICMS) {
      baseCalculo = baseCalculo + ipi.valor;
    }

    return baseCalculo;
  }


  static obtenhaICMS(tagICMS: any): ICMS {
    if (!this.deveSerTratado(tagICMS)) return null;

    let icms = new ICMS();
    icms.origem = tagICMS.orig;
    icms.cst = tagICMS.CST;
    icms.modbasecalcIcms = tagICMS.modBC;
    icms.aliquotaICMS = tagICMS.pICMS;
    icms.valorOperacao = tagICMS.vICMSOp;
    icms.percentualDiferimento = tagICMS.pDif;
    icms.valorDiferido = tagICMS.vICMSDif;
    icms.valorICMS = tagICMS.vICMS;
    icms.valorDesonerado = tagICMS.vICMSDeson;
    icms.motivoDesoneracao = tagICMS.motDesICMS;
    icms.modBaseDeCalculoICMSSt = tagICMS.modBCST;
    icms.percentualMargemValorICMSSt = tagICMS.pMVAST;
    icms.percentualReducaoBaseDeCalculoICMSSt = tagICMS.pRedBCST;
    icms.valorBaseDeCalculoICMSSt = tagICMS.vBCST;
    icms.aliquotaICMSSt = tagICMS.pICMSST;
    icms.valorICMSSt = tagICMS.vICMSST;
    icms.percentualBCOperacaoPropria = tagICMS.pBCOp;
    icms.ufPartilha = tagICMS.UFST;
    icms.valorBCICMSSTRet = tagICMS.vBCSTRet;
    icms.valorICMSSTRet = tagICMS.vICMSSTRet;
    icms.valorBCICMSSTDest = tagICMS.vBCSTDest;
    icms.valorICMSSTDest = tagICMS.vICMSSTDest;
    icms.percentualReducaoBaseDeCalculoICMS = tagICMS.pRedBC;
    icms.valorBaseDeCalculoICMS = tagICMS.vBC;
    icms.percentualFCP = tagICMS.pFCP;
    icms.valorFCP = tagICMS.vFCP;
    icms.valorBaseDeCalculoFCP = tagICMS.vBCFCP;
    icms.valorBaseDeCalculoFCPST = tagICMS.vBCFCPST;
    icms.percentualFCPST = tagICMS.pFCPST;
    icms.valorFCPST = tagICMS.vFCPST;
    icms.valorBCFCPSTRet = tagICMS.vBCFCPSTRet;
    icms.percentualFCPSTRet = tagICMS.pFCPSTRet;
    icms.valorFCPSTRet = tagICMS.vFCPSTRet;
    icms.percentualConsumidorST = tagICMS.pST;
    icms.percentualReducaoBCEfetiva = tagICMS.pRedBCEfet;
    icms.valorBCEfetiva = tagICMS.vBCEfet;
    icms.percentualICMSEfetivo = tagICMS.pICMSEfet;
    icms.valorICMSEfetivo = tagICMS.vICMSEfet;
    icms.valorICMSSubstituto = tagICMS.vICMSSubstituto;
    icms.partilhaICMS = this.ehICMSPart(tagICMS);
    icms.icmsSTRet = this.ehICMSSTRet(tagICMS);

    return icms;
  }

  static ehICMSPart(tagICMS: any): boolean {
    const tipo: string = this.obtenhaTipo(tagICMS);
    return tipo === "Part";
  }

  static ehICMSSTRet(tagICMS: any): boolean {
    const tipo: string = this.obtenhaTipo(tagICMS);
    return tipo === "ST";
  }

  static calculeICMSNormal(baseDeCalculo: number, aliquotaICMS: number) {
    return baseDeCalculo * aliquotaICMS / 100;
  }


  static deveSerTratado(tagICMS: any): boolean {
    const tipo: string = ICMS.obtenhaTipo(tagICMS); // Assuming you're calling from the same class
    const icmsMapping: { [key: string]: string } = {
      '00': '00',
      '10': '10',
      '20': '20',
      '30': '30',
      '40': '40',
      '41': '40',
      '50': '40',
      '51': '51',
      '60': '60',
      '70': '70',
      '90': '90',
      'Part': 'Part',
      'ST': 'ST'
    };

    const icms = icmsMapping[tipo];

    console.log(`ICMS normal: ${icms}`);
    console.log(`ICMS normal: ${icms != null}`);

    return icms != null;
  }

  private static obtenhaTipo(tagICMS: any) {
    return  Object.keys(tagICMS)[0].replace("ICMS", "");
  }

  gereXml() {
    return new ICMSXml(this).obtenhaXml();
  }

  gereBCicms90(): string {
    return `
            ${XmlUtils.gereTag('vBC', FormatadorDeNumeros.formateDecimalNulo(this.valorBaseDeCalculoICMS, 2))}
            ${XmlUtils.gereTag('pRedBC', FormatadorDeNumeros.formateDecimalNulo(this.percentualReducaoBaseDeCalculoICMS, 4))}
        `;
  }

  gereBCicmsNormal(): string {
    return `
            ${XmlUtils.gereTag('pRedBC', FormatadorDeNumeros.formateDecimalNulo(this.percentualReducaoBaseDeCalculoICMS, 4))}
            ${XmlUtils.gereTag('vBC', FormatadorDeNumeros.formateDecimalNulo(this.valorBaseDeCalculoICMS, 2))}
        `;
  }

}
