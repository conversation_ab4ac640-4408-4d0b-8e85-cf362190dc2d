import {ImpostoSobreImportacaoXml} from "../../utils/nfce/xml/ImpostoSobreImportacaoXml";

export class ImpostoSobreImportacao {
  iiValbasecalc: number;
  iiValdespaduane: number;
  iiValor: number;
  iiValoriof: number;

  static obtenhaImpostoSobreImportacao(tagII: any): ImpostoSobreImportacao | null {
    if(!tagII) return null;

    if (Object.keys(tagII).length === 0) return null;

    const impostoSobreImportacao = new ImpostoSobreImportacao();

    impostoSobreImportacao.iiValbasecalc = tagII.vBC;
    impostoSobreImportacao.iiValdespaduane = tagII.vDespAdu;
    impostoSobreImportacao.iiValor = tagII.vII;
    impostoSobreImportacao.iiValoriof = tagII.vIOF;

    return impostoSobreImportacao;
  }

  public gereXml(): string {
    return new ImpostoSobreImportacaoXml(this).obtenhaXml();
  }

}
