import {Mesa} from "./Mesa";
import crypto = require('crypto');

export class SessaoMesa {
  id: number;
  mesa: Mesa;
  hash: string;
  horario: Date;
  expirada: boolean;

  static CrieSessao(mesa: Mesa) {
    let sessao = new SessaoMesa()
    sessao.mesa  = mesa;
    sessao.hash =  crypto.randomBytes(32).toString('hex');
    sessao.horario = new Date()
    sessao.expirada = false;

    return sessao;
  }
}
