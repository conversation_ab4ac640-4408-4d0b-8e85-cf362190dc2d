import {Usuario} from "./Usuario";

export class RegistroDeLogin {
    id: number;
    ip: string;
    idSessao: string;
    diaAcesso: Date;
    horarioAcesso: Date;
    tipoDispositivo: string;
    detalhes_dispositivo: string;
    nome_navegador: string;
    versao_navegador: string;
    url: string;
    origem: string;
    diaLogout: Date;
    horarioLogout: Date;
    sessaoAtiva: boolean;
    empresaId: number;
    usuario: any;

    hostname: string;
    city: string;
    region: string;
    country: string;
    loc: string;
    org: string;
    postal: string;
    timezone: string;

    constructor(usuario: Usuario, ip: string, idSessao: string, diaAcesso: Date, horarioAcesso: Date, tipoDispositivo: string, detalhes_dispositivo: string, nome_navegador: string, versao_navegador: string,
                url: string, origem: string) {
        this.ip = ip;
        this.usuario = usuario;
        this.idSessao = idSessao;
        this.diaAcesso = diaAcesso;
        this.horarioAcesso = horarioAcesso;
        this.tipoDispositivo = tipoDispositivo;
        this.detalhes_dispositivo = detalhes_dispositivo;
        this.nome_navegador = nome_navegador;
        this.versao_navegador = versao_navegador;
        this.url = url;
        this.origem = origem;
        this.sessaoAtiva = true;
    }

  deslogue() {
    this.horarioLogout = new Date();
    this.diaLogout = new Date();
    this.sessaoAtiva = false;
  }
}
