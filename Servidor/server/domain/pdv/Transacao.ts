import {FormaDePagamento} from "../delivery/FormaDePagamento";
import {EnumTipoTransacao} from "./EnumTipoTransacao";
import {MoneyUtils} from "./MoneyUtils";
import { PedidoGenerico } from "../delivery/PedidoGenerico";
import { Caixa } from "./Caixa";
import { Usuario } from "../Usuario";
import {Comanda} from "../comandas/Comanda";

export class Transacao{
  public id: number;
  horario: Date;
  pedido: PedidoGenerico;
  comanda: Comanda;
  estornavel: boolean;

  get valor(): number{
    return MoneyUtils.deCentavosParaNumber(this.valorEmCentavos)
  }

  set valor(valor: number) {
    this.valorEmCentavos = MoneyUtils.deNumberParaCentavos(valor)
  }

  constructor(public caixa: Caixa, public operador: Usuario, public descricao: string, public tipo: EnumTipoTransacao,
              public formaDePagamento: FormaDePagamento,
              public valorEmCentavos: number, pedido: any = null, estornavel: boolean = false, comanda: any = null) {
    this.horario = new Date();
    this.pedido = pedido;
    this.estornavel = estornavel;
    this.comanda = comanda;
  }

  obtenhaDTO(): any {
    let dtoTransacao = {
      id: this.id,
      data: this.horario,
      pedido: this.pedido,
      comanda: this.comanda && this.comanda.id ? {
        id: this.comanda.id, mesa: this.comanda.mesa
      } : null,
      descricao: this.descricao,
      tipo: this.tipo,
      formaDePagamento: this.formaDePagamento,
      valor: this.valor
    }

    return dtoTransacao
  }
}
