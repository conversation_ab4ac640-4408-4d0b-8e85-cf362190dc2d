import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {FormaDePagamento} from "../delivery/FormaDePagamento";
import {Transacao} from "./Transacao";
import {EnumStatusCaixa} from "./EnumStatusCaixa";
import {MoneyUtils} from "./MoneyUtils";
import {Usuario} from "../Usuario";
import {EnumTipoTransacao} from "./EnumTipoTransacao";
import {EnumTipoNumero} from "./EnumTipoNumero";
import {Empresa} from "../Empresa";
import {PedidoGenerico} from "../delivery/PedidoGenerico";
import {MapeadorDeCaixa} from "../../mapeadores/MapeadorDeCaixa";
import {SaldoDaFormaDePagamento} from "./SaldoDaFormaDePagamento";
import {ResumoCaixa} from "./ResumoCaixa";
import {CaixaDTO} from "./CaixaDTO";
import {Sangria} from "./Sangria";
import {PagamentoPedido} from "../delivery/PagamentoPedido";
import {bool} from "aws-sdk/clients/signer";
import {Comanda} from "../comandas/Comanda";
import {EnumStatusComanda} from "../comandas/EnumStatusComanda";
import {PagamentoComanda} from "../delivery/PagamentoComanda";


export class Caixa implements ObjetoPersistente {
  id: any;
  saldoInicialDinheiroEmCentavos: number;
  saldoFinalDinheiroEmCentavos: number;
  saldoApuradoDinheiroEmCentavos: number;
  saldoPorFormaDePagamento: SaldoDaFormaDePagamento[] ;

  horarioAbertura: Date ;
  horarioFechamento: Date;
  transacoes: Transacao[];
  sangrias: Sangria[];
  observacao: string;
  operador: Usuario;
  status: EnumStatusCaixa;
  empresa: Empresa
  formaDinheiro: FormaDePagamento;
  saldoFinalTotal: number;
  online: boolean;

  //essa propriedade é apenas para facilitar a busca, não é persistente
  private mapaSaldos: Map<number, SaldoDaFormaDePagamento> = new Map<number, SaldoDaFormaDePagamento>();

  static abraNovoCaixa(saldoInicial: number, operador: Usuario, empresa: Empresa): Caixa {
    let caixa = new Caixa(empresa);

    caixa.abrirCaixa(saldoInicial, operador)

    return caixa;
  }


  constructor(empresa: Empresa) {
    this.empresa = empresa

    //encontra na empresa a forma de pagamento com nome 'Dinheiro' e coloca na propriedade formaDinheiro
    if(empresa)
        this.formaDinheiro = empresa.formasDePagamento.find((formaDePagamento: FormaDePagamento) => {
          return formaDePagamento.nome === 'dinheiro';
        if(!this.formaDinheiro)
          throw new Error('É preciso definir na empresa uma forma de pagamento Dinheiro')

      })

    this.saldoPorFormaDePagamento = [];
    this.transacoes = [];
    this.sangrias = [];

  }



   get saldoInicialEmDinheiro(): number {
     return MoneyUtils.deCentavosParaNumber(this.saldoInicialDinheiroEmCentavos)
   }

   set saldoInicialEmDinheiro(saldo: number) {
     this.saldoInicialDinheiroEmCentavos = MoneyUtils.deNumberParaCentavos(saldo);
   }

   get saldoFinalEmDinheiro(): number {
    return MoneyUtils.deCentavosParaNumber(this.saldoFinalDinheiroEmCentavos)

   }

   set saldoFinalEmDinheiro(saldo: number) {
    this.saldoFinalDinheiroEmCentavos = MoneyUtils.deNumberParaCentavos(saldo)
   }

  abrirCaixa(saldoInicial: number, operador: Usuario): void {
    if (this.status === 'ABERTO') {
      throw new Error('O caixa já está aberto');
    }
    this.saldoInicialEmDinheiro = saldoInicial;

    this.adicioneSaldo(this.formaDinheiro, this.saldoInicialDinheiroEmCentavos)


    this.operador = operador;
    this.horarioAbertura = new Date();
    this.status = EnumStatusCaixa.Aberto;
    this.adicioneTransacao("Saldo Inicial", EnumTipoTransacao.ENTRADA,
                           this.formaDinheiro,
                           this.saldoInicialDinheiroEmCentavos,
                    EnumTipoNumero.Centavos)
  }

  adicioneDinheiro(valorEmDinheiro: number, operador: Usuario) {
    if (this.status !== EnumStatusCaixa.Aberto) {
      throw new Error('O caixa não está aberto');
    }

    let valorEmCentavos = MoneyUtils.deNumberParaCentavos(valorEmDinheiro)

    if (valorEmCentavos <= 0) {
      throw new Error('O valor a ser adicionado deve ser maior que zero');
    }

    this.adicioneSaldo(this.formaDinheiro, valorEmCentavos);
    this.adicioneTransacao("Adição de dinheiro pelo operador " + operador.nome, EnumTipoTransacao.ENTRADA,
                           this.formaDinheiro, valorEmCentavos, EnumTipoNumero.Centavos)
  }

  //adiciona saldo a uma forma de pagamento, inicializando no mapa de saldos se for preciso
  private adicioneSaldo(formaDePagamento: FormaDePagamento, valorEmCentavos: number) {
    if (!this.mapaSaldos.has(formaDePagamento.id)) {
      this.mapaSaldos.set(formaDePagamento.id, new SaldoDaFormaDePagamento(this, formaDePagamento, 0))
      this.saldoPorFormaDePagamento.push(this.mapaSaldos.get(formaDePagamento.id));
    }

    this.mapaSaldos.get(formaDePagamento.id).adicione(valorEmCentavos);

  }

  // realiza a sangria de um valor em dinheiro do caixa, com a justificativa informada
  public realizeSangria(valorEmDinheiro: number, justificativa: string, operador: Usuario) {
    //a sangria só pode ser realizada se o caixa estiver aberto
    if (this.status !== EnumStatusCaixa.Aberto) {
      throw new Error('O caixa não está aberto');
    }

    let valorEmCentavos = MoneyUtils.deNumberParaCentavos(valorEmDinheiro)

    //a sangria só pode ser realizada se o valor em dinheiro for maior que zero
    if (valorEmCentavos <= 0) {
      throw new Error('O valor da sangria deve ser maior que zero');
    }

    //é obrigatório informar a justificativa
    if (!justificativa) {
      throw new Error('É preciso informar a justificativa da sangria');
    }

    //a sangria só pode ser realizada se o valor em dinheiro for menor ou igual ao saldo em dinheiro, consultado no mapa de saldos
    if (valorEmCentavos > this.getSaldo(this.formaDinheiro)) {
      throw new Error('O valor da sangria deve ser menor ou igual ao saldo em dinheiro');
    }

    //realiza a sangria
    this.sangrias.push(new Sangria(this, valorEmCentavos, justificativa, operador));
    this.adicioneTransacao("Sangria pelo operador " + operador.nome, EnumTipoTransacao.SAIDA,
                            this.formaDinheiro, valorEmCentavos, EnumTipoNumero.Centavos)

    //atualiza o saldo em dinheiro no mapa de saldos
    this.adicioneSaldo(this.formaDinheiro, -valorEmCentavos);

  }

  //obtem o saldo atual de uma determinada forma de pagamento, ou 0 se não foi inicializada
  public getSaldo(formaDePagamento: FormaDePagamento): number {
    let saldoDaForma = this.getSaldoDaFormaDePagamento(formaDePagamento)

    if(!saldoDaForma) return 0;

    return saldoDaForma.saldoEmCentavos;
  }

  public getSaldoDaFormaDePagamento(formaDePagamento: FormaDePagamento): SaldoDaFormaDePagamento {
    if (!this.mapaSaldos.has(formaDePagamento.id)) {
      return null;
    }

    return this.mapaSaldos.get(formaDePagamento.id);
  }

  fecharCaixa(observacao: string , operador: Usuario, saldoApuradoDinheiro: number) {
    //apenas o operador que abriu pode fechar o caixa

    if(this.operador && !operador)
      throw new Error('O caixa só pode ser fechado pelo operador que o abriu');

    if (operador && this.operador && this.operador.id !== operador.id) {
      throw new Error('O caixa só pode ser fechado pelo operador que o abriu');
    }

    if (this.status === EnumStatusCaixa.Fechado) {
      throw new Error('O caixa já está fechado');
    }
    this.horarioFechamento = new Date();
    this.saldoFinalDinheiroEmCentavos = this.getSaldo(this.formaDinheiro);
    this.saldoFinalTotal = this.calculeSaldoFinalTotal();
    this.saldoApuradoDinheiroEmCentavos = saldoApuradoDinheiro ?  MoneyUtils.deNumberParaCentavos(saldoApuradoDinheiro) : 0;
    this.observacao = observacao;
    this.status = EnumStatusCaixa.Fechado;
  }

  //

  adicionePedido(pedido: PedidoGenerico): void {
    if (!pedido.pago) {
      throw new Error('O pedido não está pago');

    }
    for (let pagamento of pedido.pagamentos) {
      let valorEmCentavos = MoneyUtils.deNumberParaCentavos(pagamento.valor);
      this.adicioneSaldo(pagamento.formaDePagamento, valorEmCentavos);
      this.adicioneTransacao("Pagamento do pedido " + pedido.id + " em " + pagamento.formaDePagamento.descricao,
        EnumTipoTransacao.ENTRADA, pagamento.formaDePagamento,  valorEmCentavos, EnumTipoNumero.Centavos, pedido);
    }
  }

  adicioneComanda(comanda: Comanda): void {
    if(comanda.status !== EnumStatusComanda.Fechada) {
      throw new Error('A comanda não está fechada');
    }

    for(let pagamento of comanda.pagamentos) {
      let valorEmCentavos = MoneyUtils.deNumberParaCentavos(pagamento.valor);
      this.adicioneSaldo(pagamento.formaDePagamento, valorEmCentavos);
      this.adicioneTransacao("Pagamento da comanda " + comanda.id + " em " + pagamento.formaDePagamento.descricao,
        EnumTipoTransacao.ENTRADA, pagamento.formaDePagamento,  valorEmCentavos, EnumTipoNumero.Centavos, null, false, comanda );
    }
  }

  removaPedido(pedido: PedidoGenerico) {
    pedido.pagamentos.forEach((pagamento: PagamentoPedido) => {
      this.estorneTransacaoDoPagamento(pedido, pagamento);
    })

  }

  removaComanda(comanda: Comanda, pagamentosCancelados: any[]) {
    pagamentosCancelados.forEach((pagamento: PagamentoComanda) => {
      this.estorneTransacaoDoPagamentoComanda(comanda, pagamento);
    })
  }


  obtenhaResumo(): ResumoCaixa {
    return new ResumoCaixa(this);
  }


  calculeSaldoFinalTotal(): any {
    //soma o total do saldo de todas as formas de pagamento e retorna o valor
    let total = 0;

    //percorre os saldos em centavos das formas de pagamento usando forEach
    this.saldoPorFormaDePagamento.forEach((valor) => {
      total += valor.saldoEmCentavos;
    });


    return total;
  }



  async atualize(): Promise<any> {
    return Promise.resolve(undefined);
  }

  async insira(): Promise<any> {
    return Promise.resolve(undefined);
  }

  async insiraGraph(): Promise<any> {
    return Promise.resolve(undefined);
  }

  mapeador(): MapeadorBasico {
    return new MapeadorDeCaixa();
  }

  async remova(): Promise<any> {
    return Promise.resolve(undefined);
  }

  async salve(graph?: boolean): Promise<void> {
    return Promise.resolve(undefined);
  }

  async valide(contexto?: any, catalogo?: any): Promise<any> {
    return Promise.resolve(undefined);
  }


  adicioneTransacao(descricao: string, tipoTransacao: EnumTipoTransacao, formaDePagamento: FormaDePagamento,
                    valor: number, tipoNumero: EnumTipoNumero, pedido: PedidoGenerico | null = null,
                    estornavel: boolean = false, comanda: Comanda = null): Transacao {
    if(tipoNumero === EnumTipoNumero.Number)
      valor = MoneyUtils.deNumberParaCentavos(valor);

    let transacao = new Transacao(this, this.operador, descricao, tipoTransacao, formaDePagamento, valor, pedido, estornavel, comanda)
    this.transacoes.push(transacao);

    return transacao;

  }

  obtenhaTotalEntradas(formaDePagamento: FormaDePagamento = null) {
    let tipoDeTransacao = EnumTipoTransacao.ENTRADA;

    return this.retorneSoma(tipoDeTransacao, formaDePagamento);
  }

  obtenhaTotalSaidas(formaDePagamento: FormaDePagamento = null, apenasComPedido: boolean = false) {
    let tipoDeTransacao = EnumTipoTransacao.SAIDA;

    return this.retorneSoma(tipoDeTransacao, formaDePagamento, apenasComPedido);
  }

  private retorneSoma(tipoDeTransacao: EnumTipoTransacao, formaDePagamento: FormaDePagamento, apenasComPedido: boolean = false) {
    let transacoes = this.transacoes.filter((transacao) => {
      if(apenasComPedido && !transacao.pedido) return false;

      return transacao.tipo === tipoDeTransacao ?
        formaDePagamento === null || transacao.formaDePagamento.id === formaDePagamento.id : false;
    })

    if(transacoes.length === 0) return 0;

    return this.someValoresDasTransacoes(transacoes);
  }

  private someValoresDasTransacoes(transacoes: Transacao[]) {
    let totalTransacoes = transacoes.map((transacao) => {
      return transacao.valor;
    }).reduce((total, valor) => {
      return total + valor;
    });

    return totalTransacoes;
  }

  calculeTotais() {

    let totalEntradas = this.obtenhaTotalEntradas() - this.saldoInicialEmDinheiro;
    let totalSaidas = this.obtenhaTotalSaidas();
    let totalSangrias = this.obtenhaTotalSangrias();

    let totalEntradasPorForma: any[] = []

    let saldoDinheiro = 0;

    for(let forma of this.saldoPorFormaDePagamento) {
      let totalDaForma = this.obtenhaTotalEntradas( forma.formaDePagamento);
      if(totalDaForma > 0) {
        if(forma.formaDePagamento.descricao === 'Dinheiro') {
          totalEntradasPorForma.unshift({descricao: forma.formaDePagamento.descricao, valor: totalDaForma - this.saldoInicialEmDinheiro});
        }

        else
          totalEntradasPorForma.push({descricao: forma.formaDePagamento.descricao, valor: totalDaForma});
      }

    }

    let totalSaidasPorForma: any[] = []// somando apenas as que tem pedido, pois as sangrias já estão separadas

    for(let forma of this.saldoPorFormaDePagamento) {
      let totalDaForma = this.obtenhaTotalSaidas( forma.formaDePagamento, true);
      if(totalDaForma > 0) {
        if(forma.formaDePagamento.descricao === 'Dinheiro') {
          if(totalDaForma !== totalSangrias)
          totalSaidasPorForma.unshift({descricao: forma.formaDePagamento.descricao, valor: totalDaForma - totalSangrias});
        }

        else
          totalSaidasPorForma.push({descricao: forma.formaDePagamento.descricao, valor: totalDaForma});
      }
    }

    saldoDinheiro = this.mapaSaldos.get(this.formaDinheiro.id).saldo;

    return {
      totalEntradasPorFormaDePagamento: totalEntradasPorForma,
      totalSaidasPorFormaDePagamento: totalSaidasPorForma,
      saldoDinheiro: saldoDinheiro,
      totalEntradas: totalEntradas,
      totalSangrias: totalSangrias,
      totalSaidas: totalSaidas,
      saldo: this.saldoInicialEmDinheiro + totalEntradas  - totalSaidas
    }
  }

  obtenhaDTO() {
    //convert the object to a non circular DTO
    let dto = new CaixaDTO(this);
    return dto;
  }

  recalculeMapaDeSaldos() {
    this.mapaSaldos.clear();

    this.saldoPorFormaDePagamento.forEach((saldo) => {
      this.mapaSaldos.set(saldo.formaDePagamento.id, saldo);
    })
  }

  private obtenhaTotalSangrias() {
    if(!this.sangrias || this.sangrias.length === 0) return 0;

    return this.sangrias.map((sangria) => {
      return sangria.valorRetirado;
    }).reduce((total, valor) => {
      return total + valor;
    });
  }

  estorneTransacaoDoPagamento(pedido: PedidoGenerico, pagamento: PagamentoPedido) {
    this.removaSaldo(pagamento.formaDePagamento, MoneyUtils.deNumberParaCentavos(pagamento.valor));
    this.adicioneTransacao('Estorno do pagamento do pedido ' + pedido.id, EnumTipoTransacao.SAIDA,
      pagamento.formaDePagamento, pagamento.valor, EnumTipoNumero.Number, pedido, false);
  }

  removaSaldo(formaDePaggamento: FormaDePagamento, valorEmCentavos: number) {
    this.adicioneSaldo(formaDePaggamento, -valorEmCentavos);
  }


  private estorneTransacaoDoPagamentoComanda(comanda: Comanda, pagamento: PagamentoComanda) {
    this.removaSaldo(pagamento.formaDePagamento, MoneyUtils.deNumberParaCentavos(pagamento.valor));
    this.adicioneTransacao('Estorno do pagamento da comanda ' + comanda.id, EnumTipoTransacao.SAIDA,
      pagamento.formaDePagamento, pagamento.valor, EnumTipoNumero.Number, null, false, comanda);
  }
}
