

import {Fatura} from "./faturamento/Fatura";
import * as moment from "moment";

export class FaturaAtivacao extends Fatura {

  constructor(public empresa: any,  public valor: number, dataVencimento: Date) { // JSON string dos módulo) {
    super(null, null,  dataVencimento );

    if(dataVencimento)
      this.referencia = Number(moment(dataVencimento).format('YYYYMM'));

    this.alvo = 'modulos'
  }

}
