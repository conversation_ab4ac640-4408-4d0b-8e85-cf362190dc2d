import { MapeadorDeTrechoDePrompt } from "../../mapeadores/MapeadorDeTrechoDePrompt";
import { Empresa } from "../Empresa";
import { TrechoDePrompt } from "./TrechoDePrompt";
import { MapeadorDeTemplatePromptDB } from "../../mapeadores/MapeadorDeTemplatePromptDB";

export class TemplateDePromptDB {
  id: number;
  nome: string;
  descricao: string;
  tipo: string;
  trechosDePrompt: TrechoDePrompt[];

  static calculeExpressao(msg: string, dados: any): string {
    msg = msg.replace(/\$\{(\w+(?:\.\w+)*)\}/g, function(match: string, propriedade: string) {
      let valores = propriedade.split(".");
      let objeto: any = dados;
      for (let i = 0; i < valores.length; i++) {
        if (!objeto.hasOwnProperty(valores[i])) {
          return match;
        }
        objeto = objeto[valores[i]];
      }
      return objeto;
    });

    return msg;
  }

  static avaliaExpressao(trecho: TrechoDePrompt, variaveis: {[chave: string]: any}): boolean {
    try {
      const expressao = trecho.condicao;
      if( !variaveis || Object.keys(variaveis).length === 0 ) {
        console.log(3);
        return true;
      }
      console.log('\n\n\n\t' + expressao, variaveis);

      // Cria uma função que retorna o valor da expressão
      const funcao = new Function(...Object.keys(variaveis), `return ${trecho.condicao};`);
      // Executa a função com os valores das variáveis
      const valor = funcao(...Object.values(variaveis));

      console.log(valor);
      return valor;
    } catch (erro) {
      console.error("Erro ao avaliar a expressão:", erro);
      console.error('Erro no trecho:', trecho);
      return false; // Considera a expressão como falsa em caso de erro
    }
  }

  static async carregue(id: number, incluirTrechos: boolean = true) {
    const templateDePromptDBObj: TemplateDePromptDB = await new MapeadorDeTemplatePromptDB().selecioneSync({
      id: id
    });

    if (incluirTrechos && templateDePromptDBObj) {
      const mapeador = new MapeadorDeTrechoDePrompt();
      const trechos = await mapeador.listeAsync({
        idTemplate: id,
        global: true
      });
      templateDePromptDBObj.trechosDePrompt = trechos;
    }

    return templateDePromptDBObj;
  }

  /*
  //percorre todos trechos de prompt e concatena 1 por linha para gerar o prompt
  obtenhaPrompt(indicarTipo: boolean = false) {
    let prompt = '';
    this.trechosDePrompt.forEach(trecho => {
      if( trecho.tipo === 'exemplos' ) return;

      //verfica se o trecho está ativo
      if (!trecho.ativo) return;

      if( indicarTipo ) {
        prompt += `(${trecho.escopo}) `;
      }

      if( trecho.exemplos.length > 0 ) {
        trecho.texto = this.geraTexto(trecho.exemplos);
      }

      prompt += `[${trecho.intent}] - ${trecho.texto}\n`;
    });

    return prompt.slice(0, -1); // remove o último caractere da string (a quebra de linha)
  }
  */

  obtenhaPrompt(indicarTipo: boolean = false, variaveis: {[chave: string]: any} = {}) {
    let prompt = '';
    this.trechosDePrompt.forEach(trecho => {
      if(trecho.tipo === 'exemplos' || !trecho.ativo) return;

      // Avalia a expressão condicional, se presente
      if(trecho.condicao && !TemplateDePromptDB.avaliaExpressao(trecho, variaveis)) {
        return;
      }

      if(indicarTipo) {
        prompt += `(${trecho.escopo}) `;
      }

      if(trecho.tipo != 'texto' && trecho.exemplos.length > 0) {
        trecho.texto = this.geraTexto(trecho.exemplos);
      }

      prompt += `[${trecho.intent}] - ${trecho.texto}\n`;
    });

    return prompt.slice(0, -1); // Remove o último caractere da string (a quebra de linha)
  }

  obtenhaMensagem(dados: any): string {
    let msg = this.obtenhaPrompt(false, dados);

    msg = TemplateDePromptDB.calculeExpressao(msg, dados);

    return msg;
  }

  //retorna os dados do template e o prompt com os trechos
  crieDTO(indicarTipo: boolean = false) {
    return {
      id: this.id,
      nome: this.nome,
      descricao: this.descricao,
      tipo: this.tipo,
      trechosDePrompt: this.trechosDePrompt,
      template: this.obtenhaPrompt(indicarTipo)
    };
  }

  private geraTexto(exemplos: Array<any>) {
    let texto = '';

    exemplos.forEach((exemplo, index) => {
      if (index > 0) {
        texto += '\n';
      }

      texto += `${index + 1}. Cliente: ${exemplo.pergunta} Mia: ${exemplo.resposta}`;
    });

    return texto;
  }
}
