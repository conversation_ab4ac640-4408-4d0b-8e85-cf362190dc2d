import {Contato} from './Contato';
import {Plano} from './Plano';
import {Empresa} from "./Empresa";

export class Cartao {
  id: number;
  contato: Contato;
  pontos: number;
  plano: Plano;
  empresa: Empresa;
  codigoTemp: string;
  pontosAcumulados: 0;
  pontosResgatados: 0;
  qtdeVisitas: 0;
  constructor(id: number, contato: Contato, plano: Plano, pontos: number) {
    this.id = id;
    this.contato = contato;
    this.plano = plano;
    this.pontos = pontos || 0;
  }

  acumulaPontos() {
    return this.plano.acumulaPontos()
  }

  acumulaSelos() {
    return this.plano.acumulaSelos()
  }

  acumulaReais() {
    return this.plano.acumulaReais()
  }

  debitePontos(pontos: number, brindeResgatado: any = null) {
    this.pontos -= Number(pontos);
    this.arredondePontuacao();
    if(brindeResgatado) brindeResgatado.setSaldo(this.pontos)
  }

  creditePontos(pontos: number) {
    this.pontos += Number(pontos);
    this.arredondePontuacao();
  }

  private arredondePontuacao() {
    //arrendondano sempre em 2 casas decimais
    this.pontos =  Number ( (Math.round(this.pontos  * 100) / 100).toFixed(2) );
    if(this.pontos < 0) this.pontos = 0;
  }

  obtenhaDescricaoPontos(pontos: number): string {
    return this.plano.obtenhaDescricaoPontos(pontos)
  }
}
