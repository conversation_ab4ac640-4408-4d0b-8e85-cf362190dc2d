import {DiaDaSemanaEnum} from "./DiaDaSemanaEnum";
import {EnumServicoHorarioFuncionamento} from "../lib/emun/EnumServicoHorarioFuncionamento";
let moment = require("moment");


export class HorarioFuncionamento {
  empresa: any;
  constructor(public servico: EnumServicoHorarioFuncionamento,
              public diaDaSemana: DiaDaSemanaEnum, public horarioAbertura: string,
              public horarioFechamento: string, public funciona: boolean) {
    if(!this.servico)
      this.servico = EnumServicoHorarioFuncionamento.Site;
  }

  estaDentroDoHorario(agoraNoFuso: Date): boolean {
    if(!this.funciona) return false;
    let funcionaDeMadrugada = this.funcionaDeMadrugada();
    let ehMadrugada = this.ehMadrugada(agoraNoFuso);

    if(!ehMadrugada) {
      if(!this.mesmoDiaDaSemana(agoraNoFuso)) return null;

      let [horaAbertura, minutoAbertura] = this.horarioAbertura.split(":");
      let [horaFechamento, minutoFechamento] = this.horarioFechamento.split(":");


      let horarioAbertura  = moment( new Date(agoraNoFuso)).seconds(0).set({ hour: horaAbertura, minute: minutoAbertura });
      let horarioFechamentoDia = moment( new Date(agoraNoFuso)).seconds(0).set({ hour: horaFechamento, minute: minutoFechamento });

      if(horarioFechamentoDia.hours() < horarioAbertura.hours()) //depois das 00:00
        horarioFechamentoDia = moment().endOf('day');

      return moment(agoraNoFuso).seconds(0).isBetween(horarioAbertura, horarioFechamentoDia, undefined, '[]');
    }
    if(!funcionaDeMadrugada) return null;

    if(!this.foiOntemEmRelacaoA(agoraNoFuso)) return null;

    //funciona de madrugada, hoje é o dia seguinte e o horário é menor que o horário de fechamento;
    return moment(agoraNoFuso).isBefore(moment(this.horarioFechamento , "HH:mm:ss"))
  }

  public funcionaDeMadrugada() {
    let funcionaDeMadrugada = false;

    if (this.horarioFechamento < this.horarioAbertura ||  this.horarioAbertura < '06:00:00')
      funcionaDeMadrugada = true;
    return funcionaDeMadrugada;
  }

  public ehMadrugada(horario: Date) {
    return horario.getHours() < 6;
  }

  mesmoDiaDaSemana(horario: Date): boolean {
    return horario.getDay() === this.diaDaSemana;
  }

  jaPassouDoHorario(agoraNoFuso: Date){
    if(!this.horarioFechamento) return  true;
    return Number(this.horarioFechamento.substr(0, 5).replace(/\D/g, '')) <
            Number(moment(agoraNoFuso).format('HHmm'))
  }

  ehAntesDo(horario: string){
    if(!horario || !this.horarioAbertura) return true;

    return Number(this.horarioAbertura.substr(0, 5).replace(/\D/g, '')) <
            Number(horario.substr(0, 5).replace(/\D/g, ''))
  }


  foiOntemEmRelacaoA(data: Date) {
    let dia = data.getDay();

    if(dia > 0) return (dia - 1) === this.diaDaSemana

    return this.diaDaSemana === 6;
  }


  // tslint:disable-next-line:member-ordering
  static setHorariosFuncionameto(empresa: any){

  }
}
