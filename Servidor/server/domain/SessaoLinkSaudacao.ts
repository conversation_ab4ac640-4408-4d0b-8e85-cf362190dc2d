import {Contato} from "./Contato";
import { nanoid } from 'nanoid/async';
import {MensagemEnviada} from "./MensagemEnviada";
import {Ambiente} from "../service/Ambiente";
import {VariaveisDeRequest} from "../service/VariaveisDeRequest";
import {Empresa} from "./Empresa";
import { Endereco } from "./delivery/Endereco";
import { FormaDeEntregaEmpresa } from "./delivery/FormaDeEntregaEmpresa";
import { FormaDePagamento } from "./delivery/FormaDePagamento";
import { MapeadorDeSessaoLinkSaudacao } from "../mapeadores/MapeadorDeSessaoLinkSaudacao";

export class SessaoLinkSaudacao {
  id: number;
  contato: Contato;
  hash: string;
  horario: Date;
  expirada: boolean;
  telefone: string;
  codigoPais: string;
  qtdeAcessos = 0;
  mensagemEnviada: MensagemEnviada;
  dadosProduto: string;
  empresa: Empresa;

  //vem do fluxo do typebot
  endereco: string;
  complemento: string;
  formaDeEntrega: string;
  formaDePagamento: string;
  pedidoGuid: string;
  static async CrieSessao(contato: Contato, telefone: any, codigoPais: any) {
    let sessao = new SessaoLinkSaudacao()
    sessao.contato  = contato;
    sessao.hash =  await nanoid(10);
    sessao.horario = new Date()
    sessao.expirada = false;
    sessao.telefone = contato ? contato.telefone : telefone;
    sessao.codigoPais = contato ? contato.codigoPais : codigoPais;

    return sessao;
  }

  obtenhaLinkInteligente() {
    const variaveisDeRequest = new VariaveisDeRequest();

    const link = variaveisDeRequest.obtenhaUrlCardapio(Ambiente.Instance.contexto().empresa)
    let contexto = {
      linkCardapio: link  + '/link/' + this.telefone + '/' + this.hash
    };

    return contexto;
  }

  static async atualize(sessao: SessaoLinkSaudacao) {
    const sessaoMapeador = new MapeadorDeSessaoLinkSaudacao();
    await sessaoMapeador.atualizeSync(sessao);
  }
}
