import { TaxaDeEntregaCalculadaController } from './routes/taxaDeEntregaCalculada';
import * as express from 'express';
var uid = require('uid-safe').sync
import * as cors from 'cors';
import * as path from 'path';
import * as bodyParser from 'body-parser';
import * as favicon from 'serve-favicon';
import * as expressSession from 'express-session';
// @ts-ignore
import * as mybatis from 'mybatisnodejs';
import * as mysql from 'mysql';
import * as databaseConfig from './database.json';
import * as fileUpload from 'express-fileupload';
import * as redisStore from 'connect-redis';
import {Resposta} from './utils/Resposta';
import {Ambiente} from './service/Ambiente';
import {ConfigPassport} from './config/ConfigPassport';
import {AuthController} from './routes/auth';
import {AuthDiretoController} from './routes/auth-auto';
import {ContatosController} from './routes/contatos';
import {UsuarioController} from './routes/usuario';
import {AtividadesController} from './routes/atividade';
import {Sessao} from "./service/Sessao";
import {VariaveisDeRequest} from "./service/VariaveisDeRequest";
import {PlanosController} from './routes/planos';
import {CartoesController} from './routes/cartoes';
import {BrindesController} from "./routes/brinde";
import {NotificacaoController} from "./routes/notificacao";
import {ApiController} from "./routes/api";
import {MapeadorDeEmpresa} from "./mapeadores/MapeadorDeEmpresa";
import {LinkController} from "./routes/link";
import {MensagensController} from "./routes/mensagens";
import {Empresa} from "./domain/Empresa";
import {EmpresasController} from "./routes/empresas";
import {TiposDePontuacaoController} from "./routes/tiposDePontuacao";
import {UploadController} from "./routes/upload";
import {ProdutosController} from "./routes/produto";
import {FotosController} from "./routes/foto";
import {CampanhaController} from "./routes/campanha";
import {CartoesSemLoginController} from "./routes/cartaoSemLogin";
import {BotsController} from "./routes/bot";
import {ContratosController} from "./routes/contratos";
import {ExportadorController} from "./routes/exportador";
import {PagamentoController} from "./routes/pagamentos";
import {IuguController} from "./routes/iugu";
import {PoliticaDePrivacidadeController} from "./routes/politica-privacidade";
import {PedidosController} from "./routes/pedidos";
import {PlanosEmpresasController} from "./routes/planos-empresa";
import {VantagensController} from "./routes/vantagens";
import {ProspectsController} from "./routes/prospects";
import {PlanoEmpresarial} from "./domain/faturamento/PlanoEmpresarial";
import {PedidoUtils} from "./utils/PedidoUtils";
import {EmailsController} from "./routes/emails";
import {ImportadorController} from "./routes/importador";
import {RecebimentosController} from "./routes/recebimentos";
import {VendasController} from "./routes/vendas";
import {FormasDePagamentoController} from "./routes/formasDePagamentos";
import {NumeroWhatsappController} from "./routes/numeroWhatsapp";
import {QRCodeController} from "./routes/qrcode";
import {MesasController} from "./routes/mesa";
import {LinkMesaController} from "./routes/link-mesa";
import {CuponsController} from "./routes/cupons";
import {VitinesController} from "./routes/vitrines";
import {CardapioController} from "./routes/cardapio";
import {ApiV1Controller} from "./routes/apiv1";
import * as passport from "passport";
import {PagSeguroController} from "./routes/pagseguro";
import {CardapioPDFController} from "./routes/cardapiopdf";
import {HooksController} from "./routes/hooks";
import {IntegracaoERPController} from "./routes/integracaoerp";
import {ImportadorRedeController} from "./routes/importadorrede";
import {BannerController} from "./routes/banner";
import {ApiChinaController} from "./routes/apichina";
import {ComandaController} from "./routes/comanda";
import {ContratosAtivacaoController} from "./routes/contratos-ativacao";
import {ClassificacaoContatoController} from './routes/classificacao-contato';
import { CepsCustomizadosController } from './routes/cepscustomizados';
let redis = require("redis");
let client = redis.createClient();
const dateformat = require('dateformat');
const cookieParser = require('cookie-parser'); // Adicionar o cookie-parser
// @ts-ignore
import * as pluralize from "pluralize";
import {EmpresaService} from "./service/EmpresaService";
import {NotificacaoAppController} from "./routes/notificacaoapp";
import {MercadoPagoController} from "./routes/mercadopago";
import {TomtomController} from "./routes/tomtom";
import {FormasDeEntregaController} from './routes/formasDeEntrega';
import {NotasFiscaisController} from "./routes/nota-fiscal";
import {GrupoDeLojas} from "./domain/GrupoDeLojas";
import {PromocoesController} from "./routes/promocoes";
import {GrupoDeLojasService} from "./service/GrupoDeLojasService";
import {MapeadorDeUsuario} from "./mapeadores/MapeadorDeUsuario";
import {CampanhaRedeController} from "./routes/campanha-rede";
import {GruposDeLojasController} from "./routes/gruposDeLojas";
import {GrupoLojasController} from "./routes/gruposloja";
import {WhatsappController} from "./routes/whatsapp";
import {InstagramController} from "./routes/instagram";
import {Bots2Controller} from "./routes/bot2";
import {CieloController} from "./routes/cielo";
import {PagarmeController} from "./routes/pagarme";
import {CatalogosController} from "./routes/catalogos";

import {ChatwootController} from "./routes/chatwoot";
import {ApiGcomController} from "./routes/apigcom";
import {CloudWhatsappController} from "./routes/cloud_whatsapp";
import {MapeadorDeProduto} from "./mapeadores/MapeadorDeProduto";
import {Produto} from "./domain/Produto";
import {AvaliacaoDePedidosController} from "./routes/avaliacao";
import {TemplateController} from "./routes/template";
import {EntregadoresController} from "./routes/entregador";
import {IntegracaoController} from "./routes/integracao";
import {OpenDeliveryController} from "./routes/opendelivery";
import {OpenDeliveryApiOrderAppController} from "./routes/opendeliveryapi-orderapp";
import {BotpressController} from "./routes/botpress";
import {TradutorMensagemBotController} from "./routes/tradutor-mensagens-bot";
import {RedeController} from "./routes/rede";
import {ChatGPTController} from "./routes/chatgpt";
import {TemplatePromptController} from "./routes/template-prompt";
import {DisponibilidadeController} from "./routes/disponibilidades";
import {ChatBotController} from "./routes/chatbot";
import {ConfiguracoesMiaController} from "./routes/configuracoesMia";
import {CountryCodesController} from "./routes/paises";
import {CadQrCodeController} from "./routes/cad-qrcode";
import {CaixaController} from "./routes/caixa";
import {OpenDeliveryApiHooksAppController} from "./routes/opendeliveryapi-webhooks";
import {OpenDeliveryMockPdvController} from "./routes/opendeliverymock-pdv";
import {OpenDeliveryMockDeliveryController} from "./routes/opendeliverymock-delivery";
import {TemplatePromptDBController} from "./routes/template-prompt-db";
import { ChamadasIaController } from './routes/chamadas-ia';
import {UberDirectApiHooksAppController} from "./routes/uberdirectapi-webhooks";
import {UberDirectController} from "./routes/uberdirect";
import {CarrinhoController} from "./routes/carrinho";
import {IfoodController} from "./routes/ifood";
import {RdcrmController} from "./routes/rdcrm";
import {ImagemController} from "./routes/imagem";
import {CustomRedisStore} from "./CustomRedisStore";
import {RegistrosDeLoginController} from "./routes/registro_de_login";
import {FidelidadeController} from "./routes/fidelidade";
import {RespostaChatbotInstagramController} from "./routes/resposta-chatbot-instagram";
import {OpcaoRespostaChatbotInstagramController} from "./routes/opcao-resposta-chatbot-instagram";
import {SocketsController} from "./routes/sockets";
import { TemaPersonalizadoController } from './routes/temas';
import { PaletaCoresController } from './routes/paleta-cores';
import {ERedeItauController} from "./routes/erede";
//import { WhatsAppFlowsController } from './routes/whatsapp_flows';
import {NfceController} from "./routes/nfce";
import {CarregadorDeWebserviceSefaz} from "./utils/nfce/comunicacao/CarregadorDeWebserviceSefaz";
import {EstoqueController} from "./routes/estoque";
import {TypebotOauthController} from "./routes/typebot-oauth";
import {CartaoClienteController} from "./routes/cartao-cliente";
import {TunaPayController} from "./routes/tunapay";
import {TabletController} from "./routes/tablet";
import {ModulosController} from "./routes/modulos";
import { LeadsController } from './routes/leads';
import { CrmEmpresasController } from './routes/crm-empresas';
pluralize.addPluralRule(/al$/i, 'ais')

require('console-stamp')(console, { pattern: 'dd/mm/yyyy HH:MM:ss.l' });
const url = require('url');
const globalAny: any = global;
globalAny.versao = '0.21';
globalAny.pluralize = pluralize;

require('source-map-support').install();
const cheerio = require('cheerio');
const fs = require('fs');

// Creates and configures an ExpressJS web server.




export class App {
  // ref to Express instance
  public express: express.Application;
  public pool: any;

  constructor() {
    this.express = express();

    this.middleware();
    this.configureAmbiente();
    this.configureBancoDeDados();
    this.configureServicosSefaz();
    this.routes();
  }

  // Configure Express middleware.
  private middleware(): void {
    const env = this.express.get('env');
    globalAny.desenvolvimento = (env === "development");

    console.log( globalAny['desenvolvimento'] );
  }

  public configureBancoDeDados() {
    const contexto = mybatis.Contexto;

    this.express.use(contexto.domainMiddleware);
    globalAny.domainDir = './distServer/domain';
    globalAny.sessionFactory = new mybatis.Principal().processe('./distServer/mapeamentos/');
    globalAny.es7 = true;
    globalAny.exibirWarnings = false;
    globalAny.sessionFactory.antesDeExecutarAConsultaFn = (no: any, nomeCompleto: string, multicliente: boolean) => {

    //console.log(no.sql);
    //console.log(no.parametros);


      if ( multicliente ) {
        if (no.sql.indexOf('empresa_id') === -1 && no.sql.indexOf('catalogo_id') === -1) {

          throw new Error('Consulta não leva em consideração o id da empresa: ' + nomeCompleto + " " + no.sql);
        }
      }
    };


    const ambienteConfigurado = Ambiente.Instance.ambiente;

    const databaseconfig = (Ambiente.Instance.producao) ? databaseConfig.prod : databaseConfig.dev;

    this.pool  = mysql.createPool(databaseconfig);
    globalAny.pool = this.pool;
  }

  public configureAmbiente() {
    console.log('fui chamado ' + __dirname);
    const exists = fs.existsSync( __dirname + '/ambiente.json');

    //console.log('Existe? ' + exists);

    if (exists) {
      const ambiente =  require('./ambiente.json');
      console.log('ambiente:', ambiente);
      Ambiente.inicialize(ambiente.ambiente);
      console.log('Inicializado ambiente para:');
      console.log(ambiente);
    }
  }

  // Configure API endpoints.
  private routes(): void {
    const RedisStore = redisStore(expressSession);
    this.express.use(cookieParser()); // Adicionar o cookie-parser aqui
    const cookieExpirationDate = new Date();
    const cookieExpirationDays = 365;
    cookieExpirationDate.setDate(cookieExpirationDate.getDate() + cookieExpirationDays);

    this.express.use( expressSession({
      proxy: true,
      store: new CustomRedisStore({ host: 'localhost', port: 6379,  logErrors: true}) as any,
      genid: (req: any) => {
        // Match method in express-session
        // See https://github.com/expressjs/session/blob/v1.15.6/index.js#L502
        let id = uid(24);

        if (req.user) {
          client.hset(`sessaoUsuario:${req.user.id}`, id, id);
        }

        return id;
      },
      secret: 'senha___segura@sorteieme',
      resave: false,
      rolling: true,
      saveUninitialized: false,
      cookie: { httpOnly: true, expires: cookieExpirationDate, secure: true, sameSite: 'none' }
    }));

    this.configurePassport();

    this.express.use(fileUpload());

    const router: express.Router = express.Router();

    function determineUsuarioLogado(req: any, res: any, next: any){
      if (req.isAuthenticated())
        Ambiente.Instance.determineUsuarioLogado(req.user)

      return next();
    }

    function isAuthenticated(req: any, res: any, next: any) {
      let urlsLivres: Array<string> = ['/empresas/contato', '/admin/assinatura/pagamento', '/admin/recuperar', '/admin/assinatura/trocarcartao'];
      if (req.isAuthenticated() || urlsLivres.find( urlLivre => req.originalUrl.indexOf(urlLivre) === 0 )) {
        Ambiente.Instance.determineUsuarioLogado(req.user)

        return next();
      }

      res.redirect('/admin/login')
    }

    function isSuperAdmin(req: any, res: any, next: any) {
      if (req.isAuthenticated() ) {
        let usuario: any = req.user;

        if( usuario && usuario.admin ) {
          return next();
        }
      }
      res.redirect('/admin/login?t=/superadmin/empresas')
    }
    router.use(cors());

    router.use(bodyParser.json({
      verify: (req: any, res, buf, encoding) => {
        if(buf) req.rawBody = buf.toString();
      },
      limit: '5mb'
    }));

    router.use(bodyParser.urlencoded({'extended': false , limit: '5mb'}));
    router.use(express.static(path.join(__dirname, '../distServer/public'), {dotfiles: 'allow'}));
    console.log('Server: ' + path.join(__dirname, '../distServer/public/images'));

    let me = this;


    function isAdminRede(req: any, res: any, next: any) {
      if (req.isAuthenticated() ) {
        let usuario: any = req.user;

        if( usuario ) {
          return next();
        }
      }
      res.redirect('/admin-rede/login?t=' +  req.originalUrl)
    }

    function isAdminGrupo(req: any, res: any, next: any) {
      if (req.isAuthenticated() ) {
        let usuario: any = req.user;

        if( usuario ) {
          return next();
        }
      }
      res.redirect('/admin-grupo/login?t=' +  req.originalUrl);
    }

    router.get('/termos-de-uso', (req: any, res: any) => {
      res.render('termos-de-uso.ejs', {});
    });

    router.get('/politica-de-privacidade', (req: any, res: any) => {
      res.render('politica-de-privacidade.ejs', {});
    });

    router.get('/admin-sdr/*', isAdminRede, function(req, res, next) {
      res.render('rede.ejs', {dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });
    router.get('/admin-grupo/login', function(req, res, next) {
      console.log('grupo');
      res.render('grupo.ejs', {dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    router.get('/admin-grupo/*', isAdminGrupo, function(req, res, next) {
      res.render('grupo.ejs', {dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    async function calculateRequestDomain(req: any, res: any): Promise<string> {
      let dominio = req.get('host');

      let empresaDaRequest = '';
      if( req.header('nomeempresa') ) {
        empresaDaRequest = req.header('nomeempresa');
      }

      let grupoDeLojas: GrupoDeLojas = await GrupoDeLojasService.Instancia().obtenha(dominio);

      if( grupoDeLojas ) {
        req.grupoDeLojas = grupoDeLojas;

        if( req.originalUrl === '/' || req.originalUrl.startsWith('/?') ) {
          console.log('trate link franquia');
          return dominio;
        }
        const urlReferer = req.headers.referer;

        let linksInternosLoja: any = ['/api/', '/pedidos', '/cupons', '/mercadopago', '/promocoes',
          '/pedido/acompanhar', '/auth/'];

        if( linksInternosLoja.find((link: string) => req.originalUrl.indexOf(link) >= 0 )){
          if( urlReferer && urlReferer.indexOf('/marca/') !== -1 ) {
            const objUrlReferer = new URL(urlReferer);
            dominio = objUrlReferer.pathname.replace('/marca/', '')
              + '.meucardapio.ai';
          } else if( empresaDaRequest ) {
            dominio = empresaDaRequest + '.meucardapio.ai';
          } else {
            if( req.grupoDeLojas.empresaPrincipal ) {
              dominio = req.grupoDeLojas.empresaPrincipal.dominio + '.meucardapio.ai';
              console.log('url: ' + req.originalUrl + ' ' + urlReferer);
            }
          }
        }
      }

      empresaDaRequest = await new EmpresaService().obtenhaEmpresaPeloLink(dominio)

      return empresaDaRequest;
    }

    const originalConsoleLog = console.log;

    console.log = function(...args: any[]) {
      const dominioAtivo = require('domain').active;

      if( !dominioAtivo || !dominioAtivo.contexto || !dominioAtivo.contexto.log ) {
        originalConsoleLog(...args);
        return;
      }
      let contexto = dominioAtivo.contexto.log; //dados do log
      const user = contexto.usuario;
      const dominio = contexto.dominio;

      const metodo = contexto.metodo;
      const url = contexto.url;
      const ip = contexto.ip;

      // Adicione o contexto da requisição, como o método HTTP e a URL
      const reqContext = `${ip} - [${dateformat(new Date(), "dd/mm/yyyy HH:MM:ss.l")} (${dominio}) uid: ${user?.id} - ${metodo} "${url}"]`;
      // Chame o console.log original com o contexto da requisição e os argumentos originais
      originalConsoleLog(reqContext, ...args);
    };

    router.use(async (req: any, res, next) => {
      let contexto = require('domain').active.contexto;

      const user = req.user ? req.user : {id: -1, nome: 'anonimo'}
      const dominio = await calculateRequestDomain(req, res);
      const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;

      contexto.log = {
        usuario: user,
        dominio: dominio,
        empresa: req.empresa,
        url: req.url,
        metodo: req.method,
        ip: ip
      };

      next();
    });

    function determineDomainFromRequest(urlReferer: string, originalUrl: string,
                                        empresaDaRequest: string, next: Function, grupoDeLojas: any): string {
      let dominio = '';
      let linksInternosLoja: any = ['/api/', '/pedidos', '/cupons', '/mercadopago', '/promocoes', '/pedido/acompanhar', '/auth/'];

      if (linksInternosLoja.find((link: string) => originalUrl.indexOf(link) >= 0)) {
        if (urlReferer && urlReferer.indexOf('/marca/') !== -1) {
          const objUrlReferer = new URL(urlReferer);
          dominio = objUrlReferer.pathname.replace('/marca/', '') + '.meucardapio.ai';
        } else if (empresaDaRequest) {
          dominio = empresaDaRequest + '.meucardapio.ai';
        } else {
          if (grupoDeLojas.empresaPrincipal) {
            dominio = grupoDeLojas.empresaPrincipal.dominio + '.meucardapio.ai';
            console.log('url: ' + urlReferer);
          } else {
            return '';
          }
        }
      }
      return dominio;
    }

    router.use('*', async function(req: any, res: any, next: any) {
      let dominio = req.get('host');

      let empresaDaRequest = '';
      if( req.header('nomeempresa') ) {
        empresaDaRequest = req.header('nomeempresa');
      }

      //console.log(req.originalUrl);
      let grupoDeLojas: GrupoDeLojas = await GrupoDeLojasService.Instancia().obtenha(dominio);

      if( grupoDeLojas ) {
        req.grupoDeLojas = grupoDeLojas;

        if( req.originalUrl === '/' || req.originalUrl.startsWith('/?') ) {
          console.log('trate link franquia');
          me.trateLinkDeFranquia(req, res, next);
          return;
        }
        const urlReferer = req.headers.referer;

        let linksInternosLoja: any = ['/api/', '/pedidos', '/cupons', '/mercadopago', '/promocoes',
          '/pedido/acompanhar', '/auth/'];

        if( linksInternosLoja.find((link: string) => req.originalUrl.indexOf(link) >= 0 )){
          if( urlReferer && urlReferer.indexOf('/marca/') !== -1 ) {
            const objUrlReferer = new URL(urlReferer);
            dominio = objUrlReferer.pathname.replace('/marca/', '')
              + '.meucardapio.ai';
          } else if( empresaDaRequest ) {
            dominio = empresaDaRequest + '.meucardapio.ai';
          } else {
            if( req.grupoDeLojas.empresaPrincipal ) {
              dominio = req.grupoDeLojas.empresaPrincipal.dominio + '.meucardapio.ai';
              console.log('url: ' + req.originalUrl + ' ' + urlReferer);
            } else {
              next();
              return;
            }
          }
        } else {
          next();
          return;
        }
      }

      empresaDaRequest = await new EmpresaService().obtenhaEmpresaPeloLink(dominio)

      if(!empresaDaRequest || empresaDaRequest.indexOf('www') >= 0) // domínio sem empresa
        return next();

      //let sessao = Sessao.Instancia(req.session)

      /*
      if(sessao.idEmpresaLogada() !== empresaDaRequest) {
        console.log('Ocorreu logout devido a mudança de id: ' + sessao.idEmpresaLogada() + " -> " + empresaDaRequest);
        sessao.setEmpresaLogada(empresaDaRequest);
        req.logout();
      }
       */

      me.carregueEmpresaLogada(empresaDaRequest, function(empresaLogada: any) {
        let contexto = require('domain').active.contexto;

        if(empresaLogada && new EmpresaService().empresaPossuiModulosNecessarios(empresaLogada, dominio)){
          contexto.idEmpresa = empresaLogada.id;
          contexto.empresa = empresaLogada;
          contexto.usuario = req.user;
          req.empresa = empresaLogada;
          next();
        } else {
          console.log('nao foi possivel carregar dados para empresa: ' + req.get('host') + ' -  ');

          if(empresaLogada) console.log('Empresa nao possui modulos necessarios.');


          res.render('404.ejs', {});
        }
      });
    });

    router.get('/admin-rede/login', function(req, res, next) {
      console.log('rede');
      res.render('rede.ejs', {dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    router.get('/admin-rede/*', isAdminRede, function(req: any, res, next) {
      const empresa = req.empresa;

      if( !empresa.dadosRede ) {
        res.render('404.ejs', {});
        return;
      }

      res.render('rede.ejs', {dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    router.get('/whatsapp/pedido/mensagem/:guid', async (req: any, res) => {
      let guid = req.params.guid;
      let idOperador = req.query.op;

      let numeroWhatsapp = req.empresa.numeroWhatsapp;

      if( idOperador ) {
        const mapeadorDeUsuario = new MapeadorDeUsuario();

        let usuario = await mapeadorDeUsuario.selecioneSync({ id: idOperador});

        if( usuario.numeroWhatsapp ) {
          numeroWhatsapp = usuario.numeroWhatsapp;
        }
      }

      let msgPedido = await PedidoUtils.obtenhaMensagemPedidoFinalizado(req.empresa, guid);
      let numeroZap = numeroWhatsapp.whatsapp.replace(/\D/g, "");
      const   link = String(`http://wa.me/55${numeroZap}?text=${msgPedido}`);
      console.log(link)
      res.redirect(link);
    });

    router.get('/cardapio/*', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.use('/loja/mesa', LinkMesaController);

    router.get('/marca/:dominio', function(req, res, next) {
      me.trateLinkDeFranquia(req, res, next);
    });

    router.get('/marca/:dominio/*', function(req, res, next) {
      me.trateLinkDeFranquia(req, res, next);
    });

    router.get('/lojas', function(req, res, next) {
      me.trateLinkDeFranquia(req, res, next);
    });

    router.get('/loja/*', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/franquia/*', function(req, res, next) {
      me.trateLinkDeFranquia(req, res, next);
    });

    router.get('/produto/*', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/categoria/*', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/loja', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/forma-entrega', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/cardapio', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/busca', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/busca/ecommerce/*', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/pedido', function(req: any, res, next) {
      if( req.grupoDeLojas ) {
        me.trateLinkDeFranquia(req, res, next);
        return;
      }

      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/cadastro', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/criar-endereco', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/perfil', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/login', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/meusPedidos', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });


    router.get('/index', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });



    router.get('/carrinho', function(req: any, res: any, next) {
      if( req.grupoDeLojas ) {
        me.trateLinkDeFranquia(req, res, next);
        return;
      }

      me.trateLinkDeLoja(req, res, next);
    });
    router.get('/pagamento', function(req: any, res: any, next) {
      if( req.grupoDeLojas ) {
        me.trateLinkDeFranquia(req, res, next);
        return;
      }

      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/pedido/acompanhar/*', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/local/*', function(req, res, next) {
      me.trateLinkDeLoja(req, res, next);
    });

    router.get('/desconto/*', function(req: any, res: any, next) {
      if( req.grupoDeLojas ) {
        me.trateLinkDeFranquia(req, res, next);
        return;
      }

      me.trateLinkDeLoja(req, res, next);
    });


    //url retorno antigo cielo
    router.get('/retorno/pagamento/externo', function(req, res, next) {
      console.log('recebendo retorno superlink pagamento externo:')
      console.log(req.hostname)
      console.log(req.query)

      let contexto = require('domain').active.contexto;
      let urlRaiz = (new VariaveisDeRequest()).obtenhaUrlRaiz(contexto.empresa);

      const sessao: any = req.session;
      const codigoPedido = sessao.codigoPedido;

      if(codigoPedido){
        const urlRedirect = String(`${urlRaiz}/loja/pedido/acompanhar/${codigoPedido}`);
        console.log(urlRedirect)
        res.redirect(urlRedirect);
      } else {
        res.send('Sua sessão expirou')
      }

    });

    router.get('/:dominio', (req, res, next) => {
      let dominio: string = req.get('host').split('.') [0];

      if(dominio !== 'meuzap') return next();

      let mapeador: MapeadorDeEmpresa = new MapeadorDeEmpresa();

      mapeador.selecioneSync({ dominio: req.params.dominio}).then((empresa: Empresa) => {
        if(!empresa)
          return res.json(Resposta.erro('Empresa ' + req.params.dominio + ' não encontrada.'))

        let whatsapp = empresa.numeroWhatsapp ? empresa.numeroWhatsapp.whatsapp : empresa.whatsapp;
        //TODO registrar em algum lugar que houve o acesso ao link da empresa
        res.redirect('http://wa.me/55' + whatsapp);
      });
    })

    router.get('/superadmin/*', isSuperAdmin, function(req, res, next) {
      let contexto = require('domain').active.contexto;

      res.render('home.ejs', {empresa: contexto.empresa, dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    router.get('/', async function(req, res, next) {
      // console.log('home');
      //console.log('Ambiente: ' + Ambiente.Instance.producao);
      let contexto = require('domain').active.contexto;
      const host = req.get('host');

      let dados: any = {
        dados: JSON.stringify({}),
        producao: Ambiente.Instance.producao
      };

      let empresa = contexto.empresa


      if(empresa) {
        //Está em um contexto de empresa

        if( empresa.temPedidos() && host.indexOf('promokit.com.br') !== -1 ) {


          res.redirect(empresa.obtenhaHostLoja())
          return;
        }

        let prefixo = await new EmpresaService().obtenhaPrefixo(req);

        dados.infoPagina = {
          descricao: contexto.empresa.descricao,
          titulo:  contexto.empresa.nome + prefixo,
          idEmpresa: contexto.empresa.id
        }
        dados.empresa = contexto.empresa;

        let urlRaiz = (new VariaveisDeRequest()).obtenhaUrlRaiz(contexto.empresa);

        dados.og = {
          url: urlRaiz,
          titulo: contexto.empresa.nome + prefixo,
          descricao: 'Faça o pedido pelo nosso cardápio digital. ' + contexto.empresa.descricao,
          imagem: urlRaiz + "/images/empresa/" + contexto.empresa.getLogoFull()
        }

      }

      if(empresa && empresa.ehMeuCardapio(req.get('host'))) {
        dados.pagseguroprod = Ambiente.Instance.producao
        dados.og.url = (new VariaveisDeRequest()).obtenhaUrlCardapio(contexto.empresa);

        if(contexto.empresa.pixelFacebook)
          dados.pixel = contexto.empresa.pixelFacebook

        if(contexto.empresa.analytics)
          dados.analytics = contexto.empresa.analytics

        if( !host.indexOf('meucardapio.ai' ) ) {
          dados.temPixel = false;
        } else {
          dados.temPixel = true;
        }

        me.renderizeALoja(req, res, dados);
      }
      else
        res.render('home.ejs', dados);
    });

    router.get('/admin/login', function(req, res, next) {
      if (req.isAuthenticated() ) {
        const usuario: any = req.user;

        if( usuario.garcom ) {
          res.redirect('/admin/pedidos-mesas');
          return;
        }
        res.redirect('/admin/index');
        return;
      }

      let contexto = require('domain').active.contexto;

      res.clearCookie('connect.sid');

      req.session.destroy( function(err) {
        res.render('home.ejs', {
          empresa: contexto.empresa,
          dados: JSON.stringify({}),
          producao: Ambiente.Instance.producao
        });
      });
    });


    router.get('/imprimir/*', function(req, res, next) {
      console.log('imprimir');
      let contexto = require('domain').active.contexto;

      res.render('home.ejs', {empresa: contexto.empresa, dados: JSON.stringify({}), producao: Ambiente.Instance.producao, bodyclass: 'imprimir' });
    });

    router.get('/admin/signup', function(req, res, next) {
      console.log('home');
      let dados: any = {
        dados: JSON.stringify({}),
        producao: Ambiente.Instance.producao
      }

      let contexto = require('domain').active.contexto;

      if(contexto.empresa) {
        dados.infoPagina = {
          descricao: "Cadastre-se no Admin da empresa " + contexto.empresa.nome,
          titulo: 'PromoKit - ' + contexto.empresa.nome + ' - Cadastre-se',
          idEmpresa: contexto.empresa.id
        }
      }

      res.render('home.ejs', {empresa: contexto.empresa, dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });


    router.get('/admin/index', isAuthenticated,  function(req, res, next) {
      const usuario: any = req.user;

      if( usuario.garcom ) {
        res.redirect('/admin/pedidos-mesas');
        return;
      }

      //seguir para a próxima rota
      next();
    });

    router.get('/admin/*', isAuthenticated,  function(req, res, next) {
      let dados: any = {
        dados: JSON.stringify({}),
        producao: Ambiente.Instance.producao
      }

      let contexto = require('domain').active.contexto;

      if(contexto.empresa) {
        dados.empresa = contexto.empresa;
        dados.infoPagina = {
          descricao: "PromoKit - Admin " + contexto.empresa.nome,
          titulo: 'PromoKit - Admin ' + contexto.empresa.nome,
          idEmpresa: contexto.empresa.id
        }
      }

      res.render('home.ejs', {empresa: contexto.empresa, dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    router.get('/confirmar/:token', function (req, res) {
      res.redirect('/cliente/ativar/' + req.params.token );
    });

    router.get('/cliente', async  (req: any, res) => {
      let dados: any = {
        dados: JSON.stringify({}),
        producao: Ambiente.Instance.producao
      }


      if(req.empresa) {
        let urlRaiz = (new VariaveisDeRequest()).obtenhaUrlRaiz(req.empresa);
        let prefixo = await new EmpresaService().obtenhaPrefixo(req);
        let descricao = String(`Cartão de fidelidade da empresa ${ req.empresa.nome}`);
        let titulo = String(`Cartão de Fidelidade${prefixo}`);

        dados.infoPagina = {
          descricao: descricao,
          titulo: titulo,
          idEmpresa: req.empresa.id
        }

        dados.og = {
          url: urlRaiz,
          descricao: descricao,
          titulo: titulo,
          imagem: urlRaiz + "/images/empresa/" + req.empresa.getLogoFull()
        }
      }

      res.render('home.ejs', dados);
    });

    router.get('/cliente/*', async function (req: any, res) {
      let dados: any = {
        dados: JSON.stringify({}),
        producao: Ambiente.Instance.producao
      }

      if(req.empresa) {
        let urlRaiz = (new VariaveisDeRequest()).obtenhaUrlRaiz(req.empresa);
        let prefixo = await new EmpresaService().obtenhaPrefixo(req);
        let descricao = String(`Cartão de fidelidade da empresa ${ req.empresa.nome}`);
        let titulo = String(`Cartão de Fidelidade${prefixo}`);

        dados.infoPagina = {
          descricao: descricao,
          titulo: titulo,
          idEmpresa: req.empresa.id
        }

        dados.og = {
          url: urlRaiz,
          descricao: descricao,
          titulo: titulo,
          imagem: urlRaiz + "/images/empresa/" + req.empresa.getLogoFull()
        }
      }

      res.render('home.ejs', dados);
    });
    router.get('/empresa', function (req, res) {
      res.render('home.ejs', {dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });
    router.get('/amp', function(req, res, next) {
      res.render('home_amp.ejs', {dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    router.get('/novo/pagamento/:guid', function (req, res) {
      res.render('landingpage/pagamento.ejs', {guid: req.params.guid, dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    router.get('/novo/cadastro', function (req, res) {
      let exibirTopo = true;


      if( req.query.topo === 'false' ) {
        exibirTopo = false;
      }

      res.render('landingpage/cadastro.ejs', {exibirTopo: exibirTopo, producao: Ambiente.Instance.producao});
    });

    router.get('/novo/plano/:guid', async (req, res) => {
      let planos = await PlanoEmpresarial.liste({publico: true})

      res.render('landingpage/plano.ejs', {planos: planos, guid: req.params.guid,
        dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    router.get('/novo/empresa/:guid', function (req, res) {
      const servidor = req.headers.host;

      res.render('landingpage/empresa.ejs', {guid: req.params.guid, servidor: servidor,
        dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    router.get('/lp/pedidos', function (req, res) {
      res.render('home.ejs', {dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    function obtenhaDadosContas(req: any) {
      const host = req.get('host');

      let codigoGoogleAds = '';
      let codigoAnalytics = '';

      if( host.indexOf('campinas') !== -1 ) {
        codigoGoogleAds = 'AW-1028673002';
        codigoAnalytics = '.';
      } else {
        codigoGoogleAds = 'AW-625433337'
        codigoAnalytics = 'AW-625433337';
      }

      return {
        codigoGoogleAds: codigoGoogleAds,
        codigoAnalytics: codigoAnalytics
      };
    }
    router.get('/lp/cardapio', function (req, res) {
      const host = req.get('host');

      const dados = obtenhaDadosContas(req);

      res.render('cardapio/index.ejs', {codigoGoogleAds: dados.codigoGoogleAds,
        codigoAnalytics: dados.codigoAnalytics, producao: Ambiente.Instance.producao});
    });

    router.get('/lp/cardapio/obrigado', function (req, res) {
      const host = req.get('host');

      const dados = obtenhaDadosContas(req);

      res.render('cardapio/obrigado.ejs', {codigoGoogleAds: dados.codigoGoogleAds,
        codigoAnalytics: dados.codigoAnalytics, producao: Ambiente.Instance.producao});
    });

    router.get('/lp/cardapio/obrigado1', function (req, res) {
      const host = req.get('host');

      const dados = obtenhaDadosContas(req);

      res.render('landingpage/obrigado.ejs', {codigoGoogleAds: dados.codigoGoogleAds,
        codigoAnalytics: dados.codigoAnalytics, producao: Ambiente.Instance.producao});
    });

    router.use('/qrcodes', isAuthenticated, CadQrCodeController);
    router.use('/empresas', isAuthenticated, EmpresasController);
    router.use('/disponibilidades', isAuthenticated, DisponibilidadeController);
    router.use('/catalogos', isAuthenticated, CatalogosController);
    router.use('/brindes', isAuthenticated, BrindesController);
    router.use('/cardapio', isAuthenticated, CardapioController);
    router.use('/cardapiopdf', CardapioPDFController);
    router.use('/produtos', isAuthenticated, ProdutosController);
    router.use('/estoque', isAuthenticated, EstoqueController);
    router.use('/notasFiscais', isAuthenticated, NotasFiscaisController);
    router.use('/nfce', isAuthenticated, NfceController);
    router.use('/fotos', isAuthenticated, FotosController);
    router.use('/contatos',  isAuthenticated, ContatosController);
    router.use('/atividades', isAuthenticated, AtividadesController);
    router.use('/mensagem', isAuthenticated, MensagensController);
    router.use('/banner', isAuthenticated, BannerController);
    router.use('/planos', isAuthenticated, PlanosController);
    router.use('/temas', isAuthenticated, TemaPersonalizadoController);
    router.use('/tiposDePontuacao', isAuthenticated, TiposDePontuacaoController);
    router.use('/cartoes2', CartoesSemLoginController);
    router.use('/cartoes', isAuthenticated, CartoesController);
    router.use('/cartao-cliente', isAuthenticated, CartaoClienteController);
    router.use('/formasDeEntrega', isAuthenticated, FormasDeEntregaController);
    router.use('/formasDePagamento', isAuthenticated, FormasDePagamentoController);
    router.use('/mesas', isAuthenticated, MesasController);
    router.use('/numerosWhatsapp', isAuthenticated, NumeroWhatsappController);
    router.use('/configuracoesMia', isAuthenticated, ConfiguracoesMiaController);
    router.use('/qrcode', isAuthenticated, QRCodeController);
    router.use('/rdcrm', RdcrmController);
    router.use('/crm/leads', isAuthenticated, LeadsController);
    router.use('/crm/empresas', isAuthenticated, CrmEmpresasController);
    router.use('/campanha', isAuthenticated, CampanhaController);
    router.use('/chatwoot', ChatwootController);
    router.use('/whatsapp', WhatsappController);
    router.use('/paleta-cores', PaletaCoresController);
    router.use('/campanha-rede', isAuthenticated, CampanhaRedeController);
    router.use('/instagram', InstagramController);
    router.use('/rede', RedeController);
    router.use('/chatgpt', ChatGPTController);
    router.use('/classificacao-contato', ClassificacaoContatoController);
    router.use('/chatbot', ChatBotController);
    router.use('/typebot-oauth', TypebotOauthController);
    router.use('/cloudwhatsapp', CloudWhatsappController);
    router.use('/api/v1/', passport.authenticate('bearer', { session: false }), determineUsuarioLogado, ApiV1Controller);
    router.use('/api', ApiController);
    router.use('/pagseguro', PagSeguroController);
    router.use('/erede', ERedeItauController);
    router.use('/cielo', CieloController);
    router.use('/pagarme', PagarmeController);
    router.use('/mercadopago', MercadoPagoController);
    router.use('/tunapay', TunaPayController);
    router.use('/importador',  ImportadorController);
    router.use('/importadorrede', ImportadorRedeController);
    router.use('/l', LinkController);
    router.use('/taxasDeEntregaCalculadas', TaxaDeEntregaCalculadaController);
    router.use('/mesa', LinkMesaController);
    router.use('/notificacoes', isAuthenticated, NotificacaoController);
    router.use('/entregadores', isAuthenticated, EntregadoresController);
    router.use('/templates', isAuthenticated, TemplateController);
    router.use('/templates-db', isAuthenticated, TemplatePromptDBController);
    router.use('/notificacaoapp', isAuthenticated, NotificacaoAppController);
    router.use('/integracao', isAuthenticated, IntegracaoController);
    router.use('/auth', AuthController);
    router.use('/link', AuthDiretoController);
    router.use('/usuario', UsuarioController);
    router.use('/registros-login', isAuthenticated, RegistrosDeLoginController);
    router.use('/bots2', Bots2Controller);
    router.use('/chamadas-ia', ChamadasIaController);
    router.use('/prompt-templates', isAuthenticated, TemplatePromptController);
    router.use('/prompt-templates-db', isAuthenticated, TemplatePromptDBController);
    router.use('/botpress', BotpressController);
    router.use('/tradutor-mensagem-bot', TradutorMensagemBotController);
    router.use('/upload', UploadController);
    router.use('/iugu', IuguController);
    router.use('/integracaoerp', IntegracaoERPController);
    router.use('/pagamentos', PagamentoController);
    router.use('/contratos', isAuthenticated, ContratosController);
    router.use('/contratos-ativacao/', ContratosAtivacaoController);
    //router.use('/whatsapp-flows', WhatsAppFlowsController);
    router.use('/recebimentos', RecebimentosController);
    router.use('/exportar', ExportadorController);
    router.use('/politica-privacidade', PoliticaDePrivacidadeController);
    router.use('/pedidos', PedidosController);
    router.use('/carrinho', CarrinhoController);
    router.use('/tela-produto', ProdutosController);
    router.use('/imagem', ImagemController);
    router.use('/caixa',  CaixaController);
    router.use('/comandas', ComandaController);
    router.use('/planosempresas', PlanosEmpresasController);
    router.use('/vantagens', VantagensController);
    router.use('/prospects', ProspectsController);
    router.use('/gruposDeLojas', isAuthenticated, GruposDeLojasController);
    router.use('/emails', EmailsController);
    router.use('/vendas', isAuthenticated, VendasController);
    router.use('/cupons', CuponsController);
    router.use('/vitrines', VitinesController);
    router.use('/avaliacoes', AvaliacaoDePedidosController);
    router.use('/sockets', SocketsController);
    router.use('/promocoes', PromocoesController);
    router.use('/hooks', HooksController);
    router.use('/apic', ApiChinaController);
    router.use('/api/gcom', passport.authenticate('bearer', { session: false }), determineUsuarioLogado, ApiGcomController);
    router.use('/opendelivery',  OpenDeliveryController);
    router.use('/opendelivery/order/v1',  OpenDeliveryApiOrderAppController);
    router.use('/opendelivery/hooks',  OpenDeliveryApiHooksAppController);
    router.use('/opendelivery/ss/v1',  OpenDeliveryMockPdvController);
    router.use('/opendelivery/lg/v1',  OpenDeliveryMockDeliveryController);

    router.use('/uberdirect',  UberDirectController);
    router.use('/ifood',  IfoodController);
    router.use('/uberdirect/hooks',  UberDirectApiHooksAppController);

    router.use('/geo', TomtomController);
    router.use('/grupolojas', GrupoLojasController);
    router.use('/countries', CountryCodesController);
    router.use('/fidelidade', isAuthenticated, FidelidadeController);
    router.use('/resposta-chatbot-instagram', isAuthenticated, RespostaChatbotInstagramController);
    router.use('/opcao-resposta-chatbot-instagram', isAuthenticated, OpcaoRespostaChatbotInstagramController);

    router.use('/cepscustomizados', CepsCustomizadosController);
    router.use('/tablets', TabletController);
    router.use('/modulos', ModulosController);

    router.use(express.static(path.join(__dirname, '../dist/')));
    router.use(express.static(path.join(__dirname, '../dist/pt')));
    router.use(express.static(__dirname + '/app'));
    router.use(favicon(path.join(__dirname, '../server/public', 'favicon.ico')));

    this.express.engine('html', require('ejs').renderFile);
    this.express.set('view engine', 'html');
    this.express.set('views', path.join(__dirname, 'views'));

    router.get('/:empresa', function (req, res, next) {
      const paramEmpresa = req.params.empresa;
      if( paramEmpresa.indexOf(".") !== -1 || paramEmpresa === 'empresas' || paramEmpresa === 'lojas' ) {
        return next();
      }

      if(req.get('host').indexOf('promokit.com.br') < 0)
        return next();

      const nomeEmpresa = req.params.empresa;

      const urlRedirect = 'https://' + nomeEmpresa + '.promokit.com.br/cliente';

      let empresaDaRequest: string = req.get('host').split('.') [0];

      if((empresaDaRequest.indexOf('localhost') >= 0) || (empresaDaRequest.indexOf('192') >= 0) ||
        (empresaDaRequest.indexOf('ngrok.io') >= 0) || (empresaDaRequest.indexOf('serveo.net') >= 0)) {
        return res.redirect('localhost:3100/cliente');
      }

      res.redirect(urlRedirect);
    });

    router.get('/crm/*', isAuthenticated,  function(req, res, next) {
      let contexto = require('domain').active.contexto;

      res.render('home.ejs', {empresa: contexto.empresa, dados: JSON.stringify({}), producao: Ambiente.Instance.producao});
    });

    router.use( (req, res, next) => {
      if( req.originalUrl.indexOf('.woff') >= 0) return next();

      //console.log('404->' + req.originalUrl)
      res.render('404.ejs', {});
    });

    this.express.use('/', router);
  }

  renderizeALoja(req: any, res: any, dados: any)
  {
    const userAgent = req.get('User-Agent');
    const origem = req.query.origem;

    if ((userAgent && userAgent.includes('MeuAppFlutter')) || (origem && origem.toUpperCase() === 'FLUTTER')) {
      dados.flutter = true;
    } else {
      dados.flutter = false;
    }

    // Aplicar o tema da URL, sessão ou empresa
    const temaUrl = req.query.tema;

    console.log('tema: ' + temaUrl);

    const temaSessao = req.session.tema;

    if (temaUrl) {
      // Se o tema está na URL, salve-o na sessão
      req.session.tema = temaUrl;
      dados.tema = temaUrl;
    } else if (temaSessao) {
      // Se não há tema na URL, mas há na sessão, use o da sessão
      dados.tema = temaSessao;
    } else if (dados.empresa && dados.empresa.tema) {
      // Se não há tema na URL nem na sessão, use o da empresa
      dados.tema = dados.empresa.tema;
    }

    if (dados.tema) {
      dados.empresa = dados.empresa || {};
      dados.empresa.tema = dados.tema;
    }

    console.log('renderizando a loja: ' + req.originalUrl + ' tema: ' + dados?.empresa?.tema);

    res.render('loja/loja.ejs', dados);
  }

  private trateLinkDeFranquia(req: any, res: any, next: any) {
    let dados: any = {
      dados: JSON.stringify({}),
      producao: Ambiente.Instance.producao,
    }

    let contexto = require('domain').active.contexto;
    dados.bodyclass = 'scroll'
    dados.pastaScripts = '/_loja/'
    dados.empresa = {};



    if( req.grupoDeLojas && req.grupoDeLojas.id === 3 ) {
      dados.pixel = '463849371313616';
      // pixel flying
    }

    let host = req.get('host');

    if( host.indexOf(":") !== -1 ) {
      host = host.split(":")[0];
    }

    let urlRaiz = url.format({
      protocol: req.protocol,
      host: host,
      pathname: req.originalUrl
    });

    const grupoDeLojas: GrupoDeLojas = req.grupoDeLojas;

    if(grupoDeLojas) {
      dados.infoPagina = {
        descricao: grupoDeLojas.descricao,
        titulo: grupoDeLojas.nome,
        idEmpresa: -1
      }
    }

    dados.og = {
      url: urlRaiz,
      titulo: 'Cardápio Online - ' + grupoDeLojas.nome,
      descricao: grupoDeLojas.descricao,
      imagem: urlRaiz.replace('lojas', '') + "images/empresa/" + grupoDeLojas.logo
    };

    if( grupoDeLojas.codigoGtm ) {
      dados.empresa = {
        gtm: grupoDeLojas.codigoGtm
      };
    }

    if(grupoDeLojas.analytics)
      dados.analytics = grupoDeLojas.analytics;

    if( grupoDeLojas.pixelFacebook ) {
      dados.pixel = grupoDeLojas.pixelFacebook;
    }

    dados.pagseguroprod = Ambiente.Instance.producao
    dados.agregador = true;
    dados.telaMultiLoja = grupoDeLojas.telaMultiLoja;
    dados.favicon = grupoDeLojas.favicon;
    dados.multipedido = grupoDeLojas.telaMultiLoja && grupoDeLojas.multipedido;

    res.render('loja/loja_franquia.ejs', dados);
  }

  private trateLinkDeLoja(req: any, res: any, next: any) {
    let dados: any = {
      dados: JSON.stringify({}),
      producao: Ambiente.Instance.producao,
    }

    let contexto = require('domain').active.contexto;

    dados.bodyclass = 'scroll'
    dados.pastaScripts = '/_loja/'

    let urlRaiz = (new VariaveisDeRequest()).obtenhaUrlCardapio(contexto.empresa);

    if(contexto.empresa) {
      dados.infoPagina = {
        descricao: "Cardápio Online - " + contexto.empresa.nome + " - "  + contexto.empresa.descricao,
        titulo: 'Cardápio Online - ' + contexto.empresa.nome,
        idEmpresa: contexto.empresa.id
      }
      dados.empresa = contexto.empresa;

      dados.og = {
        url: urlRaiz,
        titulo: 'Cardápio Online - ' + contexto.empresa.nome,
        descricao: contexto.empresa.descricao,
        imagem: urlRaiz + "/images/empresa/" + contexto.empresa.getLogoFull()
      }

      dados.pagseguroprod = Ambiente.Instance.producao
      if(contexto.empresa.pixelFacebook)
        dados.pixel = contexto.empresa.pixelFacebook

      if(contexto.empresa.analytics)
        dados.analytics = contexto.empresa.analytics
    }

    const host = req.get('host');
    if( host.indexOf('meucardapio.ai' ) !== -1 || host.indexOf('localhost') !== -1 ||
      host.indexOf("ngrok.io") !== -1 || host.indexOf("trycloudflare") !== -1 ) {
      dados.temPixel = false;
    } else {
      dados.temPixel = true;
    }

    if(req.query.c && !Number.isNaN(Number(req.query.c))){
      new MapeadorDeProduto(dados.empresa.catalogo).selecioneSync({id: Number(req.query.c)}).then((produto: Produto) => {
        if(produto && produto.imagens)
          dados.og.imagem = String(`${urlRaiz}/images/empresa/${ produto.imagens[0].linkImagem}`)

        this.renderizeALoja(req, res, dados);
      });

    } else {
      this.renderizeALoja(req, res, dados);
    }
  }


  private carregueEmpresaLogada(empresaDaRequest: any, cb: any) {
    new MapeadorDeEmpresa().selecioneCachePorDominio(empresaDaRequest).then( (empresa => {
      cb(empresa);
    }))
  }


  private configurePassport() {
    const configPassport = new ConfigPassport();

    configPassport.configure();
    this.express.use(configPassport.passport.initialize());
    this.express.use(configPassport.passport.session());
  }

  private configureServicosSefaz() {
    CarregadorDeWebserviceSefaz.carregueWebservices()
  }
}

export default new App().express;
