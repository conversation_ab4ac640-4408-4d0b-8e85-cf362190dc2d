<html>
<head>
    <link href="/assets/template/css/bootstrap.min.css" rel="stylesheet"/>
</head>
<style>
    .page{
        display: table;
        width: 500px;
        margin: 0 auto;

        padding: 20px;
        margin-top: 20px;
    }

    .font-15{
        font-size: 15px;
    }

    body {
        width: 75mm;

    }

    .separador{
        width: 100%;
        border-top: 1px dashed;
        padding: 0px;
        margin:0px;
        position: relative;
        display: table;
        line-height: 10px;
    }

    .linha{
        display: table;width: 100%;
    }


    .font-11{
        font-size: 11px !important;
    }


    .paper {
        width: 75mm;
        display: block;

        background: #fff;
        border: 1px dashed #ccc;

        padding: 15px;

        font-weight: 700;
    }

    label {
        font-weight: 700;
    }

    p, span, label,div{
        color: #000 !important;

        font-family: "lucida <PERSON>", Arial, Verdana !important;;
    }

    p{
        color:black;
        font-weight: bold;
        margin-bottom: 0;
    }

    .row:not(:last-child){
        margin-bottom: 15px;
    }


    .titulo{
        text-align: center;
    }

    .row.itens div{
        padding-top: 2px;
        padding-bottom:0px;
        margin-bottom: 0px !important;
    }



    .itens .item >div{
        padding-left: 10px !important;
        padding-right: 10px !important;;
    }



    .assinatura {
        margin-top: 20px;
    }

    .assinatura     .campo{
        width: 80%;
        margin: 0 auto;
        padding-bottom: 10px;
    }

    .assinatura label{
        display: block;
    }
    .assinatura span{
        font-size: 11px;
    }

    .text-muted {
        color: #000 !important;
    }

    .forte {
        font-weight: bolder;


    }

    h1, h2, h3, h4, h5, h6 {
        margin: 5px 0;
    }

    h3 {
        margin: 0px
    }

    label {
        margin-bottom: 0;
        font-size: 15px;
    }

</style>
<body>
<div class="paper">
    <div class="titulo">
        <!--<img class="imagem_empresa" src="https://promokit.com.br/images/empresa/<%= resumoVendas.empresa.logo%>" style="    width: 65px;"/>-->
        <h3 class="empresa"><b><%= resumoVendas.empresa.nome%></b></h3>
        <h4>Relatorio Financeiro</h4>

    </div>
    <div class="dados mt-3 ">
        <% if( resumoVendas.dataInicial ) { %>
        <div class="row">
            <div class="col-12">
                <span>Data Inicial: <%= resumoVendas.dataInicial%></span><br>
                <span>Data Final: <%= resumoVendas.dataFinal%></span><br>
            </div>
        </div>
        <% } %>

    </div>
    <div class="row">
        <div class="col-12">
            <label class="text-center font-weight-bold" style="display: block" >
                Resumo Geral

            </label>
        </div>
        <div class="col-12">
            <span>Qtde de Pedidos: <%= resumoVendas.qtde%></span><br>
            <span>Total de Vendas: <%= formatador.numeroParaCurrency(resumoVendas.totalComTaxa) %></span><br>
            <span>Total Taxa de Entrega: <%= formatador.numeroParaCurrency(resumoVendas.taxaDeEntrega) %></span><br>
            <span>Total Produtos: <%= formatador.numeroParaCurrency(resumoVendas.total )%></span><br>
        </div>
    </div>
    <div class="row pt-2 itens">
        <div class="col-12">
            <label class="text-center font-weight-bold" style="display: block" >
                Resumo por Forma de Pagamento

            </label>

            <div class="row">
                <div class="font-italic col-6"><label><b>Nome</b></label></div>
                <div class="col-3 font-italic" ><label><b>Qtde.</b></label></div>
                <div class="col-3 font-italic" ><label><b>Total</b></label></div>
            </div>
            <% for(var i = 0; i < resumoVendas.formasDePagamento.length; i++) { %>
                <div class="row item">
                    <div class="col-7"><%= resumoVendas.formasDePagamento[i].nome %>
                    </div>
                    <div class="col-2  "><%= resumoVendas.formasDePagamento[i].qtde%></div>
                        <div class="col-3 " >
                            <span class="float-right"><%= formatador.numeroParaCurrency(resumoVendas.formasDePagamento[i].total)%></span>
                        </div>
                </div>
            <% } %>

        </div>


    </div>
    <div class="row pt-2 itens">
        <div class="col-12">
            <label class="text-center font-weight-bold" style="display: block" >
                Resumo por Forma de Entrega

            </label>

            <div class="row">
                <div class="font-italic col-6"><label><b>Nome</b></label></div>
                <div class="col-3 font-italic" ><label><b>Qtde.</b></label></div>
                <div class="col-3 font-italic" ><label><b>Total</b></label></div>
            </div>

            <% for(var j = 0; j < resumoVendas.formasDeEntrega.length; j++) { %>
                <div class="row item">
                    <div class="col-7"><%= resumoVendas.formasDeEntrega[j].nome === 'undefined' ? 'Outros' : resumoVendas.formasDeEntrega[j].nome%>
                    </div>
                    <div class="col-2  "><%= resumoVendas.formasDeEntrega[j].qtde%></div>
                        <div class="col-3 " >
                            <span class="float-right"><%= formatador.numeroParaCurrency(resumoVendas.formasDeEntrega[j].total) %></span>
                        </div>
                </div>
            <% } %>
        </div>
    </div>
    <div class="row pt-2 itens">
        <div class="col-12">
            <label class="text-center font-weight-bold" style="display: block" >
                Resumo por Cupom

            </label>

            <div class="row">
                <div class="font-italic col-6"><label><b>Nome</b></label></div>
                <div class="col-3 font-italic" ><label><b>Qtde.</b></label></div>
                <div class="col-3 font-italic" ><label><b>Total</b></label></div>
            </div>
            <% for(var l = 0; l < resumoVendas.cupons.length; l++) { %>
                <div class="row item">
                    <div class="col-7"><%= resumoVendas.cupons[l].nome%>
                    </div>
                    <div class="col-2  "><%= resumoVendas.cupons[l].qtde %></div>
                        <div class="col-3 " >
                            <span class="float-right"><%= formatador.numeroParaCurrency(resumoVendas.cupons[l].total) %></span>
                        </div>
                </div>
            <% } %>
        </div>
    </div>

</div>
</body>
</html>
