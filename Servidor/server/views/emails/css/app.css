body{
  background-color: #f7f8f8;
  font-family: 'Trebuchet MS',Arial,Helvetica,sans-serif;
  padding-top: 50px;
  padding-bottom: 150px;
}

.panel{
  background-color: #e8eaf4;
  -webkit-box-shadow: 0px 10px 20px 0px #aaa;
  box-shadow: 0px 10px 20px 0px #aaa;
}

.container{
  width: 550px;
  margin:0 auto;
  position: relative;

}


.coluna-body{
  width: 470px;
  background: #fff;
}
.fundo-tabela-branco{
  height:60px;
  width:50px;
  background-color: #fff;
}


.panel .panel-body{
  position: relative;
  border: 1px solid #f7f7f7;
  background: #fcfcfc;
  margin: 0 auto;
  position: relative;
  padding-bottom: 50px;

  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  -moz-border-radius-bottomleft: 0;
  -moz-border-radius-bottomright: 0;
  -webkit-border-bottom-left-radius: 0;;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-bottomleft: 0;
  -moz-border-radius-bottomright:  0;
}

.panel .panel-heading{
  text-align: center;
}

.header-promokit{
  height: 50px;
}

.header-promokit img{
  height: 30px;
  float: left;
  margin-top: 10px;
  margin-left: 10px;
}

.header-loja{
  height: 90px;
  padding-top: 30px;
  background: #fff;
}

.bloco-empresa{
  width: 350px;
  margin: 0 auto;
}

.destaque{
  font-weight: 700;
}

.icone{
  display: block;
}

.text-left{
  text-align: left;
}


.table{
  display: table;
  width: 100%;
}

.font-25{
  font-size: 25px;
}

.font-11{
  font-size: 11px;
}

.font-18{
  font-size: 18px;
}


.icone-fone{
  background: url('https://meubilhete.com/images/email/icone_fone.png')  no-repeat;
  width: 16px;
  height: 16px;
}

.icone-mail{
  background: url('https://meubilhete.com/images/email/icone_mail.png')  no-repeat;
  width: 16px;
  height: 16px;

}

.panel .separador{
  height: 9px;
  background: url("https://meubilhete.com/images/email/separador.png") repeat-x;
}


.bloco-imagem{
  display: inline-block;
  width: 155px;

}

.redes-socias .link{
  float: left;
  text-decoration: none;

}
.redes-socias p{
  color: #575151;
  font-size: 12px;
}


.table .row{
  padding: 15px;
  display: table-cell;
}

.table.social{
  color: #fff
}

.botao{
  color: #fff;
  padding: 10px 20px;
  display: inline-block;
  font-weight: 400;
  line-height: 1.25;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: 1px solid transparent;
  padding: .5rem 1rem;
  font-size: 1rem;
  border-radius: .25rem;
  text-decoration: none;
}



.panel .linha{
  border-top: 1px solid #ccc;
  margin: 0px -15px ;
}

.panel .bloco{
  padding: 15px 35px;
}

.panel .caixa{
  border: 2px solid #ccc;
  padding: 15px;
  border-radius: 20px;
  font-size: 16px;
}


.abreviar {
  text-overflow: ellipsis;
  white-space: pre-line;
  overflow: hidden;
}

.pull-left{
  float: left;
}

table.assinatura tr td{
  border: 2px dashed #ccc;
}

table,th,td
{
  padding: 10px;
  border-collapse:collapse;
}

table.layout{
  padding:0;
  margin:0;
  width: 100%;
}
table.layout td{
  padding:0;
  margin:0;
  vertical-align: top;
}


footer{
  color: #000000b8;

}
footer >div{
  padding-top: 80px;
}

footer a{
  color: #000000b8;
  text-decoration: none;
  text-align: center;
  display: block;
}

footer img{
  height: 25px;
  text-align: center;
  margin: auto;
  display: block;
  margin-bottom: 10px;
}

.fundo-azul{
  background-color: #3a44b9;
}

.codigo-vericacao{
  font-size: 20px;
  font-weight: bold;
}

p{
  margin: 0 ;
  margin-bottom: 12px;
  font-size: 14px;
  font-family: 'Trebuchet MS',Arial,Helvetica,sans-serif;
  color: #333;
}

a{
  font-family: 'Trebuchet MS',Arial,Helvetica,sans-serif;
  font-size: 14px;
  text-decoration: none;
  color: #333;
}

.link-recuracao{
  display: inline-block;
  color: #777;
  font-family: sans-serif;
  font-weight: bold;
  line-height: 120%;
  margin: 0;
  text-decoration: none;
  text-transform: uppercase;
  border-radius: 3px;
  font-size: 12px;
}
