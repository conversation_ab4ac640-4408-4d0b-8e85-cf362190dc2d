<!DOCTYPE html>
<html lang="pt">
<head>

    <% if (!locals.flutter) { %>
        <% include tags_google %>
    <% } %>

  <meta charset="utf-8" />


  <base href="/"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">

  <link rel="apple-touch-icon" sizes="192x192" href="/assets/icons/icon-192x192.png">

<% if (!locals.flutter) { %>
    <% include ./../metatags %>
<% } %>


  <style>
    * {
      touch-action: manipulation;
    }

    .loader {
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -60px;
      margin-left: -60px;
      border: 16px solid #f3f3f3;
      border-radius: 50%;
      border-top: 16px solid #3498db;
      width: 120px;
      height: 120px;
      -webkit-animation: spin 2s linear infinite; /* Safari */
      animation: spin 2s linear infinite;
    }

    /* Safari */
    @-webkit-keyframes spin {
      0% { -webkit-transform: rotate(0deg); }
      100% { -webkit-transform: rotate(360deg); }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>

  <!-- App favicon -->
    <% if( typeof(empresa) !== 'undefined' && empresa.favicon ) { %>
    <link rel="shortcut icon" href="/images/empresa/<%=empresa.favicon%>">
    <% } else { %>
        <link rel="shortcut icon" href="/assets/template/images/favicon.ico">
    <% } %>

    <!--
  <link rel="manifest" href="/assets/lojamanifest.webmanifest?v=4">
  -->
  <link href="https://fonts.googleapis.com/css?family=Poppins&display=swap" rel="stylesheet">

  <% include css %>

  <% if (!locals.flutter) { %>
    <% include pixel-facebook %>
  <% } %>
</head>

<body class=" <%=locals.bodyclass%>">

<% include body-tags-google %>
<app-root>
  <div class="loader"></div>
</app-root>

<% include scripts %></body>


<script type="text/javascript">
  var versao = '<%=global.versao%>';
  var analytics = '<%=locals.analytics%>';
  var agregador = <%=locals.agregador ? true : false%>;
  window['pagseguroprod'] = <%=locals.pagseguroprod ? true : false%>;

  // Adiciona listener para receber mensagens do iframe pai
  window.addEventListener('message', function(event) {
    // Verifica se a mensagem é do tipo esperado
    if (event.data && event.data.tipo === 'atualizacao-tema') {
      // Atualiza as variáveis CSS com os novos valores
      const valores = event.data.todosValores;
      if (valores) {
        document.documentElement.style.setProperty('--cor-fundo', valores.corFundo || '#f7f8f8');
        document.documentElement.style.setProperty('--cor-texto', valores.corTextoSecundaria || 'inherit');
        document.documentElement.style.setProperty('--cor-texto-topo', valores.corTextoTopo || 'inherit');
        document.documentElement.style.setProperty('--cor-texto-primaria', valores.corTextoPrimaria || 'inherit');
        document.documentElement.style.setProperty('--cor-texto-secundaria', valores.corTextoSecundaria || 'inherit');
        document.documentElement.style.setProperty('--cor-texto-muted', valores.corTexto ? `color-mix(in srgb, ${valores.corTexto} 70%, black)` : '#6c757d');
        document.documentElement.style.setProperty('--cor-botao', valores.corBotao || '#007bff');
        document.documentElement.style.setProperty('--cor-texto-botao', valores.corTextoBotao || '#007bff');
        document.documentElement.style.setProperty('--cor-preco-adicional', valores.corPrecoAdicional || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-texto-preco-adicional', valores.corTextoPrecoAdicional || '#FFFFFF');
        document.documentElement.style.setProperty('--cor-fundo-site', valores.corFundoDoSite || '#f7f8f8');
        document.documentElement.style.setProperty('--cor-fundo-elementos', valores.corFundoElementos || '#ffffff');
        document.documentElement.style.setProperty('--cor-borda', valores.corBorda || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-destaque', valores.corDestaque || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-hover', valores.corHover || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-preco', valores.corPreco || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-radio', valores.corBotao || '#007bff');
        document.documentElement.style.setProperty('--cor-texto-rodape', valores.corTextoRodape || '#007bff');
        document.documentElement.style.setProperty('--cor-fundo-rodape', valores.corFundoRodape || '#f7f8f8');
        document.documentElement.style.setProperty('--cor-borda-rodape', valores.corBordaRodape || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-item-ativo-rodape', valores.corItemAtivoRodape || valores.corBotao || '#007bff');
      }
    }
  });
</script>


<style>
  :root {
    <% if (typeof empresa !== 'undefined' && empresa.temaPersonalizado) { %>
      --cor-fundo: <%= empresa.temaPersonalizado.corFundo || '#f7f8f8' %>;
      --cor-texto: <%= empresa.temaPersonalizado.corTextoSecundaria || 'inherit' %>;
      --cor-texto-topo: <%= empresa.temaPersonalizado.corTextoTopo || 'inherit' %>;
      --cor-texto-primaria: <%= empresa.temaPersonalizado.corTextoPrimaria || 'inherit' %>;
      --cor-texto-secundaria: <%= empresa.temaPersonalizado.corTextoSecundaria || 'inherit' %>;
      --cor-texto-muted: <%= empresa.temaPersonalizado.corTexto ? `color-mix(in srgb, ${empresa.temaPersonalizado.corTexto} 70%, black)` : '#6c757d' %>;
      --cor-botao: <%= empresa.temaPersonalizado.corBotao || '#007bff' %>;
      --cor-texto-botao: <%= empresa.temaPersonalizado.corTextoBotao || '#007bff' %>;
      --cor-preco-adicional: <%= empresa.temaPersonalizado.corPrecoAdicional || '#e2e3e3' %>;
      --cor-texto-preco-adicional: <%= empresa.temaPersonalizado.corTextoPrecoAdicional || '#FFFFFF' %>;
      --cor-fundo-site: <%= empresa.temaPersonalizado.corFundoDoSite || '#f7f8f8' %>;
      --cor-fundo-elementos: <%= empresa.temaPersonalizado.corFundoElementos || '#ffffff' %>;
      --cor-borda: <%= empresa.temaPersonalizado.corBorda || '#e2e3e3' %>;
      --cor-destaque: <%= empresa.temaPersonalizado.corDestaque || '#e2e3e3' %>;
      --cor-hover: <%= empresa.temaPersonalizado.corHover || '#e2e3e3' %>;
      --cor-preco: <%= empresa.temaPersonalizado.corPreco || '#e2e3e3' %>;
      --cor-radio: <%= empresa.temaPersonalizado.corBotao || '#007bff' %>;
      --cor-texto-rodape: <%= empresa.temaPersonalizado.corTextoRodape || '#007bff' %>;
      --cor-fundo-rodape: <%= empresa.temaPersonalizado.corFundoRodape || '#f7f8f8' %>;
      --cor-borda-rodape: <%= empresa.temaPersonalizado.corBordaRodape || '#e2e3e3' %>;
      --cor-item-ativo-rodape: <%= empresa.temaPersonalizado.corItemAtivoRodape || empresa.temaPersonalizado.corBotao || '#007bff' %>;
    <% } %>
  }
</style>

</html>
