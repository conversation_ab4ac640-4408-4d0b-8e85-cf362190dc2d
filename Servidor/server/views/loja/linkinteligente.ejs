<!DOCTYPE html>
<html lang="pt">
<head>
  <!-- Google Analytics -->
  <script>
    window.ga=window.ga||function(){(ga.q=ga.q||[]).push(arguments)};ga.l=+new Date;
    ga('create', 'UA-163170335-2', 'auto');
    <%if (locals.analytics) { %>
    ga('create', '<%=locals.analytics%>', {name: 'custom'});
    <% } %>
    ga('send', 'pageview');
    <%if (locals.analytics) { %>
    ga('custom.send', 'pageview');
    <% } %>
  </script>
  <script async src='https://www.google-analytics.com/analytics.js'></script>  <!-- End Google Analytics -->
  <!-- Google Tag Manager -->
  <!--
  <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-TNJ69BG');</script>
    -->
  <!-- End Google Tag Manager -->
  <meta charset="utf-8" />


  <base href="/"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">

  <link rel="apple-touch-icon" sizes="192x192" href="/assets/icons/icon-192x192.png">
  <link rel="canonical" href="<%= urlLoja %>" />

  <% include ./../metatags %>

  <style>
    * {
      touch-action: manipulation;
    }

    .loader {
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -60px;
      margin-left: -60px;
      border: 16px solid #f3f3f3;
      border-radius: 50%;
      border-top: 16px solid #3498db;
      width: 120px;
      height: 120px;
      -webkit-animation: spin 2s linear infinite; /* Safari */
      animation: spin 2s linear infinite;
    }

    /* Safari */
    @-webkit-keyframes spin {
      0% { -webkit-transform: rotate(0deg); }
      100% { -webkit-transform: rotate(360deg); }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>

  <!-- App favicon -->
  <link rel="shortcut icon" href="/assets/template/images/favicon.ico">
  <link href="https://fonts.googleapis.com/css?family=Poppins&display=swap" rel="stylesheet">


  <% include css %>
  <!-- Facebook Pixel Code -->
  <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '723487391545650');
    <%if (locals.pixel) { %>
      fbq('init', '<%=locals.pixel%>');
    <% } %>
    fbq('track', 'PageView');
  </script>
  <noscript>
    <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=723487391545650&ev=PageView&noscript=1"/>
    <%if (locals.pixel) { %>
      <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=<%=locals.pixel%>&ev=PageView&noscript=1"/>
    <% } %>
  </noscript>
  <!-- End Facebook Pixel Code -->
</head>

<body class=" <%=locals.bodyclass%>">
<!-- Google Tag Manager (noscript) -->
<!--
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TNJ69BG"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>-->
<!-- End Google Tag Manager (noscript) -->

<app-root>
  <div class="loader"></div>
</app-root>

<script type="application/javascript">
  var pedidoAtual = {
    itens: [],
    timestamp: new Date().getTime()
  };

  <%if (locals.formaDeEntrega) { %>
    pedidoAtual.entrega = {
      permiteEntrega: true,
      formaDeEntrega: "<%-formaDeEntrega%>",
      endereco: JSON.parse('<%-enderecoSessao%>')
    };

    pedidoAtual.entrega.gps = true;

    pedidoAtual.pagamento = {
      formaDePagamento: {
        id: 387
      },
      tipoDePagamento: "offline"
    };
    localStorage._pedido_ = JSON.stringify(pedidoAtual);
  <% } %>

  window.location.href = "<%-urlLoja%>";
</script>
</body>
</html>
