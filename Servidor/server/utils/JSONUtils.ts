export class JSONUtils {
  static removaNulls(obj: any, seen = new Set()) {
    if (obj === null || typeof obj !== 'object') return;

    if (seen.has(obj)) return;
    seen.add(obj);

    // tslint:disable-next-line:forin
    for (let key in obj) {
      if (key === 'dadosJson') {
        delete obj[key];
        continue;
      }

      const value = obj[key];

      if (value === null) {
        delete obj[key];
      } else if (Array.isArray(value)) {
        value.forEach((item: any) => {
          JSONUtils.removaNulls(item, seen);
        });
      } else if (typeof value === 'object') {
        JSONUtils.removaNulls(value, seen);
      }
    }
  }
}
