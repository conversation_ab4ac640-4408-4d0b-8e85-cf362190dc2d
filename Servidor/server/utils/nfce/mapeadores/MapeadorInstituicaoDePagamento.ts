import {MapeadorBasico} from "../../../mapeadores/MapeadorBasico";
import {EnumMeioDePagamento} from "../../../domain/delivery/EnumMeioDePagamento";
import {InstituicaoDePagamento} from "../../../domain/nfce/InstituicaoDePagamento";

export class MapeadorInstituicaoDePagamento extends MapeadorBasico {
    constructor() {
        super('instituicaoDePagamento')
    }

    selecionePorMeio(meioDePagamento: EnumMeioDePagamento): Promise<InstituicaoDePagamento> {
      return this.selecioneSync({meioDePagamento})
    }
}
