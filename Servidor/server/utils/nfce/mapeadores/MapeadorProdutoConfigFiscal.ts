import {MapeadorBasico} from "../../../mapeadores/MapeadorBasico";

export class MapeadorProdutoConfigFiscal extends MapeadorBasico {
    constructor() {
        super('produtoConfigFiscal',  false);
    }

    selecionePorIdProduto(produtoId: any) {
        return this.selecioneSync({produtoId: produtoId});
    }

  persista(configuracoesNotaFiscal: any) {
    if(!configuracoesNotaFiscal) return Promise.reject('Configurações não informadas')

    if(!configuracoesNotaFiscal.id) {
      return this.insiraSync(configuracoesNotaFiscal)
    }

    return this.atualizeSync(configuracoesNotaFiscal)
  }
}
