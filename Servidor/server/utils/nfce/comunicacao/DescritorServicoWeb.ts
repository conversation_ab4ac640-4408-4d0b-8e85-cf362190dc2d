import { Operacao } from "./Operacao";
import {Certificado} from "../../../domain/nfce/Certificado";

export class DescritorServicoWeb {
  url: string;
  nome: string;
  operacoes: Operacao[] = [];
  certificado: Certificado;

  constructor() {
    this.operacoes = [];
  }

  static comEndereco(enderecoDoServico: string): DescritorServicoWeb {
    let servico = new DescritorServicoWeb();
    servico.url = enderecoDoServico;
    return servico;
  }

  eNome(nomeDoServico: string): DescritorServicoWeb {
    this.nome = nomeDoServico;
    return this;
  }

  prepareOperacao(nomeDaOperacao: string): Operacao {
    let operacao = new Operacao();
    operacao.nome = nomeDaOperacao;
    operacao.servico = this;
    return operacao;
  }

  adicione(operacao: Operacao): void {
    this.operacoes.push(operacao);
  }

  eCertificado(certificado: Certificado): DescritorServicoWeb {
    this.certificado = certificado;
    return this;
  }
}
