import {Operacao} from "./Operacao";
import {ValorDoParametro} from "./ValorDoParametro";
import fetch from 'node-fetch'
import * as https from 'https';
import {XMLBuilder,  XMLParser} from 'fast-xml-parser';
import {XmlUtils} from "./XmlUtils";

export class InvocadorSoapNfce {
  private endereco: string;

  constructor(private operacao: Operacao) {
    //this.endereco = endereco;
  }

  invoqueWebService(): Promise<any> {
    return new Promise(async (resolve, reject): Promise<any> => {
      try {
        let documentoCorpo: string = this.operacao.mensagem.valor.toString();
        let documentoHeader: string = this.operacao.header ? this.operacao.header.valor.toString() : "";

        let mensagem: any = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">`;
              /*
               <?xml version="1.0" encoding="utf-8"?>
<soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
  <soap12:Body>
    <nfeDadosMsg xmlns="http://www.portalfiscal.inf.br/nfe/wsdl/NFeAutorizacao4">xml</nfeDadosMsg>
  </soap12:Body>
</soap12:Envelope>
 */


        mensagem += `<soap12:Body><${this.operacao.mensagem.parametro.nome} xmlns="${"http://www.portalfiscal.inf.br/nfe/wsdl/" + this.operacao.servico.nome}">${documentoCorpo}</${this.operacao.mensagem.parametro.nome}></soap12:Body></soap12:Envelope>`;


        const options: any = {
          method: 'POST',
          headers: {
            "Content-Type": "application/soap+xml; charset=utf-8", // "application/soap+xml; charset=utf-8",
            "soapAction": this.operacao.servico.url + "/" + this.operacao.servico.nome //+ "/"  + this.operacao.nome
          },
          agent: new https.Agent({
            rejectUnauthorized: false,
             pfx:  require('fs').readFileSync(this.operacao.servico.certificado.caminhoCertificadoCompleto()) ,
            passphrase: await this.operacao.servico.certificado.getSenha(),
            secureProtocol: "TLSv1_2_method"
          }),
          body: mensagem,
        }

        const res = await fetch(this.operacao.servico.url, options);
        const status = res.status
        const xmlRetorno = await res.text();

        let xmlOptions = {
          attributeNamePrefix : '',
          attrNodeName: 'attr',
          ignoreNameSpace: true,
          ignoreAttributes: false,
          tagValueProcessor: (tagName: any, tagValue: any, jPath: any, hasAttributes: any, isLeafNode: any) => {
            if(tagName === 'chNFe') return null
            return tagValue;
          },
          removeNSPrefix: true

        };

        const parser = new XMLParser(xmlOptions);
        const objetoXml = parser.parse(xmlRetorno);

        const retorno = new XMLBuilder().build(objetoXml['Envelope']['Body']['nfeResultMsg'])


        resolve({
          status: status,
          xmlRetorno: retorno
        })
      } catch (error) {
        reject(error);
      }
    })
  }

  adicioneValor(valor: ValorDoParametro) {
    throw new Error("Method not implemented.")
  }
}
