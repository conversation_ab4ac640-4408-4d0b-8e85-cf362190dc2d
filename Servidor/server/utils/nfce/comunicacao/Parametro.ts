import {ValorDoParametro} from "./ValorDoParametro";

export class Parametro {
  nome: string;
  tipo: any;
  tipoXml: any;

  static nome(nomeDoParametro: string): Parametro {
    let parametro = new Parametro();

    parametro.nome = nomeDoParametro;

    return parametro;
  }

  setTipo(tipo: any) {
    // if(tipo === String)
    this.tipoXml = 'XMLType.XSD_STRING';
    // else if (tipo === Number)
    // this.tipoXml = 'XMLType.XSD_INT';

    this.tipo = tipo;
  }


  valor(valorRecebido: any) {
    this.tipo = typeof valorRecebido;

    let valor = new ValorDoParametro(this);
    valor.valor = valorRecebido;

    return valor;
  }
}
