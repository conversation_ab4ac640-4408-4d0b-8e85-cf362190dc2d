import {ClasseXml} from "./ClasseXml";
import {ItemNFe} from "../../../domain/nfce/ItemNFe";
import {XmlUtils} from "../comunicacao/XmlUtils";
import {FormatadorDeNumeros} from "../utils/FormatadorDeNumeros";
import {Pis} from "../../../domain/nfce/Pis";
import {Cofins} from "../../../domain/nfce/Cofins";
import {ICMSUFDestino} from "../../../domain/nfce/ICMSUFDestino";


export class ItemNFeXml extends ClasseXml {
  private itemNFe: ItemNFe;
  private homologacao: boolean;
    constructor(itemNFe: ItemNFe, homologacao: boolean) {
        super();
        this.itemNFe = itemNFe;
        this.homologacao = homologacao
    }
    obtenhaXml() {
        const el = this.itemNFe

        let descricao = this.homologacao && this.itemNFe.numeroItem === 1 ?
          "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL" : this.itemNFe.descricaoDoProduto
        return `
            <det nItem="${this.itemNFe.numeroItem}">
                <prod>
                    ${XmlUtils.gereTag("cProd", this.itemNFe.codigoProduto)}
                    <cEAN>${this.itemNFe.codigoEAN || "SEM GTIN"}</cEAN>
                    ${XmlUtils.gereTag("xProd", descricao)}
                    ${XmlUtils.gereTag("NCM", this.itemNFe.ncm)}
                    ${XmlUtils.gereTag("NVE", this.itemNFe.nve)}
                    ${XmlUtils.gereTag("CEST", this.itemNFe.cest)}
                    ${XmlUtils.gereTag("indEscala", this.itemNFe.indEscala)}
                    ${XmlUtils.gereTag("CNPJFab", this.itemNFe.cnpjFabricante)}
                    ${XmlUtils.gereTag("cBenef", this.itemNFe.codigoBeneficio)}
                    ${XmlUtils.gereTag("EXTIPI", this.itemNFe.extIPI)}
                    <CFOP>${this.itemNFe.cfop}</CFOP>
                    <uCom>${this.itemNFe.unidadeComercial}</uCom>
                    <qCom>${FormatadorDeNumeros.formateDecimal(this.itemNFe.qtdeComercial, 4)}</qCom>
                    <vUnCom>${FormatadorDeNumeros.formateDecimal(this.itemNFe.valorUnicoComercial, 10)}</vUnCom>
                    <vProd>${FormatadorDeNumeros.formateDecimal(this.itemNFe.valorTotalBruto, 2)}</vProd>
                    ${this.itemNFe.codigoEANTrib ? `<cEANTrib>${this.itemNFe.codigoEANTrib}</cEANTrib>` : "<cEANTrib>SEM GTIN</cEANTrib>"}
                    <uTrib>${this.itemNFe.unidadeTributavel}</uTrib>
                    <qTrib>${FormatadorDeNumeros.formateDecimal(this.itemNFe.qtdeTributavel, 4)}</qTrib>
                    <vUnTrib>${FormatadorDeNumeros.formateDecimal(this.itemNFe.valorUnitarioTributacao, 10)}</vUnTrib>
                    ${this.itemNFe.valorFrete ? XmlUtils.gereTag("vFrete", FormatadorDeNumeros.formateDecimalNulo(this.itemNFe.valorFrete,2)) : ""}
                    ${this.itemNFe.valorSeguro ? XmlUtils.gereTag("vSeg", FormatadorDeNumeros.formateDecimalNulo(this.itemNFe.valorSeguro,2)) : ""}
                    ${this.itemNFe.valorDesconto ? XmlUtils.gereTag("vDesc", FormatadorDeNumeros.formateDecimalNulo(this.itemNFe.valorDesconto,2)) : ""}
                    ${this.itemNFe.valorOutrasDespesas ? XmlUtils.gereTag("vOutro", FormatadorDeNumeros.formateDecimalNulo(this.itemNFe.valorOutrasDespesas,2)) : ""}
                    ${this.tratePropCompoeTotNota()}
                    ${this.itemNFe.gereXmlDI()}
                    ${this.itemNFe.gereXmlDE()}
                    ${XmlUtils.gereTag('xPed', this.itemNFe.numPedCompra)}
                    ${XmlUtils.gereTag('nItemPed', this.itemNFe.numItemPedCompra?.toString())}
                    ${XmlUtils.gereTag('nFCI', this.itemNFe.nFCI)}
                    ${this.itemNFe.gereXmlItemVeiculo()}
                    ${this.itemNFe.gereXmlItemCombustivel()}
                    ${XmlUtils.gereTag("nRECOPI", this.itemNFe.recopi)}
                </prod>
                <imposto>
                    ${XmlUtils.gereTag("vTotTrib", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.itemNFe.valorTotalTributacao, 2))}
                    ${this.gereImposto()}
                    ${Pis.gereXml(this.itemNFe)}
                    ${Cofins.gereXml(this.itemNFe)}
                    ${ICMSUFDestino.gereXml(this.itemNFe)}
                </imposto>
                ${this.itemNFe.gereXmlImpostoDevolvido()}
                ${XmlUtils.gereTag("infAdProd", this.itemNFe.informacoesAdicionais)}
            </det>
        `;
    }

  private tratePropCompoeTotNota() {
    if(this.itemNFe.compoeTotalDaNota === undefined)
      return "<indTot>1</indTot>"

    if (this.itemNFe.compoeTotalDaNota) {
      return "<indTot>1</indTot>";
    }
    return "<indTot>0</indTot>";
  }

  private gereImposto(): string {
    if (this.itemNFe.issqn != null) {
      return `
            ${this.itemNFe.ipi ? this.itemNFe.ipi.gereXml() : ''}
            ${this.itemNFe.issqn.gereXml()}
        `;
    } else {
      return `
            ${this.itemNFe.gereXmlICMS()}
            ${this.itemNFe.ipi ? this.itemNFe.ipi.gereXml() : ''}
            ${this.itemNFe.ii ? this.itemNFe.ii.gereXml() : ''}
        `;
    }
  }


}
