import {ClasseXml} from "./ClasseXml";
import {Ipi} from "../../../domain/nfce/Ipi";
import {XmlUtils} from "../comunicacao/XmlUtils";
import {FormatadorDeNumeros} from "../utils/FormatadorDeNumeros";

export class IpiXml extends ClasseXml {
  //gera o xml do ipi baseado no objeto ipi
  constructor(private ipi: Ipi) {
    super();
  }

  obtenhaXml(): string {
    return `
    <IPI>
        ${XmlUtils.gereTag("CNPJProd", this.ipi.cnpjProdutor)}
        ${XmlUtils.gereTag("cSelo", this.ipi.codigoDoSelo)}
        ${XmlUtils.gereTag("qSelo", this.ipi.qtdeDeSelo)}
        ${this.ipi.codigoDeEnquadramento ? `<clEnq>${this.ipi.codigoDeEnquadramento}</clEnq>` : XmlUtils.gereTag("cEnq", "999")}
        <${this.ipi.obtenhaNomeTag()}>
          ${this.ipi.cst ? `<CST>${this.ipi.cst}</CST>` : ''}
          ${this.ipi.valorBaseDeCalculo ? `<vBC>${this.ipi.valorBaseDeCalculo}</vBC>` : ''}
          ${this.ipi.qtdeTotal ? `<qUnid>${this.ipi.qtdeTotal}</qUnid>` : ''}
          ${this.ipi.valorUnidadeTributavel ? `<vUnid>${this.ipi.valorUnidadeTributavel}</vUnid>` : ''}
          ${this.ipi.aliquota ? `<pIPI>${this.ipi.aliquota}</pIPI>` : ''}
          ${this.ipi.valor ? `<vIPI>${FormatadorDeNumeros.formateDecimal(this.ipi.valor, 2)}</vIPI>` : ''}
        </${this.ipi.obtenhaNomeTag()}>
    </IPI>
    `;
  }
}
