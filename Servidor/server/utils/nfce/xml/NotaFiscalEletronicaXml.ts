import {NotaFiscalEletronica} from "../../../domain/nfce/NotaFiscalEletronica";
import {FormatadorDeDatas} from "../utils/FormatadorDeDatas";
import {XmlUtils} from "../comunicacao/XmlUtils";
import {TipoImpressaoDoDanfe} from "../../../domain/nfce/TipoImpressaoDoDanfe";
import {FormatadorDeNumeros} from "../utils/FormatadorDeNumeros";
import {Transporte} from "../../../domain/nfce/Transporte";
import {Cobranca} from "../../../domain/nfce/Cobranca";
import {PagamentoNfe} from "../../../domain/nfce/PagamentoNfe";
import { ClasseXml } from "./ClasseXml";

export class NotaFiscalEletronicaXml extends ClasseXml {
  nota: NotaFiscalEletronica;
  formatador: FormatadorDeDatas
  constructor(nota: NotaFiscalEletronica) {
    super()
    this.nota = nota;

  }

  obtenhaXml() {
    let formatador = new FormatadorDeDatas()
    return `<NFe xmlns="http://www.portalfiscal.inf.br/nfe">
    <infNFe Id="${this.nota.getChaveDeAcesso()}" versao="4.00">
        <ide>
            <cUF>${this.nota.ufEmitente}</cUF>
            <cNF>${this.nota.chaveDeAcesso.substring(35, 43)}</cNF>
            ${XmlUtils.gereTag("natOp", this.nota.naturezaDaOperacao)}
            <mod>65</mod>
            <serie>${this.nota.serie}</serie>
            <nNF>${this.nota.numeroNFe}</nNF>
            <dhEmi>${formatador.formateSefaz(this.nota.dataDeEmissao)}</dhEmi>
            ${XmlUtils.gereTag("dhSaiEnt", formatador.formateSefaz(this.nota.dataHoraSaida))}
            <tpNF>${this.nota.tipoDoDocumento}</tpNF>
            <idDest>${this.nota.tipoDestinoOperacao}</idDest>
            <cMunFG>${this.nota.municipioFatorGerador}</cMunFG>
            <tpImp>${TipoImpressaoDoDanfe.NFCE_COM_MENSAGEM_ELETRONICA}</tpImp>
            <tpEmis>${this.nota.tipoEmissao}</tpEmis>
            <cDV>${this.nota.DigitoVerificador()}</cDV>
            <tpAmb>${this.nota.ambiente}</tpAmb>
            <finNFe>${this.nota.finalidade}</finNFe>
            <indFinal>${this.nota.consumidorFinal}</indFinal>
            <indPres>${this.nota.compradorPresente}</indPres>
            ${this.gereIndIntermed()}
            <procEmi>0</procEmi>
            <verProc>1</verProc>
            ${XmlUtils.gereTag("dhCont", formatador.formateSefaz(this.nota.dataContingencia))}
            ${XmlUtils.gereTag("xJust", this.nota.justContingencia)}
        </ide>
         <emit>
                    ${XmlUtils.gereTag('CNPJ', this.nota.cnpjEmitente?.toString().padStart(14, '0'))}
                    ${XmlUtils.gereTag('CPF', this.nota.cpfEmitente?.toString().padStart(11, '0'))}
                    ${XmlUtils.gereTag("xNome", this.nota.nomeEmitente)}
                    ${XmlUtils.gereTag("xFant", this.nota.nomeFantasiaEmitente)}
                    <enderEmit>
                        ${this.nota.enderecoEmitente.gereXml()}
                    </enderEmit>
                    <IE>${this.nota.ieEmitente}</IE>
                    ${XmlUtils.gereTag("IEST", this.nota.ieStEmitente)}
                    ${this.nota.inscricaoMunicipal != null && this.nota.inscricaoMunicipal.toUpperCase() === "ISENTO" ? "" : XmlUtils.gereTag("IM", this.nota.inscricaoMunicipal)}
                    ${XmlUtils.gereTag("CNAE",  (this.nota.inscricaoMunicipal == null || this.nota.inscricaoMunicipal === ""
    || this.nota.inscricaoMunicipal.toUpperCase() === "") ? null : this.nota.cnaeEmitente)}
                    ${XmlUtils.gereTag("CRT", (this.nota.codigoRegimeTributario != null ? this.nota.codigoRegimeTributario : 3))}
         </emit>
         ${this.gereDestinatario()}
         ${this.gereLocalRetirada()}
         ${this.gereLocalEntrega()}
         ${this.nota.gereAutorizacoesDownloadXml()}
         ${this.nota.gereItensXml()}
         <total>
             <ICMSTot>
                     <vBC>${FormatadorDeNumeros.formateDecimal(this.nota.valorBaseDeCalculoICMS, 2)}</vBC>
                    <vICMS>${FormatadorDeNumeros.formateDecimal(this.nota.valorICMS, 2)}</vICMS>
                    <vICMSDeson>${FormatadorDeNumeros.formateDecimal(this.nota.valorICMSDesonerado, 2)}</vICMSDeson>
                    ${XmlUtils.gereTag("vFCPUFDest", FormatadorDeNumeros.formateDecimal(this.nota.valorFCPUFDest, 2))}
                    ${XmlUtils.gereTag("vICMSUFDest", FormatadorDeNumeros.formateDecimal(this.nota.valorICMSUFDest, 2))}
                    ${XmlUtils.gereTag("vICMSUFRemet", FormatadorDeNumeros.formateDecimal(this.nota.valorICMSUFRemet, 2))}
                    <vFCP>${FormatadorDeNumeros.formateDecimal(this.nota.valorFCP, 2)}</vFCP>
                    <vBCST>${FormatadorDeNumeros.formateDecimal(this.nota.valorBaseDeCalculoICMSSt, 2)}</vBCST>
                    <vST>${FormatadorDeNumeros.formateDecimal(this.nota.valorICMSSt, 2)}</vST>
                    <vFCPST>${FormatadorDeNumeros.formateDecimal(this.nota.valorFCPST, 2)}</vFCPST>
                    <vFCPSTRet>${FormatadorDeNumeros.formateDecimal(this.nota.valorFCPSTRet, 2)}</vFCPSTRet>
                    ${XmlUtils.gereTag("qBCMono", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.totalTributadoMono, 2))}
                    ${XmlUtils.gereTag("vICMSMono", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorICMSMono, 2))}
                    ${XmlUtils.gereTag("qBCMonoReten", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.totalTributadoMonoRetencao, 2))}
                    ${XmlUtils.gereTag("vICMSMonoReten", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorICMSMonoRetencao, 2))}
                    ${XmlUtils.gereTag("qBCMonoRet", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.totalTributadoMonoRetido, 2))}
                    ${XmlUtils.gereTag("vICMSMonoRet", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorICMSMonoRetido, 2))}
                    <vProd>${FormatadorDeNumeros.formateDecimal(this.nota.valorTotalProdutos, 2)}</vProd>
                    <vFrete>${FormatadorDeNumeros.formateDecimal(this.nota.valorDoFrete, 2)}</vFrete>
                    <vSeg>${FormatadorDeNumeros.formateDecimal(this.nota.valorDoSeguro, 2)}</vSeg>
                    <vDesc>${FormatadorDeNumeros.formateDecimal(this.nota.valorDoDesconto, 2)}</vDesc>
                    <vII>${FormatadorDeNumeros.formateDecimal(this.nota.valorII, 2)}</vII>
                    <vIPI>${FormatadorDeNumeros.formateDecimal(this.nota.valorIPI, 2)}</vIPI>
                    <vIPIDevol>${FormatadorDeNumeros.formateDecimal(this.nota.valorIPIdevolvido, 2)}</vIPIDevol>
                    <vPIS>${FormatadorDeNumeros.formateDecimal(this.nota.valorPis, 2)}</vPIS>
                    <vCOFINS>${FormatadorDeNumeros.formateDecimal(this.nota.valorCofins, 2)}</vCOFINS>
                    <vOutro>${FormatadorDeNumeros.formateDecimal(this.nota.valorOutrasDespesas, 2)}</vOutro>
                    <vNF>${FormatadorDeNumeros.formateDecimal(this.nota.valorTotalNFe, 2)}</vNF>
                    ${XmlUtils.gereTag("vTotTrib", FormatadorDeNumeros.formateDecimalNulo(this.nota.valorTotalTributacao, 2))}
             </ICMSTot>
             ${this.gereTagISSQNTot()}
             <retTrib>
                  ${XmlUtils.gereTag("vRetPIS", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorRetidoPis, 2))}
                  ${XmlUtils.gereTag("vRetCOFINS", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorRetidoCofins, 2))}
                  ${XmlUtils.gereTag("vRetCSLL", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorRetidoCsll, 2))}
                  ${XmlUtils.gereTag("vBCIRRF", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.baseDeCalculoIRRF, 2))}
                  ${XmlUtils.gereTag("vIRRF", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorIRRF, 2))}
                  ${XmlUtils.gereTag("vBCRetPrev", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.nfeBasecalcretprev, 2))}
                  ${XmlUtils.gereTag("vRetPrev", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.nfeValretidoprevsocial, 2))}
             </retTrib>
         </total>
         <transp>
              <modFrete>${this.nota.modoFrete}</modFrete>
              ${Transporte.gereXml(this.nota)}
         </transp>
         ${Cobranca.gereXml(this.nota)}
         ${this.gerePagamentos()}
         ${this.gereInfIntermed()}
         <infAdic>
             ${XmlUtils.gereTag('infAdFisco', this.nota.infAdicionaisFisco)}
              ${XmlUtils.gereTag('infCpl', this.nota.informacoesAdicionaisContrib)}
              ${this.nota.gereObservacoesContribuinte()}
         </infAdic>
         ${this.gereResponsavelTecnico()}
         ${this.gereLocalEmbarque()}
    </infNFe>
</NFe>`

      ;
  }

  private gereLocalEmbarque(): string {
    if (!this.nota.ufEmbarque || this.nota.ufEmbarque === "") return "";

    return `
        <exporta>
            <UFSaidaPais>${this.nota.ufEmbarque}</UFSaidaPais>
            <xLocExporta>${this.nota.localEmbarque}</xLocExporta>
            ${XmlUtils.gereTag("xLocDespacho", this.nota.localDespacho)}
        </exporta>
    `;
  }

  private gereResponsavelTecnico(): string {
    const ufsComResponsavelTecnico = [13, 50, 26, 41, 42, 17];

    if (ufsComResponsavelTecnico.includes(this.nota.ufEmitente)) {
      return `
            <infRespTec>
                <CNPJ>08150325000162</CNPJ>
                <xContato>Rafael Augusto Mendes Moreira</xContato>
                <email><EMAIL></email>
                <fone>62981712622</fone>
            </infRespTec>
        `;
    } else {
      return "";
    }
  }

  private gereInfIntermed(): string {
    if ([2, 3, 4, 9].includes(this.nota.compradorPresente) &&
      this.nota.finalidade === 1 &&
      this.nota.tipoDoDocumento === 1 &&
      this.nota.indicadorIntermediador === 1) {
      return `
                <infIntermed>
                    <CNPJ>${this.nota.cnpjIntermediador}</CNPJ>
                    <idCadIntTran>${this.nota.identificadorNoIntermediador}</idCadIntTran>
                </infIntermed>
            `;
    } else {
      return "";
    }
  }

  private gerePagamentos(): string {
    if (this.nota.finalidade === 3 || this.nota.finalidade === 4) {
      return `<pag>
                        <detPag>
                            <tPag>90</tPag>
                            <vPag>0.00</vPag>
                        </detPag>
                    </pag>`;
    } else {
      return `<pag>
                        ${PagamentoNfe.gereXml(this.nota)}
                        ${XmlUtils.gereTag("vTroco", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorTroco, 2))}
                    </pag>`;
    }
  }
  private gereTagISSQNTot(): string {
    if (!this.nota.dataPrestacaoServico) return "";

    return `
        <ISSQNtot>
            ${XmlUtils.gereTag("vServ", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorTotalServicos, 2))}
            ${XmlUtils.gereTag("vBC", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.baseDeCalculoISS, 2))}
            ${XmlUtils.gereTag("vISS", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorISS, 2))}
            ${XmlUtils.gereTag("vPIS", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorServicosPis, 2))}
            ${XmlUtils.gereTag("vCOFINS", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorServicosCofins, 2))}
            ${XmlUtils.gereTag("dCompet", this.formatador.formate(this.nota.dataPrestacaoServico))}
            ${XmlUtils.gereTag("vDeducao", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorDeducao, 2))}
            ${XmlUtils.gereTag("vOutro", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorOutrasRetencoes, 2))}
            ${XmlUtils.gereTag("vDescIncond", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorDescontoIncondicionado, 2))}
            ${XmlUtils.gereTag("vDescCond", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorDescontoCondicionado, 2))}
            ${XmlUtils.gereTag("vISSRet", FormatadorDeNumeros.formateDecimalNuloOuZerado(this.nota.valorISSRetido, 2))}
            ${XmlUtils.gereTag("cRegTrib", this.nota.codigoRegimeEspecialTributacao)}
        </ISSQNtot>`;
  }

  private gereLocalEntrega(): string {
    if (!this.nota.cnpjEntrega || this.nota.cnpjEntrega === "") return "";

    const tagsXml = [
      `<CNPJ>${this.nota.cnpjEntrega}</CNPJ>`,
      XmlUtils.gereTag("xNome", this.nota.nomeRecebedor),
      XmlUtils.gereTag("xLgr", this.nota.localEntrega.logradouro),
      XmlUtils.gereTag("nro", this.nota.localEntrega.numero),
      XmlUtils.gereTag("xCpl", this.nota.localEntrega.complemento),
      XmlUtils.gereTag("xBairro", this.nota.localEntrega.bairro),
      `<cMun>${this.nota.localEntrega.codMunicipio}</cMun>`,
      XmlUtils.gereTag("xMun", this.nota.localEntrega.nomeMunicipio),
      XmlUtils.gereTag("UF", this.nota.localEntrega.siglaUF),
      XmlUtils.gereTag("CEP", this.nota.localEntrega.cep),
      XmlUtils.gereTag("cPais", this.nota.localEntrega.codigoPais),
      XmlUtils.gereTag("xPais", this.nota.localEntrega.nomePais),
      XmlUtils.gereTag("fone", this.nota.localEntrega.telefone),
      XmlUtils.gereTag("email", this.nota.emailRecebedor),
      XmlUtils.gereTag("IE", this.nota.ieRecebedor)
    ].filter(tag => tag).join('');

    return `<entrega>${tagsXml}</entrega>`;
  }

  private gereLocalRetirada(): string {
    if (!this.nota.cpfExpedidor || this.nota.cpfExpedidor === "") return "";

    const tagsXml = [
      `<CNPJ>${this.nota.cnpjExpedidor}</CNPJ>`,
      XmlUtils.gereTag("xNome", this.nota.nomeExpedidor),
      XmlUtils.gereTag("xLgr", this.nota.localRetirada.logradouro),
      XmlUtils.gereTag("nro", this.nota.localRetirada.numero),
      XmlUtils.gereTag("xCpl", this.nota.localRetirada.complemento),
      XmlUtils.gereTag("xBairro", this.nota.localRetirada.bairro),
      `<cMun>${this.nota.localRetirada.codMunicipio}</cMun>`,
      XmlUtils.gereTag("xMun", this.nota.localRetirada.nomeMunicipio),
      XmlUtils.gereTag("UF", this.nota.localRetirada.siglaUF),
      XmlUtils.gereTag("CEP", this.nota.localRetirada.cep),
      XmlUtils.gereTag("cPais", this.nota.localRetirada.codigoPais),
      XmlUtils.gereTag("xPais", this.nota.localRetirada.nomePais),
      XmlUtils.gereTag("fone", this.nota.localRetirada.telefone),
      XmlUtils.gereTag("email", this.nota.emailExpedidor),
      XmlUtils.gereTag("IE", this.nota.ieExpedidor)
    ].filter(tag => tag).join('');

    return `<retirada>${tagsXml}</retirada>`;
  }
  private gereDestinatario(): string {
    if(!this.nota.cnpjDestinatario && !this.nota.cpfDestinatario
      && !this.nota.idEstrangeiroDestinatario && !this.nota.nomeDestinatario)
      return "";

    let nomeDestinatario = this.nota.ambiente === 2 ? "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL" : this.nota.nomeDestinatario;


    const enderecoXml = this.nota.enderecoDestinatario ? `<enderDest>${this.nota.enderecoDestinatario.gereXml()}</enderDest>` : "";
    const tagsXml = [
      XmlUtils.gereTag('CNPJ', this.nota.cnpjDestinatario),
      XmlUtils.gereTag('CPF', this.nota.cpfDestinatario),
      this.gereIdEstrangeiro(),
      XmlUtils.gereTag("xNome", nomeDestinatario),
      enderecoXml,
      `<indIEDest>${this.nota.indIeDestinatario}</indIEDest>`,
      XmlUtils.gereTag("IE", this.nota.ieDestinatario),
      XmlUtils.gereTag("ISUF", this.nota.inssSuframaDestinatario),
      XmlUtils.gereTag("IM", this.nota.imDestinatario),
      XmlUtils.gereTag("email", this.nota.email)
    ].filter(tag => tag).join('');

    return `<dest>${tagsXml}</dest>`;
  }

  private gereIdEstrangeiro(): string {
    if ((!this.nota.cnpjDestinatario || this.nota.cnpjDestinatario === "") &&
      (!this.nota.cpfDestinatario || this.nota.cpfDestinatario === "")) {
      return `<idEstrangeiro>${this.nota.idEstrangeiroDestinatario ? this.nota.idEstrangeiroDestinatario : ""}</idEstrangeiro>`;
    }

    return "";
  }

  private gereIndIntermed(): string {
    if ([2, 3, 4, 9].includes(this.nota.compradorPresente) && this.nota.finalidade === 1 && this.nota.tipoDoDocumento === 1) {
      return `<indIntermed>${this.nota.indicadorIntermediador}</indIntermed>`;
    }
    return "";
  }
}
