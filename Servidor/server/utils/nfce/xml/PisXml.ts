import {ClasseXml} from "./ClasseXml";
import {string} from "blockly/core/utils";
import {Pis} from "../../../domain/nfce/Pis";
import {FormatadorDeNumeros} from "../utils/FormatadorDeNumeros";
import {XmlUtils} from "../comunicacao/XmlUtils";

export class PisXml extends ClasseXml{
    //gera o xml do pis baseado no objeto pis
    constructor(private pis: Pis) {
        super();
    }
    obtenhaXml(): string {
        let xml = `
        <PIS>
            <${this.pis.obtenhaNomeTag()}>
                ${this.pis.cst ? `<CST>${this.pis.obtenhaCst()}</CST>` : ''}
                ${XmlUtils.gereTag("vBC", FormatadorDeNumeros.formateDecimalNulo(this.pis.valorBaseCalculo, 2))}
                ${XmlUtils.gereTag("pPIS", FormatadorDeNumeros.formateDecimalNulo(this.pis.percentualAliquota, 4))}
                ${XmlUtils.gereTag("qBCProd", FormatadorDeNumeros.formateDecimalNulo(this.pis.qtdeVendida, 4))}
                ${XmlUtils.gereTag("vAliqProd", FormatadorDeNumeros.formateDecimalNulo(this.pis.valorAliquota, 4))}
                ${XmlUtils.gereTag("vPIS", FormatadorDeNumeros.formateDecimalNulo(this.pis.valor, 2))}
             </${this.pis.obtenhaNomeTag()}>
        </PIS>
        `;

        if(this.pis.valorSt){
            xml += `
            <PISST>
                ${XmlUtils.gereTag("vBC", FormatadorDeNumeros.formateDecimalNulo(this.pis.valorBaseDeCalculoSt, 2))}
                ${XmlUtils.gereTag("pPIS", FormatadorDeNumeros.formateDecimalNulo(this.pis.percentualAliquotaSt, 4))}
                ${XmlUtils.gereTag("qBCProd", FormatadorDeNumeros.formateDecimalNulo(this.pis.qtdeVendidaSt, 4))}
                ${XmlUtils.gereTag("vAliqProd", FormatadorDeNumeros.formateDecimalNulo(this.pis.valorAliquotaSt, 4))}
                ${XmlUtils.gereTag("vPIS", FormatadorDeNumeros.formateDecimalNulo(this.pis.valorSt, 2))}
            </PISST>
            `;
        }

        return xml;
    }

}
