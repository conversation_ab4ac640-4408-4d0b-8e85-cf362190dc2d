const axios = require('axios');
const xmlescape = require('xml-escape');

const parseString = require('xml2js').parseString;
const decode = require('unescape');

export class InvocadorSoapNfseGoiania {
  private endereco: string;

  constructor(endereco: string) {
    this.endereco = endereco;
  }

  invoqueWebService(documento: string) {
    return new Promise(resolve => {
      let xmlMensagemSoap = `<soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
    <soap12:Body>
        <GerarNfse xmlns="http://nfse.goiania.go.gov.br/ws/">
            <ArquivoXML>${xmlescape(documento)}</ArquivoXML>
        </GerarNfse>
    </soap12:Body>
</soap12:Envelope>
`
      xmlMensagemSoap = '<?xml version="1.0" encoding="utf-8"?>\n' + xmlMensagemSoap
      let headers = {  "Content-Type": "application/soap+xml; charset=utf-8"};


      axios.post(this.endereco, xmlMensagemSoap, { headers: headers}).then(   (response: any) => {
        if(response.status === 200) {
          parseString(response.data, (err: Error, result: any) => {
            if(err) {
              return resolve( {
                sucesso: false,
                resposta: err.message
              })
            }

            let stringXmlResposta = result["soap:Envelope"]["soap:Body"][0].GerarNfseResponse[0].GerarNfseResult[0]

            // tslint:disable-next-line:no-shadowed-variable
            parseString(stringXmlResposta, (err: Error, result: any) => {
              if(err) {
                return resolve({
                  sucesso: false,
                  resposta: err.message
                })
              }

              let resultadoProcessamento = result.GerarNfseResposta

              if(!resultadoProcessamento) {
                return resolve({
                  sucesso: false,
                  resposta: "A Prefeitura não retornou nenhuma resposta"
                })
              }


              if(!resultadoProcessamento.ListaNfse) {
                let listaMensagens = resultadoProcessamento.ListaMensagemRetorno

                let mensagemResposta = ""

                let textoMensagens: string[] = []

                for(let i = 0; i < listaMensagens.length; i++)
                  textoMensagens.push(listaMensagens[i].MensagemRetorno[0].Codigo[0] + " - "
                    + listaMensagens[i].MensagemRetorno[0].Mensagem[0])

                mensagemResposta = textoMensagens.join("; ")

                return resolve({
                  sucesso: false,
                  resposta: mensagemResposta
                })
              } else {
                console.log(resultadoProcessamento)
                let nfseProcessada = resultadoProcessamento.ListaNfse[0].CompNfse[0].Nfse[0].InfNfse[0]

                let dadosNota: any = {
                  numero: nfseProcessada.Numero[0],
                  codigoVerificacao: nfseProcessada.CodigoVerificacao[0]
                }

                return resolve({
                  sucesso: true,
                  resposta: 'A nfse ' + dadosNota.numero + ' foi gerada com sucesso!',
                  resumo: dadosNota,
                  xmlNfseProcessado: stringXmlResposta
                })
              }



              //let mensagensRetorno = result.
            })
          })


        }
        else
          resolve( {
            sucesso: false,
            resposta: response.data ? response.data : response.statusText
          })
      }).catch((err: any) => {
        console.log(err)
      })

    })
  }
}
