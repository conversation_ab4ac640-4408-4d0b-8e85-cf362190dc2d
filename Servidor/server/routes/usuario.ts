import { Router, Request, Response } from 'express';
import {UsuarioService} from "../service/UsuarioService";
import {Filtro} from "../domain/Filtro";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeFiltro} from "../mapeadores/MapeadorDeFiltro";
import * as _ from "underscore";
import {MapeadorDeUsuario} from "../mapeadores/MapeadorDeUsuario";
import {Usuario} from "../domain/Usuario";
const router: Router = Router();

router.post('/', (req, res, next) => {
  const {body: {user}} = req;

  if(!user.email) {
    return res.json({
      sucesso: false,
      mensagem: "E-mail é obrigatório"
    });
  }

  if(!user.senha) {
    return res.json({
      sucesso: false,
      mensagem: "Senha é obrigatório"
    });
  }

  if(!user.nome) {
    return res.json({
      sucesso: false,
      mensagem: "Nome é obrigatório"
    });
  }

  //nessa hora salvaria o usuario
  let usuarioService: UsuarioService = new UsuarioService()

  let usuario = usuarioService.crieUsuario(user);

  usuarioService.verifiqueExistencia(usuario.email).then(jaExiste => {
    if(jaExiste) return res.json({
      sucesso: false,
      mensagem: "Já existe um usuário com esse e-mail"
    })

    usuarioService.insiraUsuario(usuario).then(inseriu => {
      if(!inseriu) return res.json({
        sucesso: false,
        mensagem: "Não foi possível inserir o usuário"
      })

      return res.json({
        sucesso: true,
        mensagem: "Usuário inserido com sucesso"
      })
    })
  })
});


router.post('/filtros' , async (req: any, res) => {
  let usuario: any = req.user,
    dadosFiltro = req.body,
    id = dadosFiltro.id,
    nome = dadosFiltro.nome;

  let empresa = req.empresa;

  let filtro = new Filtro(id, empresa, usuario, nome,  dadosFiltro.dados);

  const erro: string =  await  new UsuarioService().salveFiltro(filtro);

  if( !erro ){
    res.json(Resposta.sucesso({id: filtro.id}));
  } else {
    res.json(Resposta.erro(erro));
  }
})

router.put('/filtros' , async (req: any, res) => {
  let usuario: any = req.user,
    dadosFiltro = req.body,
    id = dadosFiltro.id,
    nome = dadosFiltro.nome;

  let empresa = req.empresa;
  let filtro = new Filtro(id, empresa, usuario, nome,  dadosFiltro.dados);

  const erro: string =  await  new UsuarioService().atualizeFiltro(filtro);

  if( !erro ){
    res.json(Resposta.sucesso({id: filtro.id}));
  } else {
    res.json(Resposta.erro(erro));
  }
})

router.get('/filtros', async (req, res ) => {
  let usuario: any = req.user;

  if(!usuario) return     res.json(Resposta.sucesso({}));

  let filtros =  await  new MapeadorDeFiltro().listeAsync({})

  res.json(Resposta.sucesso(_.map(filtros, (filtro: Filtro) => {
    return filtro.getDados();
  })));
})

router.get('/garcom/:id', async (req, res ) => {
  let usuario = await new MapeadorDeUsuario().selecioneSync(req.params.id);

  if(usuario && usuario.id){
    res.json(Resposta.sucesso({id: usuario.id, nome: usuario.nome, codigoPdv: usuario.codigoPdv}));
  } else {
    res.json(Resposta.sucesso({}));
  }
})

router.post('/salveAssinatura', async(req: any, res: any) => {
  let usuario: Usuario = req.user;

  if(!usuario) return     res.json(Resposta.sucesso({}));

  usuario.assinarMensagens = req.body.assinar;

  const atualizou = await new UsuarioService().atualizeAssinatura(usuario);

  console.log(atualizou);

  if( atualizou ) {
    // salvar o usuário na sessão para forçar a serialização
    req.login(usuario,  ( ) => {
      res.json(Resposta.sucesso(true));
    })
  }

  res.json(Resposta.erro('Não foi possível salvar'));
});

export const UsuarioController: Router = router;
