import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {AppFacebook} from "../service/AppFacebook";
import {DadosInstagram} from "../domain/instagram/DadosInstagram";
import {MapeadorDeDadosInstagram} from "../mapeadores/MapeadorDeDadosInstagram";
import {TratadorMensagemIG} from "../lib/instagram/tratador_mensagem/TratadorMensagemIG";
import {ConstrutorDeRespostaIG} from "../lib/instagram/ConstrutorDeRespostaIG";
import {TratadorDeMensagemWitai} from "../lib/instagram/TratadorDeMensagemWitai";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
const axios = require('axios');



const router: Router = Router();

router.get('/contaConectada', async (req: any, res: any) => {
  const dadosInstagram = await new MapeadorDeDadosInstagram().selecioneSync({});

  if (dadosInstagram) {
    //delete dadosInstagram['accessToken'];
    delete dadosInstagram['accessTokenPagina'];
  }

  res.json(Resposta.sucesso(dadosInstagram));
});

router.post('/desconectarConta', async (req: any, res: any) => {
  const mapeador = new MapeadorDeDadosInstagram();
  const dadosInstagram = await mapeador.selecioneSync({});

  if (dadosInstagram) {
    await mapeador.removaAsync(dadosInstagram);

    await AppFacebook.Instance.desassinarEventos(dadosInstagram.idPaginaFace, dadosInstagram.accessTokenPagina);
  }

  res.json(Resposta.sucesso(true));
});

router.post('/estendaToken', async (req: any, res: any) => {
  let accessToken = req.body.at;
  let appFacebook = AppFacebook.Instance;

  console.log('vai estender: ', accessToken);

  appFacebook.estenderToken(accessToken).then(async(respostaTokenEstendido: any) => {
    res.json(Resposta.sucesso(respostaTokenEstendido.accessToken));
  });
});

router.post('/tokenDePagina', async (req: any, res: any) => {
  let accessToken = req.body.at;
  let idPagina = req.body.idp;
  let idInstagram = req.body.igid;
  let tokenDePagina = req.body.atp;
  let nomeInstagram = req.body.nome;
  let imageProfileUrl = req.body.imagem;
  let appFacebook = AppFacebook.Instance;
  let empresa = req.empresa;

  console.log('vai estender: ', accessToken);
  console.log('token de página: ', tokenDePagina);

  let dadosInstagram: DadosInstagram = await new MapeadorDeDadosInstagram().selecioneSync({});

  await appFacebook.assineEventosInsta(tokenDePagina, idPagina);

  const criouIcebreakers: Resposta<any> = await appFacebook.definaIceBreakers(tokenDePagina, idPagina);

  if( criouIcebreakers && !criouIcebreakers.sucesso ) {
    res.json(criouIcebreakers);
    return;
  }

  appFacebook.accessTokenPagina = tokenDePagina;
  if( !dadosInstagram ) {
    dadosInstagram = new DadosInstagram();
  }
  dadosInstagram.empresa = empresa;
  dadosInstagram.nomeInstagram = nomeInstagram;
  dadosInstagram.accessToken = accessToken;
  dadosInstagram.idPaginaFace = idPagina;
  dadosInstagram.accessTokenPagina = tokenDePagina;
  dadosInstagram.dataCriacao = new Date();
  dadosInstagram.userIdInsta = idInstagram;
  dadosInstagram.imageProfileUrl = imageProfileUrl;

  if( !dadosInstagram.id ) {
    await new MapeadorDeDadosInstagram().insiraGraph(dadosInstagram);
  } else {
    await new MapeadorDeDadosInstagram().atualizeSync(dadosInstagram);
  }

  res.json(Resposta.sucesso(tokenDePagina));
});

router.get('/webhook', (req: any, res: any) => {
// Parse the query params
  console.log("Got /webhook");
  let mode = req.query["hub.mode"];
  let token = req.query["hub.verify_token"];
  let challenge = req.query["hub.challenge"];
  const verifyToken = '234';

  // Check if a token and mode is in the query string of the request
  if (mode && token) {
    // Checks the mode and token sent is correct
    if (mode === "subscribe" && token === verifyToken) {
      // Responds with the challenge token from the request
      console.log("WEBHOOK_VERIFIED");
      res.status(200).send(challenge);
    } else {
      // Responds with '403 Forbidden' if verify tokens do not match
      res.sendStatus(403);
    }
  } else {
    console.warn("Got /webhook but without needed parameters.");
  }
});

router.get('/empresa', async (req: any, res: any) => {
  const mapeadorDeEmpresa = new MapeadorDeEmpresa();

  const empresa = await mapeadorDeEmpresa.selecioneSync({id: '454'});

  res.json(Resposta.sucesso(empresa));
});

router.post('/webhook', async (req: any, res: any) => {
  let body = req.body;

  console.log(` Received webhook:`);
  console.dir(body, {depth: null});
  const mapeadorDeEmpresa = new MapeadorDeEmpresa();

  if (body.object === "instagram") {
    // Return a '200 OK' response to all requests
    //res.status(200).send("EVENT_RECEIVED");

    // Iterate over each entry - there may be multiple if batched
    for( let entry of body.entry ) {
      if (!("messaging" in entry)) {
        console.warn("No messaging field in entry. Possibly a webhook test.");
        return null;
      }

      const idInstagram = entry.id;
      for( let webhookEvent of entry.messaging ) {
        let reciepientIgsid = webhookEvent.recipient.id;

        if ("message" in webhookEvent && webhookEvent.message.is_echo === true) {
          const idMensagem = webhookEvent.message.mid;

          const msgEhDoBot = await TratadorMensagemIG.msgFoiDoBot(idMensagem);

          console.log("Got an echo: " + reciepientIgsid + ' Msg é do bot: ' + msgEhDoBot);
          if( !msgEhDoBot ) {
            console.log('Desativando bot: ' + reciepientIgsid);
            await new TratadorMensagemIG(null, reciepientIgsid, webhookEvent).desligueBotParaAtendente();
          }

          return;
        }

        let senderIgsid = webhookEvent.sender.id;

        const mapeador = new MapeadorDeDadosInstagram();

        mapeador.desativeMultiCliente();
        console.log('id: ' + idInstagram);

        const dadosInstagram = await mapeador.selecioneSync({idInstagram: idInstagram});

        //console.log('dados instagram: ', dadosInstagram);

        if(!dadosInstagram)
          return res.status(400).json({ erro: 'Nenhum instagram com esse id: ' + idInstagram});

        const queryEmpresa = {id: dadosInstagram.empresa.id};

        const empresa = await mapeadorDeEmpresa.selecioneSync(queryEmpresa);

        const respostas = await processeMensagemWebhook(empresa, senderIgsid, webhookEvent);

        if (Array.isArray(respostas)) {
          let delay = 0;

          for (let resposta of respostas) {
            await envieMensagem(dadosInstagram, senderIgsid, resposta, delay * 1000);
            delay++;
          }
        } else if (respostas) {
          await envieMensagem(dadosInstagram, senderIgsid, respostas, 0);
        }
      }
    }

    res.json(Resposta.sucesso(true));
  }
});

router.get('/witai', async(req: any, res: any) => {
  const q = req.query.q;

  const tokenStr = 'AMIDZM7SE4GYFY6TBHATYD2YXMN5KQGK';

  axios.get(`https://api.wit.ai/message?v=20210904&q=${encodeURIComponent(q)}`, { headers: {"Authorization" : `Bearer ${tokenStr}`} }).then(
    (dados: any) => {
      const resposta = dados.data;

      if( resposta.intents.length === 0 ) {
        return res.json(Resposta.sucesso('NAO_ENTENDI'));
      }

      const intent = resposta.intents[0];
      res.json(Resposta.sucesso(intent.name));

      return;
    });
});

async function processeMensagemWebhook(empresa: any, igId: string, webhookEvent: any): Promise<any> {
  const construtor = new ConstrutorDeRespostaIG(empresa);
  
  // Verifica se está no modo atendente
  const client = require('redis').createClient();
  
  return new Promise((resolve, reject) => {
    client.get('atendente:' + igId, async (erro: Error, valor: string) => {
      try {
        if (valor && false) {
          // Se está no modo atendente, não responde
          console.log('está no modo atendente');

          resolve(null);
          return;
        }

        const event = webhookEvent;
        let responses: any = [];

        if (event.message) {
          let message = event.message;

          if (message.is_echo) {
            const idMensagem = message.mid;
            const msgEhDoBot = await TratadorMensagemIG.msgFoiDoBot(idMensagem);
            
            console.log("Got an echo: " + igId + ' Msg é do bot: ' + msgEhDoBot);
            if (!msgEhDoBot) {
              console.log('Desativando bot: ' + igId);
              ativarAtendente(igId);
            }
            resolve(null);
            return;
          } else if (message.quick_reply) {
            // Processa quick reply
            let payload = message.quick_reply.payload;
            responses = await processarPayload(construtor, payload, igId);
          } else if (message.attachments) {
            // Por enquanto não processa anexos
            responses = await construtor.construaResposta('nao_entendi', { igId });
          } else if (message.text) {
            // Processa mensagem de texto
            responses = await processarMensagemTexto(construtor, message.text, igId);
          }
        } else if (event.postback) {
          // Processa postback
          let payload = event.postback.payload;
          responses = await processarPayload(construtor, payload.toUpperCase(), igId);
        } else if (event.referral) {
          // Por enquanto não processa referral
          responses = await construtor.construaResposta('cumprimento', { igId });
        }

        resolve(responses);
      } catch (error) {
        console.error('Erro ao processar mensagem:', error);
        const respostasErro = await construtor.construaResposta('nao_entendi', { igId });
        resolve(respostasErro);
      }
    });
  });
}

async function processarMensagemTexto(construtor: ConstrutorDeRespostaIG, texto: string, igId: string): Promise<any[]> {
  console.log('processarMensagemTexto: ', texto);

  const intent = await new TratadorDeMensagemWitai().processeTexto(texto);
  
  const contextoMensagem = {
    igId: igId,
    message: texto,
    intent: intent
  };

  console.log('\n\\nintent: ', intent);

  switch (intent) {
    case 'horario_atendimento':
      return await construtor.construaResposta('horario_atendimento', contextoMensagem);
    case 'fazerpedido':
      return await construtor.construaResposta('cardapio', contextoMensagem);
    case 'enviar_cardapio':
      return await construtor.construaResposta('cardapio', contextoMensagem);
    case 'fome':
      return await construtor.construaResposta('fome', contextoMensagem);
    case 'cumprimento':
      return await construtor.construaResposta('get_started', contextoMensagem);
    case 'agradecimento':
      return await construtor.construaResposta('agradecimento', contextoMensagem);
    case 'whatsapp':
      return await construtor.construaResposta('whatsapp', contextoMensagem);
    case 'taxasdeentrega':
      return await construtor.construaResposta('taxas_entrega', contextoMensagem);
    case 'endereco':
      return await construtor.construaResposta('localizacao', contextoMensagem);
    case 'atendente':
      ativarAtendente(igId);
      return await construtor.construaResposta('atendente', contextoMensagem);
    default:
      ativarAtendente(igId);
      return await construtor.construaResposta('fallback', contextoMensagem);
  }
}

async function processarPayload(construtor: ConstrutorDeRespostaIG, payload: string, igId: string): Promise<any[]> {
  const contextoPayload = {
    igId: igId,
    payload: payload
  };

  payload = payload.toUpperCase();

  if( payload.includes("GET_STARTED") ) {
    return await construtor.construaResposta('get_started', contextoPayload);
  }
  else if (payload.includes("FAZER_PEDIDO")) {
    return await construtor.construaResposta('fazer_pedido', contextoPayload);
  } else if (payload.includes("HORARIO_ATENDIMENTO")) {
    return await construtor.construaResposta('horario_atendimento', contextoPayload);
  } else if (payload.includes("CARDAPIO")) {
    return await construtor.construaResposta('cardapio', contextoPayload);
  } else if (payload.includes("ATENDENTE")) {
    ativarAtendente(igId);
    return await construtor.construaResposta('atendente', contextoPayload);
  } else {
    return await construtor.construaResposta('fallback', contextoPayload);
  }
}

function ativarAtendente(igId: string) {
  const client = require('redis').createClient();
  client.setex('atendente:' + igId, 60 * 60 * 4, 'true', (erro: any) => {
    if (erro) console.error('Erro ao ativar atendente:', erro);
  });
}

async function envieMensagem(dadosInstagram: DadosInstagram, igsid: string, resposta: any, delay: number = 0) {
  return new Promise((resolve, reject) => {
    let requestBody = {
      recipient: {
        id: igsid
      },
      message: resposta
    };

    setTimeout(async () => {
      const resp: any = await AppFacebook.Instance.envieMensagemMessenger(dadosInstagram.accessTokenPagina, requestBody);
      const idMensagem = resp.message_id;

      console.log('salvando mensagem: ' + idMensagem);
      TratadorMensagemIG.salveMsgBot(idMensagem);

      resolve(resp);
    }, delay);
  });
}



export const InstagramController: Router = router;
