import {Router} from 'express';
import {Resposta} from "../utils/Resposta";
import {Campanha} from "../domain/Campanha";
import {CampanhaService} from "../service/CampanhaService";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {MapeadorDeCampanha} from "../mapeadores/MapeadorDeCampanha";
import {VariaveisDeRequest} from "../service/VariaveisDeRequest";
import {TarefaEnvioCampanha} from "../domain/TarefaEnvioCampanha";
import {MapeadorDeMensagemEnviada} from "../mapeadores/MapeadorDeMensagemEnviada";
import {Filtro} from "../domain/Filtro";
import {Empresa} from "../domain/Empresa";
import {Contato} from "../domain/Contato";
import {Ambiente} from "../service/Ambiente";
import {ImportadorProdutosCSV} from "../lib/integracao/ImportadorProdutosCSV";
import {EnumOrigemContatosCampanha} from "../domain/EnumOrigemContatosCampanha";
import {EnumStatusCampanha} from "../domain/EnumStatusCampanha";
import {Contrato} from "../domain/faturamento/Contrato";
import {Fatura} from "../domain/faturamento/Fatura";
import * as  moment from "moment";
import {MapeadorDeHistoricoCreditosMensagens} from "../mapeadores/MapeadorDeHistoricoCreditosMensagens";
import {HistoricoCreditosMensagens} from "../domain/mensagens/HistoricoCreditosMensagens";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {EnumStatusAprovacao} from "../domain/EnumStatusAprovacao";
// @ts-ignore
import * as csvParse from 'csv-parse';
import {MapeadorDeMensagemGeradaIA} from "../mapeadores/MapeadorDeMensagemGeradaIA";
import {IAService} from "../service/IAService";

let path = require('path');

const uuidv1 = require('uuid/v1');
let redis = require("redis");
let fs = require('fs');
let readline = require('readline');

let client = redis.createClient();

const router: Router = Router();

router.get('/saldoIA', async(req: any, res: any) => {
  const empresa: Empresa = req.empresa;

  const resposta = await new IAService().obtenhaSaldo(empresa);

  res.json(resposta);
});

router.get('/saldo', async(req: any, res: any) => {
  const empresa: Empresa = req.empresa;
  const contrato: Contrato = await Contrato.get({
    idEmpresa: empresa.id
  });

  if( !contrato ) {
    return res.json(Resposta.sucesso({
      qtde: empresa.obtenhaSaldoMensagens()
    }));
  }
  const ultimaFatura: Fatura = contrato.obtenhaUltimaFaturaPaga();

  if( !ultimaFatura ) {
    return res.json(Resposta.sucesso({
      qtde: 0
    }));
  }

  const mapeadorDeHistoricoCreditosMensagens = new MapeadorDeHistoricoCreditosMensagens();

  //gerar créditos
  const dadosFatura = ultimaFatura.getDados();

  let qtdeParcelas = dadosFatura.parcelas ? dadosFatura.parcelas : 1;
  let dataVencimento = moment(ultimaFatura.dataVencimento);

  const qtdeMeses = moment().diff( dataVencimento, 'months');

  if( qtdeMeses <= qtdeParcelas ) { //verifica se o pagamento deve atribuir créditos ou não
    res.json(Resposta.sucesso({
        qtde: empresa.obtenhaSaldoMensagens()
      }));
    return;
  }

  dataVencimento.add(qtdeMeses, 'month');

  const referencia: number = +dataVencimento.format('YYYYMM');

  let historico: HistoricoCreditosMensagens = await mapeadorDeHistoricoCreditosMensagens.selecioneSync({
    referencia: referencia
  });

  if( historico ) { // se tiver salvo, empresa já ganhou os créditos do mês
    res.json(Resposta.sucesso({
      qtde: empresa.obtenhaSaldoMensagens()
    }));

    return;
  }

  historico = new HistoricoCreditosMensagens(new Date(), referencia, contrato.qtdeMensagensMes, empresa);

  const mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada();

  const qtdeEnviadas = await mapeadorDeMensagemEnviada.selecioneTotal({
    tipo: TipoDeNotificacaoEnum.Marketing,
    horario: dataVencimento.toDate()
  });

  empresa.saldoMensagens = contrato.qtdeMensagensMes - qtdeEnviadas;
  if( empresa.saldoMensagens < 0 ) {
    empresa.saldoMensagens = 0;
  }

  await new MapeadorDeEmpresa().atualizeSaldoMensagens(empresa);

  await mapeadorDeHistoricoCreditosMensagens.insiraGraph(historico);

  res.json(Resposta.sucesso({
    qtde: empresa.obtenhaSaldoMensagens()
  }));
});

router.post('/validarContatos', async (req, res) => {
  let file: any = null, partesNome: any = null, extensao = null;

  if( req.files ) {
    file = req.files.files;
    partesNome = file.name.split('.');
    extensao = file.name.split('.')[partesNome.length - 1];
  }

  leiaArquivo(file, extensao).then( async (respArquivo: any) => {
    if (!respArquivo.sucesso) {
      return res.status(400).json(Resposta.erro(respArquivo.erro));
    }

    let listaDeContatos = respArquivo.contatos.map( (contato: any) => {
      return contato.obtenhaDTOContato();
    });

    res.json(Resposta.sucesso(listaDeContatos));
  });
});

router.put('/', async (req: any, res) => {
  let file: any = null, partesNome: any = null, extensao = null;
  const empresa = req.empresa;

  if(!req.body.data)
    return res.json(Resposta.erro('Dados da campanha não informados'));

  let dados: any = JSON.parse(req.body.data);

  if( req.files ) {
    file = req.files.file;
    partesNome = file.name.split('.');
    extensao = file.name.split('.')[partesNome.length - 1];

    delete dados.files;
  }

  leiaArquivo(file, extensao).then( async (respArquivo: any) => {
    if (!respArquivo.sucesso) {
      return res.json(Resposta.erro(respArquivo.erro));
    }

    let campanha = await  new MapeadorDeCampanha().obtenhaCampanha({id: dados.id});

    Object.assign(campanha, dados);
    if (dados.horarioEnvio)
      campanha.horarioEnvio = new Date(dados.horarioEnvio);

    if( campanha.origemContatos === EnumOrigemContatosCampanha.Filtro ) {
      campanha.contatos = [];
      // Criar um novo filtro com os dados corretos
      campanha.filtro = new Filtro(
        dados.filtro.id,
        campanha.empresa,
        null,  // usuário pode ser null para este caso
        dados.filtro.nome,
        dados.filtro.dados  // Isso vai passar pelo método setDados que fará a serialização correta
      );
    } else if( file ) {
      campanha.contatos = respArquivo.contatos;
    }

    if( !campanha.campanhaRede ) {
      campanha.statusAprovacao = EnumStatusAprovacao.Pendente;
    }

    const listaDeContatos: any = await campanha.carregueContatos(empresa, {}, true);
    const listaDeContatosTotal: any = await campanha.carregueContatos(empresa, {}, false);

    if( listaDeContatos.length == 0 ) {
      let msg = '';
      if (campanha.origemContatos === 'Arquivo') {
        msg = `O arquivo contém ${listaDeContatosTotal.length} contatos, mas nenhum deles é válido para enviar a mensagem após aplicar os filtros.
          Verifique se os números de telefone estão corretos e se o arquivo está no formato adequado.`;

        if (campanha.naoEnviarMsgParaQuemRecebeuRecente) {
          msg += `\nLembre-se que você configurou para não enviar mensagens para quem recebeu campanha recentemente. ${campanha.qtdeDiasUltimaNotificacao} dias.`;
        }
      } else {
        msg = `Não há contatos para enviar a mensagem. Verifique os filtros da campanha.\n
        ${campanha.naoEnviarMsgParaQuemRecebeuRecente ? 'Lembre-se que você configurou para não enviar mensagens para quem recebeu campanha recentemente.' : ''}`;
      }
      return res.json(Resposta.erro(msg));
    }

    new CampanhaService().atualize(campanha, empresa).then(async (erro: any) => {

      if( campanha.iniciouOEnvio() ) {
        await new CampanhaService().atualizeMensagensCampanha(empresa, campanha, listaDeContatos);
      }

      if (!erro) {
        res.json(Resposta.sucesso({id: campanha.id}))
      } else {
        res.json(Resposta.erro(erro))
      }
    });
  });
});

function leiaArquivo(uploadedFile: any, extensao: string) {
  return new Promise( (resolve, reject) => {
    if( !uploadedFile ) {
      return resolve({
        sucesso: true,
        ids: []
      });
    }

  let diretorio = Ambiente.Instance.config.caminhoImagens,
    nomeArquivo = String(`${uuidv1()}.${extensao}`),
    arquivo: string = path.join(diretorio, 'empresa', nomeArquivo);

    if(!fs.existsSync(diretorio)) {
      console.log(diretorio)
      return resolve({
        sucesso: false,
        erro: "Diretório upload de arquivo csv1 não existe"
      });
    }

    const importador = new ImportadorProdutosCSV(null);
    const erros: Array<string> = [];
    let linha = 1;
    uploadedFile.mv(arquivo).then(async (err: any) => {
      if (!err) {
        const contatos: any = [];
        // @ts-ignore
        let fileStream = fs.createReadStream(arquivo).pipe(csvParse({
          from_line: 2,
          delimiter: [",", ";", ":"]
        })).on('error', (error: Error) => {
          console.log('deu erro');
          const mensagem = error.message.replace('on line', 'na linha');
          resolve(Resposta.erro(mensagem));
        }).on('data', (row: any) => {
          linha ++;
          const idContato = row[0];

          if( isNaN(idContato) ) {
            erros.push(`Erro na linha ${linha} "${idContato}" não é um id de contato válido`);
            return;
          }

          contatos.push({
            id: idContato
          });
        }).on('end', async() => {
          if( erros.length > 0 ) {
            resolve(Resposta.erro(erros.join('\n')));
            return;
          }

          let ids = [];
          for( let contato of contatos ) {
            ids.push(contato.id);
          }

          let listaDeContatos: Array<Contato> = await  new MapeadorDeContato().listeAsync({ ids: ids });

          if( listaDeContatos.length === 0 ) {
            listaDeContatos = await  new MapeadorDeContato().listeAsync({ telefones: ids });
          }

          resolve({
            sucesso: true,
            contatos: listaDeContatos
          });
        });
      }
    });
  });
}

router.post('/', async (req , res) => {
  let file: any = null, partesNome: any = null, extensao = null;

  let dados: any = JSON.parse(req.body.data);

  if( req.files ) {
    file = req.files.file;
    partesNome = file.name.split('.');
    extensao = file.name.split('.')[partesNome.length - 1];

    delete dados.files;
  }

  leiaArquivo(file, extensao).then( (respArquivo: any) => {
    if( !respArquivo.sucesso ) {
      return res.status(400).json(Resposta.erro(respArquivo.erro));
    }

    let campanha = new Campanha();
    if( dados.temMenu ) {
      campanha.menu = dados.menu;
    }
    Object.assign(campanha, dados);

    campanha.contatos = respArquivo.contatos;

    if( campanha.origemContatos === EnumOrigemContatosCampanha.Filtro ) {
      // Criar um novo filtro com os dados corretos
      campanha.filtro = new Filtro(
        dados.filtro.id,
        campanha.empresa,
        null,  // usuário pode ser null para este caso
        dados.filtro.nome,
        dados.filtro.dados  // Isso vai passar pelo método setDados que fará a serialização correta
      );
    }

    if ( dados.horarioEnvio )
      campanha.horarioEnvio = new Date(dados.horarioEnvio);

    campanha.statusAprovacao = EnumStatusAprovacao.Pendente;

    new CampanhaService().insira(campanha).then( (erro: any) => {
      if(!erro){
        res.json(Resposta.sucesso({id: campanha.id}))
      }else{
        res.json(Resposta.erro(erro))
      }
    });
  });
});


function mudeStatusCampanha(dados: any, novoStatus: EnumStatusCampanha, res: any) {
  let campanha = new Campanha();
  Object.assign(campanha, dados);

  const mapeadorDeCampanha = new MapeadorDeCampanha();

  mapeadorDeCampanha.obtenhaCampanha({id: campanha.id}).then( (objCampanha: Campanha) => {
    objCampanha.status = novoStatus;

    mapeadorDeCampanha.atualizeStatus(objCampanha).then( async (erro: any) => {
      if( novoStatus === EnumStatusCampanha.Cancelada ) {
        await new MapeadorDeMensagemEnviada().removaMensagensPendentes(objCampanha);
      }
      if(!erro){
        res.json(Resposta.sucesso({id: campanha.id}))
      }else{
        res.json(Resposta.erro(erro))
      }
    });
  });
}

router.post('/cancelarEnvio', async (req, res) => {
  return mudeStatusCampanha(req.body, EnumStatusCampanha.Cancelada, res);
});

async function atualizeCampanha(req: any, res: any, fnAlterarObjeto: Function, fnAtualizou: Function) {
  const idCampanha = req.body.id;
  const mapeadorDeCampanha = new MapeadorDeCampanha();
  mapeadorDeCampanha.desativeMultiCliente();

  const campanha: Campanha = await mapeadorDeCampanha.obtenhaCampanha({id: idCampanha});

  if( !campanha ) {
    return res.json(Resposta.erro("Parâmetros inválidos"));
  }

  fnAlterarObjeto(campanha);

  new MapeadorDeCampanha().atualizeSync(campanha).then( (atualizou: any) => {
    fnAtualizou(campanha, atualizou)
  });
}

router.post('/aprove', async(req, res) => {
  atualizeCampanha(req, res, (campanha: Campanha) => {
    campanha.statusAprovacao = EnumStatusAprovacao.Aprovada;
  }, (campanha: Campanha, atualizou: any) => {
    res.json(Resposta.sucesso({
      msg: "Campanha aprovada com sucesso! Não se esqueça de avisar o cliente.",
      status: campanha.statusAprovacao
    }));
  });
});

router.post('/reprove', async(req, res) => {
  atualizeCampanha(req, res, (campanha: Campanha) => {
    campanha.statusAprovacao = EnumStatusAprovacao.Rejeitada;
  }, (campanha: Campanha, atualizou: any) => {
    res.json(Resposta.sucesso({
      msg: "Campanha reprovada! Não se esqueça de avisar o cliente.",
      status: campanha.statusAprovacao
    }));
  });
});

router.post('/aceitarCampanhaRede', async(req, res) => {
  atualizeCampanha(req, res, (campanha: Campanha) => {
    campanha.foiAceita = true;
    campanha.foiTestada = true;
    campanha.status = EnumStatusCampanha.Nova;
    campanha.statusAprovacao = EnumStatusAprovacao.Aprovada;
  }, (campanha: Campanha, atualizou: any) => {
    res.json(Resposta.sucesso({
      msg: "Campanha foi Aceita! Para enviar entre em contato com o suporte ou acesse acessa a campanha e clique em Enviar.",
      status: campanha.statusAprovacao
    }));
  });
});

router.post('/rejeitarCampanhaRede', async(req, res) => {
  atualizeCampanha(req, res, (campanha: Campanha) => {
    campanha.foiAceita = false;
    campanha.status = EnumStatusCampanha.Rejeitada;
  }, (campanha: Campanha, atualizou: any) => {
    res.json(Resposta.sucesso({
      msg: "Campanha foi Rejeitada e ela não será enviada.",
      status: campanha.statusAprovacao
    }));
  });
});

router.post('/continuarEnvio', async (req, res) => {
  return mudeStatusCampanha(req.body, EnumStatusCampanha.Enviando, res);
});

router.post('/pararEnvio', async (req, res) => {
  return mudeStatusCampanha(req.body, EnumStatusCampanha.ParouEnvio, res);
});

router.put('/desative', async (req: any, res) => {
  const dados: any = req.body;
  const empresa = req.empresa;

  let campanha = new Campanha();
  Object.assign(campanha, dados);

  new MapeadorDeCampanha().obtenhaCampanha({id: campanha.id}).then( (objCampanha) => {
    objCampanha.ativa = false;
    new CampanhaService().atualize(objCampanha, empresa).then( (erro: any) => {
      if(!erro){
        res.json(Resposta.sucesso({id: campanha.id}))
      }else{
        res.json(Resposta.erro(erro))
      }
    });
  });
});

router.get('/liste', async (req: any, res) => {
  const tipoEnvio = req.query.t;
  const status = req.query.s;
  const pendentes = req.query.p;
  const campanhasRede = req.query.cr;
  const mapeadorDeCampanha = new MapeadorDeCampanha(),
    query: any  = {orderBy: true};

  if( tipoEnvio === 'recorrentes' ) {
    query['tipoDeEnvio'] = 'Agendado';
  } else if( tipoEnvio === 'envio-unico' ) {
    query['tipoDeEnvio'] = 'Unico';
  }
  if( campanhasRede ) {
    query['campanhasRede'] = true;
  }

  if( pendentes ) {
    query['pendentes'] = true;
    mapeadorDeCampanha.desativeMultiCliente();
  }

  if( tipoEnvio === 'desativadas' ) {
    query['desativadas'] = true;
  }

  if( status ) {
    query['status'] = status.split(',');
  }

  console.log(query);

  const campanhas: any = await mapeadorDeCampanha.listeAsync(query).catch( (erro) => {
    console.log(erro);
  });

  campanhas.forEach( (campanha: Campanha) => {
    campanha.ajusteMenu();

    delete campanha.contatos
  });

  let total = await mapeadorDeCampanha.selecioneTotal(query);

  res.json( Resposta.sucesso(
    {
      campanhas: campanhas,
      total: total
    }
  ));
});

router.get('/envioTeste/inicie', async(req, res) => {
  const mapeadorDeCampanha = new MapeadorDeCampanha();
  const id: any = req.query.idc;
  const telefone: any = req.query.tel;

  console.log(id);

  const campanha: Campanha = await mapeadorDeCampanha.obtenhaCampanha({id: id});

  if( campanha == null ) {
    return res.json(Resposta.erro('Campanha não existe!'));
  }

  if( campanha.status === 'Enviada' ) {
    return res.json(Resposta.erro('Campanha já foi enviada!'));
  }

  const query = {};

  let contato  = await new MapeadorDeContato().selecioneSync({telefone: telefone});

  if( contato == null ) {
    contato = new Contato(-1, '', telefone);
  }

  const contatos = [contato];

  new VariaveisDeRequest().obtenhaEmpresaLogada().then( async (empresa: Empresa) => {
    const tarefaEnvioCampanha = new TarefaEnvioCampanha(contatos.length);

    res.json({
      sucesso: true,
      enviando: true,
      status: tarefaEnvioCampanha
    });

    await new CampanhaService().facaEnvioTeste(empresa, campanha, contatos, tarefaEnvioCampanha);
  });
});


router.get('/marqueComoTestada/:idc', async(req, res) => {
  const mapeadorDeCampanha = new MapeadorDeCampanha();
  const id: any = req.params.idc;

  console.log(id);

  const campanha: Campanha = await mapeadorDeCampanha.obtenhaCampanha({id: id});

  if( campanha == null ) {
    return res.json(Resposta.erro('Campanha não existe!'));
  }

  if( campanha.status === 'Enviada' ) {
    return res.json(Resposta.erro('Campanha já foi enviada!'));
  }

  campanha.foiTestada = true;

  new MapeadorDeCampanha().atualizeSync(campanha).then( (atualizou: any) => {
    res.json({
      sucesso: true
    });
  }).catch( (erro: any) => {
    res.json({
      sucesso: false,
    });
  });
});

router.post('/inicieEnvio/:idc', async(req: any, res: any) => {
  const mapeadorDeCampanha = new MapeadorDeCampanha();

  const id: any = req.params.idc;
  const dados: any = req.body;
  console.log(id);

  const campanha: Campanha = await mapeadorDeCampanha.obtenhaCampanha({id: id});

  if( campanha == null ) {
    return res.json(Resposta.erro('Campanha não existe!'));
  }

  if( campanha.status === 'Enviada' ) {
    return res.json(Resposta.erro('Campanha já foi enviada!'));
  }

  let qtdeContatos = null;

  if( dados.qtdeContatos ) {
    qtdeContatos = +dados.qtdeContatos;
  }

  new VariaveisDeRequest().obtenhaEmpresaLogada().then( async (empresa: Empresa) => {
    const listaContatos = await campanha.carregueContatos(empresa, {}, true);
    const tarefaEnvioCampanha = new TarefaEnvioCampanha(campanha.contatos.length);

    new CampanhaService().facaEnvio(empresa, campanha, listaContatos, tarefaEnvioCampanha).then( () => {
      res.json(Resposta.sucesso({
        enviando: true,
        status: tarefaEnvioCampanha
      }));
    });
  });
});

router.get('/:id', async (req: any, res: any) => {
  const mapeadorDeCampanha = new MapeadorDeCampanha();
  const id: any = req.params.id;
  const empresa: Empresa = req.empresa;

  const inicio = new Date();
  const campanha: Campanha = await mapeadorDeCampanha.obtenhaCampanha({id: id}, false);

  if( campanha.origemContatos === EnumOrigemContatosCampanha.Arquivo ) {
    await campanha.carregueContatos(empresa);
  }

  if( campanha && campanha.filtro ) {
    campanha.filtro = campanha.filtro.getDados();
  }

  campanha.ajusteMenu();

  console.log((new Date().getTime() - inicio.getTime()) / 1000.0);
  res.json({
    sucesso: true,
    data: campanha
  });
});

router.get('/:id/qtdeLidas', async (req, res) => {
  const mapeadorDeCampanha = new MapeadorDeCampanha();
  const id: any = req.params.id;

  // tslint:disable-next-line:radix
  const qtdeLidas = await mapeadorDeCampanha.obtenhaQtdeLidas({id: parseInt(id)});

  res.json(Resposta.sucesso(qtdeLidas))
});

router.get('/:id/mensagens', async (req, res) => {
  const mapeador = new MapeadorDeMensagemEnviada();
  const id: any = req.params.id || -1;
  const inicio = req.query.p || 1;
  const total = req.query.t || 10;

  let query: any = {idCampanha: id, inicio: Number(inicio), total: Number(total)};

  if(req.query.recebeu === 'true')
     query.soQueRecebeu = true;

  console.log(req.query);
  console.log(query);

  let mensagens = await mapeador.listeAsync(query)
  let qtdetotal = await mapeador.selecioneTotal(query)

  res.json(Resposta.sucesso({mensagens: mensagens, total: qtdetotal}));
});

router.get('/statusEnvio/:idc', async(req, res) => {
  const mapeadorDeCampanha = new MapeadorDeCampanha();
  const id: any = req.params.idc;

  console.log(id);
  const campanha: Campanha = await mapeadorDeCampanha.obtenhaCampanha({id: id});

  if( campanha == null ) {
    return res.json(Resposta.erro('Campanha não existe!'));
  }

  client.get('tarefaenvio_' + campanha.id, (err: any, reply: string) => {
    if( err ) {
      return Resposta.erro(err.message);
    }

    console.log(reply);

    const dados = JSON.parse(reply);
    if( dados && dados.contatoAtual ) {
      delete dados.contatoAtual.empresa
    }

    res.json({
      sucesso: true,
      data: dados
    });
  });
});

export const CampanhaController: Router = router;
