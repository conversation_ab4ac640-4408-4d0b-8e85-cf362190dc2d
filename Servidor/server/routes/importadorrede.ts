import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {ImportardorTrendFoods} from "../lib/integracao/ImportardorTrendFoods";
import {Produto} from "../domain/Produto";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {MapeadorDeCategoria} from "../mapeadores/MapeadorDeCategoria";
import {ImportadorProduto} from "../lib/integracao/ImportadorProduto";
import {MapeadorDePlano} from "../mapeadores/MapeadorDePlano";
import {TipoDePontuacao} from "../domain/TipoDePontuacao";
import {Plano} from "../domain/Plano";
import {TipoDePontuacaoCashback} from "../domain/TipoDePontuacaoCashback";
import {EnumTipoDeCartao} from "../lib/emun/EnumTipoDeCartao";
import {MapeadorDeTipoDePontuacao} from "../mapeadores/MapeadorDeTipoDePontuacao";
import {IntegracaoPedidoFidelidade} from "../domain/IntegracaoPedidoFidelidade";
import {Atividade} from "../domain/Atividade";
import {MapeadorDeAtividade} from "../mapeadores/MapeadorDeAtividade";
import {MapeadorDeIntegracaoPedidoFidelidade} from "../mapeadores/MapeadorDeIntegracaoPedidoFidelidade";
import {Brinde} from "../domain/obj/Brinde";
import {MapeadorDeBrinde} from "../mapeadores/MapeadorDeBrinde";
import {TrendFoodsTarefa} from "../lib/integracao/TrendFoodsTarefa";
import {CatalogoModeloService} from "../lib/integracao/CatalogoModeloService";
import {MapeadorDeCatalogo} from "../mapeadores/MapeadorDeCatalogo";
import {MapeadorDeDisponibilidade} from "../mapeadores/MapeadorDeDisponibilidade";
import {RotaGuard} from "../lib/permissao/RotaGuard";


const router: Router = Router();

async function temIntegracaoAtiva(req: any, res: any, next: any) {
  let empresa: any = await new MapeadorDeEmpresa().selecioneSync(req.params.idEmpresa);

  req.empresaRequest = empresa;

  if (!empresa || !empresa.integracaoDelivery) {
    res.json(Resposta.erro('Não tem integração ativa'))
  } else {
    return next();
  }
}

router.get('/:idEmpresa/catalogo/sincronize', temIntegracaoAtiva, async (req: any, res ) => {
  let produtosSite =       req.session.produtosSite;
  let sincronizarPrecos = req.query.precos;
  let sincronizarImagens = req.query.imagens;

  if(!produtosSite)
    return res.json(Resposta.erro("Nenhum produto do site foi encontrado"))

  let empresa =   req.empresaRequest;

  let respostaSincronizacao =
    await new TrendFoodsTarefa().sincronizeEmpresa(empresa, produtosSite.novosProdutos, produtosSite.produtosComSku,
      sincronizarPrecos, sincronizarImagens);

  res.json(Resposta.sucesso(respostaSincronizacao))
})

router.get('/:idEmpresa/catalogo/modelo/sincronize', temIntegracaoAtiva, async (req: any, res ) => {
  let sincronizarPrecos = req.query.precos != null;
  let sincronizarImagens = req.query.imagens != null;

  let empresa: any = await new MapeadorDeEmpresa().selecioneSync( req.params.idEmpresa);

  if(empresa.modeloCatalogoDaRede){

    let respostaSincronizacao =   await new CatalogoModeloService().sincronizeEmpresa(empresa, sincronizarPrecos, sincronizarImagens);

    res.json(Resposta.sucesso(respostaSincronizacao))
  } else {
    res.json(Resposta.erro('Nenhum catalogo modelo configurado na empresa'))
  }
})


//Tabelas Ecleticas modelos
//https://localhost:4200/importadorrede/catalogo/1465/replique/423  ecletica  tabela 1
//https://localhost:4200/importadorrede/catalogo/1466/replique/608   ecletica  tabela 2
//https://localhost:4200/importadorrede/catalogo/1467/replique/639   ecletica  tabela 3
//https://localhost:4200/importadorrede/catalogo/1468/replique/675   ecletica  tabela 5
//https://localhost:4200/importadorrede/catalogo/1469/replique/618   ecletica  tabela 6
//https://localhost:4200/importadorrede/catalogo/1470/replique/717  ecletica  tabela 7


//https://localhost:4200/importadorrede/catalogo/1471/replique/816   gcom  tabela 1
//https://localhost:4200/importadorrede/catalogo/1472/replique/661   gcom  tabela 2
//https://localhost:4200/importadorrede/catalogo/1473/replique/716   gcom  tabela 3
//https://localhost:4200/importadorrede/catalogo/1474/replique/670   gcom  tabela 5
//https://localhost:4200/importadorrede/catalogo/1475/replique/568   gcom  tabela 6
//https://localhost:4200/importadorrede/catalogo/1476/replique/897   gcom  tabela 7



//Tabelas Ecleticas modelos gendai
//https://localhost:4200/importadorrede/catalogo/1716/replique/707  ecletica  tabela 1

//Tabelas gcom modelos gendai
//https://localhost:4200/importadorrede/catalogo/1719/replique/787  gcom  tabela 1
//https://localhost:4200/importadorrede/catalogo/1720/replique/1430  gcom  tabela 2

router.get('/catalogo/:destino/replique/:modelo',  async (req: any, res ) => {
  let catalogoDestino = await new MapeadorDeCatalogo().selecioneSync(req.params.destino);

  await new MapeadorDeProduto(catalogoDestino).removaTodosProdutos();
  await new MapeadorDeCategoria(catalogoDestino).removaTodasCategorias();
  await new MapeadorDeDisponibilidade(catalogoDestino).removaTodas();


  let catalogoModelo = await new MapeadorDeCatalogo().selecioneSync(req.params.modelo);

  let respostaSincronizacao = await new CatalogoModeloService().repliceCatalogo(catalogoDestino, catalogoModelo);

  res.json(Resposta.sucesso(respostaSincronizacao))

})

router.put('/catalogo/:id/produto/sincronize', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const produto: any = req.body;

  let catalogo = await new MapeadorDeCatalogo().selecioneSync(req.params.id);

  //sincronizeProdutoDaRede
  new ImportadorProduto().sincronizeProdutos(catalogo, [produto]).then( async()   => {
    await new MapeadorDeProduto(catalogo).removaCacheProdutos();
    res.json(Resposta.sucesso())
  }).catch( (erro) => {
    res.json(Resposta.erro(erro.message ? erro.message : erro))
  })

});


router.get('/:idEmpresa/produtos/novos', temIntegracaoAtiva, async (req: any, res ) => {
  let empresa =   req.empresaRequest;

  if(!empresa.integracaoDelivery.unidadeChina && !empresa.integracaoDelivery.unidadeGendai)
    return res.json(Resposta.erro('Nenhuma unidade china/gendai configurada na integração.'))

  new ImportardorTrendFoods(empresa.integracaoDelivery).obtenhaProdutos()
    .then( async (respostaProdutos: any) => {
      let novosProdutos: any = [];
      let categorias: any = [];
      let produtosChina: any = respostaProdutos.produtos;
      let produtosComSku: any = respostaProdutos.produtosComSku;

      produtosChina.forEach( (produtoChina: Produto) => {
        if(produtoChina.categoria){
          if(!categorias.find( (categoria:  any) =>   produtoChina.categoria.nome === categoria.nome  )){
            categorias.push(produtoChina.categoria)
          }
        }
        novosProdutos.push(produtoChina)
      })

      req.session.produtosSite = { novosProdutos: novosProdutos, categorias: categorias, produtosComSku: produtosComSku}

      res.json(Resposta.sucesso({ novosProdutos: novosProdutos, categorias: categorias,
        produtosAtualizar: [], produtosRemovidos: [] }))

    });
})

function insiraPlanoIntegracao(empresa: any, plano: any , tipoDePontuacao: any){
  return new Promise( ( resolve: any) => {
    new MapeadorDeEmpresa().transacao(async  (conexao: any, commit: Function) => {
      await  new MapeadorDeTipoDePontuacao().insiraSync(tipoDePontuacao);
      plano.tipoDePontuacao = tipoDePontuacao;
      await new MapeadorDePlano().insiraSync(plano);

      let atividade = new Atividade(null, 'Fez um pedido', 0, plano, empresa, 0, 0.15);
      atividade.integrada = true;

      let integracaoPedidoFidelidade = new IntegracaoPedidoFidelidade(empresa, plano, atividade, false, false);

      await new MapeadorDeAtividade().insiraGraph(atividade);
      await new MapeadorDeIntegracaoPedidoFidelidade().insiraGraph(integracaoPedidoFidelidade);

      let brinde = new Brinde(null, "Resgate", null, 0, plano, 'um');

      await new MapeadorDeBrinde().insiraGraph(brinde);

      new MapeadorDeEmpresa().removaDasCaches(empresa)

      commit( () => { resolve()  })

    })
  })

}

//chinainbox
router.get('/rede/:rede/cache/recarregue', async (req, res) => {
  let empresas = await new MapeadorDeEmpresa().listeEmpresasRede({redeDaEmpresa: req.params.rede});
  let mapeador = new MapeadorDeEmpresa();
  for(let i = 0; i < empresas.length; i++) {
    let empresa = empresas[i];
    console.log(String(`Removendo ${empresa.id} - ${empresa.nome}`));
    mapeador.removaDasCaches(empresa);
  }

  for(let i = 0; i < empresas.length; i++) {
    let empresa = empresas[i];
    console.log(String(`Selecionando ${empresa.id} - ${empresa.nome}`));

    await mapeador.selecioneSync(empresa.id);
  }

  res.json(Resposta.sucesso({total: empresas.length, rede: req.params.rede }))
})

router.get('/chinna/fidelidade/gerePlano', async (req, res) => {
  let empresas = await new MapeadorDeEmpresa().listeEmpresasRede({redeDaEmpresa: 'chinainbox'});

  let empresasKohalas =  await new MapeadorDeEmpresa().listeEmpresasRede({kohala: true});

  empresasKohalas.forEach((e: any) => empresas.push(e));

  let resposta: any = { novos: [], existentes: [] , erros: [], ignorados: []}
  let contexto: any = require('domain').active.contexto;

  let naoGerar = [657] //China In Box Caiçara


  for(let i = 0; i < empresas.length; i++){
    let empresa = empresas[i];
    console.log(String(`${empresa.id} - ${empresa.nome}`));
    contexto.idEmpresa = empresa.id;
    contexto.empresa  = empresa;

    let gendai = empresa.nome.toUpperCase().indexOf('GENDAI') >= 0;

    if(naoGerar.indexOf(empresa.id) === -1) {
      let planoExistente = await  new MapeadorDePlano().selecioneSync({ idEmpresa: empresa.id, tipoAcumulo: 'Reais'});

      if(!planoExistente && !empresa.integracaoPedidoFidelidade){
        let tipoDePontuacao: TipoDePontuacao = new TipoDePontuacaoCashback();
        let plano: Plano = new Plano();

        plano.nome = String(`Fidelidade ${empresa.nome}`)
        plano.tituloCartao = String(`Cartão ${plano.nome}`)
        plano.tipoDeAcumulo = EnumTipoDeCartao.Reais;
        plano.valorMinimoPontuar = gendai ?  50 : 60;
        plano.dataFimAcumulo  = new Date('2021-11-30 00:00:00')
        plano.vencimento = new Date('2021-12-31 00:00:00');
        plano.valorMinimoResgate = 15

        let erro = plano.valide();

        if(!erro) {
          await insiraPlanoIntegracao(empresa, plano, tipoDePontuacao)
          resposta.novos.push({id: empresa.id, nome: empresa.nome})
        } else {
          resposta.erros.push(erro)
        }
      } else {
        resposta.existentes.push({id: empresa.id, nome: empresa.nome})
      }
    } else {
      resposta.ignorados.push({id: empresa.id, nome: empresa.nome})
    }
  }

  res.json(Resposta.sucesso(resposta))
});


export const ImportadorRedeController: Router = router;
