import {Router} from 'express';
import {MapeadorDeContato} from '../mapeadores/MapeadorDeContato';
import {MapeadorDeCartao} from '../mapeadores/MapeadorDeCartao';
import {Contato} from '../domain/Contato';
import {ContatoService} from '../service/ContatoService';
import {Resposta} from '../utils/Resposta';
import {PontuacaoRegistrada} from "../domain/PontuacaoRegistrada";
import {MapeadorDeAtividade} from "../mapeadores/MapeadorDeAtividade";
import {DTOResumoSocios} from "../lib/dto/DTOResumoSocios";
import {MapeadorDePontuacaoRegistrada} from "../mapeadores/MapeadorDePontuacaoRegistrada";
import {Cartao} from "../domain/Cartao";
import {VariaveisDeRequest} from "../service/VariaveisDeRequest";
import {FiltroTela} from "../utils/FiltroTela";
import {MapeadorDeCampanha} from "../mapeadores/MapeadorDeCampanha";
import {Notificacao} from "../domain/Notificacao";
import {CartaoService} from "../service/CartaoService";
import {MapeadorDeEndereco} from "../mapeadores/MapeadorDeEndereco";
import {Endereco} from "../domain/delivery/Endereco";
import {Empresa} from "../domain/Empresa";
import {Modulo} from '../domain/Modulo';
import {Campanha} from "../domain/Campanha";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {MapeadorDeContatoBloqueado} from "../mapeadores/MapeadorDeContatoBloqueado";
import {ContatoBloqueado} from "../domain/ContatoBloqueado";
import {RotaGuard} from "../lib/permissao/RotaGuard";
import { PhoneNumberUtil } from 'google-libphonenumber';
import {Ambiente} from "../service/Ambiente";
import {RespostaEncurtarLinks} from "../utils/RespostaEncurtarLinks";
import {CacheService} from "../service/CacheService";
import {Pedido} from "../domain/delivery/Pedido";

const moment  = require('moment');
const _  = require('underscore');
const router: Router = Router();
const uuidv1 = require('uuid/v1');

function estaLogado(req: any, res: any, next: any){
  if(!req.user)
    return res.json(Resposta.erro('Faça login para realizar a operação'))

  return next();
}

router.get('/status-contatos', estaLogado, async (req: any, res: any) => {
  try {
    const empresa: Empresa = req.empresa;
    const mapeador = new MapeadorDeContato();
    const filtro: any = {
      idEmpresa: empresa.id
    };

    if (req.query.like) {
      filtro.like = req.query.like;
    }

    if (empresa.rede) {
      filtro.rede = empresa.rede;
      filtro.redeId = empresa.idRede;
    }

    const resumo = await mapeador.listeStatusContatos(filtro);

    console.log('resumo: ', resumo);

    res.json(Resposta.sucesso(resumo));
  } catch (erro) {
    console.error('Erro ao obter resumo de contatos:', erro);
    res.status(500).json({ erro: 'Erro ao obter resumo de contatos' });
  }
});


router.get('/' , async (req: any, res) => {
  const empresa: Empresa = req.empresa;
  const inicio = req.query.i || 0,
        total = req.query.t || 10,
        query: any = { inicio: parseInt(inicio, 10), total: parseInt(total, 10) };
  const resultadoCompleto = req.query.rc;
  const idCampanha = req.query.idc;
  let campanha: Campanha = null;
  const origemContatos = req.query.origemContatos;
  const envio = Boolean(req.query.envio);
  const qtdeDiasUltimaNotificacao = req.query.qtdeDiasUltimaNotificacao;
  const naoEnviarMsgParaQuemRecebeuRecente = Boolean(req.query.naoEnviarMsgParaQuemRecebeuRecente);

  if( campanha && !origemContatos ) {
    res.json(Resposta.erro('Faltando parametro oc.'));
    return;
  }

  if( idCampanha ) {
    campanha = await new MapeadorDeCampanha().selecioneSync({id: idCampanha});
  }

  const temModuloPedidos = empresa.possuiModulo('pedidos');

  if( temModuloPedidos ) {
    query.temModuloPedidos = true;
  }

  let contatos: Array<any> = null;
  let qtdeContatos = null;

  if( campanha ) {
    campanha.origemContatos = origemContatos;
    if( qtdeDiasUltimaNotificacao ) {
      campanha.qtdeDiasUltimaNotificacao = qtdeDiasUltimaNotificacao;
      campanha.naoEnviarMsgParaQuemRecebeuRecente = naoEnviarMsgParaQuemRecebeuRecente;
    }
    contatos = await campanha.carregueContatos(empresa, {}, envio);

    qtdeContatos = contatos.length;
    if( query.total && contatos.length > query.total ) {
      contatos = contatos.slice(query.inicio, query.inicio + query.total);
    }
  } else {
    if (campanha && campanha.filtro) {
      let paramsFiltro = campanha.filtro.getParamsFiltro();
      let filtro = FiltroTela.getQuerySQL(paramsFiltro)
      Object.assign(query, filtro);
    } else {
      let filtro = FiltroTela.getQuerySQL(req.query)
      Object.assign(query, filtro);
    }

    if( campanha && campanha.naoEnviarMsgParaQuemRecebeuRecente ) {
      query.enviadasNosultimosDias = campanha.qtdeDiasUltimaNotificacao;
    }

    contatos  = await new MapeadorDeContato().listeAsync(query);
    if( query.total && contatos.length > query.total ) {
      contatos = contatos.slice(query.inicio, query.inicio + query.total);
    }
    if( resultadoCompleto ) {
      qtdeContatos = await new MapeadorDeContato().selecioneTotal(query);

      if( campanha && campanha.limitarQtdeContatos && qtdeContatos > campanha.qtdeContatosEnviarMsg ) {
        qtdeContatos = campanha.qtdeContatosEnviarMsg;
      }
    }
  }

  console.log(query);

  for( let i = 0; i < contatos.length; i++ ) {
    let contato = contatos[i];

    contato.selos = [];
    const notificacao = new Notificacao();

    if( campanha ) {
      let link = '';
      let variavel = new VariaveisDeRequest()
      link = variavel.obtenhaUrlRaiz(campanha.empresa) + contato.obtenhaLinkCartao();
      let linkCardapio = empresa.obtenhaLinkLoja(Ambiente.Instance.producao)// variavel.obtenhaUrlCardapio(campanha.empresa)
      notificacao.mensagem = campanha.mensagem;
      const respostaEncurtarLinks: RespostaEncurtarLinks = (await notificacao.processe(campanha.empresa, contato,
        {linkCartao: link, linkCardapio: linkCardapio}));

      if( respostaEncurtarLinks ) {
        contato.mensagemProcessada = respostaEncurtarLinks.mensagemFinal;
      } else {
        contato.mensagemProcessada = 'Não vai gerar mensagem.';
      }
    }
  }

  if( !resultadoCompleto ) {
    return res.json(Resposta.sucesso(contatos));
  }

  return res.json(Resposta.sucesso({
    contatos: contatos,
    total: qtdeContatos
  }));
});

router.post('/', RotaGuard.alterarContatos, async (req, res) => {
  const dados = req.body;
  const contato = new Contato(dados.id, dados.nome, dados.telefone, dados.sexo,
                              dados.dataNascimento, dados.cpf, dados.email);
  contato.ultimaVisita = new Date();

  new ContatoService().salve(contato).then(resp => {
    res.json(Resposta.sucesso({id: contato.id}));
  }).catch(erro => {
    res.json(Resposta.erro(erro));
  });
});

router.put('/', RotaGuard.alterarContatos, async (req: any, res) => {
  const dados = req.body;
  const contato = new Contato(dados.id, dados.nome, dados.telefone, dados.sexo, dados.dataNascimento,
                              dados.cpf, dados.email, dados.codigoPais);

  contato.codigoPagarme = dados.codigoPagarme;

  const contatoService = new ContatoService();

  contatoService.valide(contato, false, true).then( (erro: any) => {
    if( erro ) {
      res.json(Resposta.erro(erro.mensagem || erro));
      return;
    }
    contatoService.atualize(contato, req.empresa).then(resp => {
      res.json(Resposta.sucesso());
    }).catch(err => {
      res.json(Resposta.erro(err));
    });
  });
});

router.put('/endereco/:id', estaLogado, async (req, res) => {
  const dados: any = req.body;

  let endereco: Endereco = await new MapeadorDeEndereco().selecioneSync( dados.id);

  if(!endereco)
    return res.json(Resposta.erro('endereçao naõ econtrado: ' + dados.id));

  let enderecoAtualizado =  new Endereco(dados.id, dados.contato, dados.cidade, dados.cep,
    dados.logradouro, dados.complemento, dados.bairro,
    dados.numero, dados.descricao,  endereco.localizacao);

  if(endereco.cep  !== enderecoAtualizado.cep || endereco.bairro !==  enderecoAtualizado.bairro)
    enderecoAtualizado.localizacao = ''

  enderecoAtualizado.zonaDeEntrega = dados.zonaDeEntrega;
  enderecoAtualizado.pontoDeReferencia = dados.pontoDeReferencia;

  await new MapeadorDeEndereco().atualizeSync(enderecoAtualizado);

  res.json(Resposta.sucesso({enderecoCompleto: enderecoAtualizado.obtenhaEnderecoCompleto()}))
});

router.delete('/endereco/:id', estaLogado, async (req, res) => {

  let endereco: Endereco = await new MapeadorDeEndereco().selecioneSync( req.params.id);

  if(!endereco)
    return res.json(Resposta.erro('endereçao naõ econtrado: ' +  req.params.id));

  await new MapeadorDeEndereco().removaAsync(endereco);

  res.json(Resposta.sucesso())

});



router.get('/resumoVendas/:referencia', async(req: any, res) => {
    let query: any   = {};

    query[req.params.referencia] = true;

    if(req.query.r) query.rede = req.empresa.dadosRede.grupo

    new MapeadorDePontuacaoRegistrada().listeResumo(query).then( (dados: any) => {

    if(req.params.referencia === 'dia'){
       for(let data = moment().add(-14, "d"); data <= moment(); data = data.add(1, "d")){
           const dia: number =  Number(data.format('DD'));

         if(!_.findWhere(dados, { dia: dia} )){
             dados.push({total: 0,  pontos: 0, ano: Number(data.format('YYYY')), mes: Number(data.format('MM')), dia: dia})
           }
       }
    }

    for ( let i = 0; i < dados.length; i++ ) {
      const resumo = dados[i];
      resumo.data =   moment(new Date(resumo.ano, resumo.mes - 1, resumo.dia)).format('YYYYMMDD')
      resumo.mesFormatado = (resumo.mes + '').padStart(2, '0') + '/' + resumo.ano;
    }

    res.json(Resposta.sucesso(_.sortBy(dados, 'data')));

  }).catch( (erro: any) => {
    res.json(Resposta.erro(erro));
  });
});

router.get('/autocomplete/:telefone', async (req, res) => {
  let telefone = req.params.telefone;

  const mapeadorDeContato = new MapeadorDeContato();

  telefone = '%' + telefone + '%';

  const lista = await mapeadorDeContato.listeAsync({autocomplete: true, telefone: telefone, inicio: 0 , total: 10});

  const contatos = [];

  for( let i = 0; i < lista.length; i++ ) {
    const contato = lista[i];

    contatos.push({
      id: contato.id,
      nome: contato.nome,
      telefone: contato.telefone,
      status: contato.status,
      codigoPais: contato.codigoPais
    });
  }

  if ( lista ) {
    res.json( Resposta.sucesso( contatos ));
  } else {
    res.json( Resposta.sucesso([]));
  }
});

function extrairNumeroTelefone(numero: string): [string, string] | null {
  if(numero === "+5500000000000")
    return ["+55", "00000000000"]

  const phoneUtil = PhoneNumberUtil.getInstance(); // Instancia o objeto phoneUtil
  try {
    const numeroObj = phoneUtil.parse(numero);

    if(!numeroObj)
      return null;
    const codigoPais = numeroObj.getCountryCode();
    const telefone = numeroObj.getNationalNumber();
    return ["+" + codigoPais, telefone.toString()];
  } catch (e) {
    console.error('Número de telefone inválido:', e);
    return null;
  }
}

router.get('/:telefoneCompleto', estaLogado, async (req: any, res: any) => {
  const telefoneCompleto = req.params.telefoneCompleto;
  const resultado = extrairNumeroTelefone(telefoneCompleto);
  if (resultado) {
    const [codigoPais, telefone] = resultado;
    try {
      const mapeadorDeContato = new MapeadorDeContato(); // Instancia o objeto MapeadorDeContato
      let contato: any = await mapeadorDeContato.selecioneSync({ codigoPais: codigoPais, telefone: telefone });

      if(contato){
          let codigoValidacaoPendente = await CacheService.obtenhaCodigoValidacao(contato)

          if(codigoValidacaoPendente) contato.codigoValidacaoPendente  = codigoValidacaoPendente.idMensagem;
      }

      res.json(Resposta.sucesso(contato));
    } catch (e) {
      console.error('Erro ao buscar o contato:', e);
      res.json(Resposta.erro('Erro ao buscar o contato'));
    }
  } else {
    res.json(Resposta.erro('Número de telefone inválido: ' + telefoneCompleto));
  }
});

router.get('/cpf/:cpf', estaLogado, async (req: any, res) => {
  const cpf = req.params.cpf;

  const mapeadorDeContato = new MapeadorDeContato();

  const contato: Contato = await mapeadorDeContato.selecioneSync({cpf: cpf});

  res.json( Resposta.sucesso(contato));
});

router.get('/id/:id', estaLogado, async (req: any, res) => {
  const contato: Contato = await new MapeadorDeContato().selecioneSync( req.params.id);

  res.json( Resposta.sucesso(contato));
});

router.get('/resumo/liste', async (req: any, res) => {
  const mapeadorDeContato = new MapeadorDeContato(),
    query: any  = {};

  let rede: string = req.query.r ?
    (req.empresa.dadosRede ? req.empresa.dadosRede.grupo : null ) : null;

  let filtro = FiltroTela.getQuerySQL(req.query, rede)

  Object.assign(query, filtro);

  if(Object.keys(query).length === 0)
     query.referencia =  moment().format('YYYYMM');

  const resumosPorTipo: any = await mapeadorDeContato.listeResumo(query),
        resumo: any = new DTOResumoSocios( resumosPorTipo );

  resumo.total = await mapeadorDeContato.selecioneTotal(query);

  res.json( Resposta.sucesso(resumo));
})

router.get('/resumo/liste/:proximo', async (req, res) => {
});

router.delete('/:id', async(req: any, res) => {
  let id = req.params.id;

  if(!id)
    return res.json(Resposta.erro('É necessário informar o id do contato a ser removido'));

  await   new MapeadorDeContato().removaContato({id: id} as Contato);

  res.json(Resposta.sucesso());

})

router.post('/:id/ativar', async(req: any, res) => {
  let id = req.params.id;
  let ativar = req.body.ativar;

  if(!id)
    return res.json(Resposta.erro('É necessário informar o id do contato a ser removido'));

  const mapeadorDeContato = new MapeadorDeContato();

  mapeadorDeContato.selecioneSync({id: id} as Contato).then((contato: Contato) => {
    if( !contato ) {
      return res.json({
        sucesso: false,
        data: 'Contato não encontrado.'
      });
    }

    contato.desativarMensagensDeMarketing = ativar;

    mapeadorDeContato.atualizeSync(contato).then( () => {
      let mensagem = 'Contato não receberá mais mensagens de marketing.';

      if( !contato.desativarMensagensDeMarketing ) {
        mensagem = 'Contato voltará a receber mensagens de marketing.';
      }

      res.json({
        sucesso: true,
        data: mensagem
      });
    }).catch( () => {
      res.json({
        sucesso: false,
        data: 'Não foi possível atualizar'
      });
    });
  });
});

router.post('/novaPontuacao', RotaGuard.pontuarFidelidade, async (req: any, res) => {
  const parametros: any = req.body,
    idCartao = Number(parametros.ic);

  if(!parametros.ic) return res.json(Resposta.erro('Cartão não informado'))

  const operador = req.user;

  let idsAtividades: any = parametros.idas ;

  if(!idsAtividades)  return  res.json(Resposta.erro('Atividade é obrigatória.'));

  idsAtividades   = parametros.idas.split(',' ) ;

  let cartao: Cartao = await new MapeadorDeCartao().selecioneSync({ id: idCartao });

  if(!cartao)
    return res.json(Resposta.erro('Cartão não identificado: ' + idCartao))

  const atividades = await new MapeadorDeAtividade().listeAsync({ ids: idsAtividades });
  const pontuacaoRegistrada = new PontuacaoRegistrada();

  pontuacaoRegistrada.valor = parametros.valor;
  pontuacaoRegistrada.pontosInformado = parametros.pontosInformado;
  pontuacaoRegistrada.cartao = cartao;
  pontuacaoRegistrada.atividades = atividades;
  pontuacaoRegistrada.cartao.empresa = req.empresa;
  pontuacaoRegistrada.codigo = uuidv1();
  pontuacaoRegistrada.operador =  req.user;
  pontuacaoRegistrada.referenciaExterna =  parametros.referenciaExterna;
  pontuacaoRegistrada.dataVencimento = cartao.plano.obtenhaDataVencimento();
  pontuacaoRegistrada.empresa = req.empresa;

  try {
    const codigoResgate: string =
      await new CartaoService().salvePontuacao(operador, pontuacaoRegistrada,  parametros.pontos,  parametros.cashback);

    res.json(Resposta.sucesso({
      id: pontuacaoRegistrada.id,
      novaPontuacao: pontuacaoRegistrada.cartao.pontos,
      nome: pontuacaoRegistrada.cartao.contato.nome,
      tipo: pontuacaoRegistrada.cartao.plano.tipoDeAcumulo,
      codigo: pontuacaoRegistrada.codigo,
      codigoResgate: codigoResgate
    }));
  } catch (erro) {
    res.json(Resposta.erro(erro));
  }
});

router.get('/vencidos/dias/:qtdeDia', async (req: any, res: any) => {
  let pontuacoesVencidas: any = await new MapeadorDePontuacaoRegistrada().listePontuacoesAhVencerAhDias(Number(req.params.qtdeDia));

  res.json(Resposta.sucesso({total: pontuacoesVencidas.length, pontuacoes: pontuacoesVencidas}));
})

router.get('/vencidos/calcule', async (req: any, res: any) => {
  let idCartao: number;
  if(req.query.cid) idCartao = Number(req.query.cid);

  let pontuacoesVencidas: any = await new MapeadorDePontuacaoRegistrada().listePontuacoesVencidas(idCartao);

  let cartaoService = new CartaoService();

  for(let i = 0; i < pontuacoesVencidas.length; i++){
    let contexto = require('domain').active.contexto;
    let pontuacoes: any = pontuacoesVencidas[i].pontuacoes;
    let cartao = pontuacoesVencidas[i].cartao;

    contexto.idEmpresa = cartao.empresa.id;
    contexto.empresa  = cartao.empresa;

    await cartaoService.registrePontuacaoVencida(cartao, pontuacoes);
  }

  res.json( Resposta.sucesso(pontuacoesVencidas.length) );
})

async function insiraPontosPedido(empresa: any, pedido: any){
  return new Promise( async resolve => {
    let mapeador = new MapeadorDePedido();
    console.log(String(`Pedido ${pedido.id} pontos calculados: ${pedido.pontosGanhos}`))
    mapeador.transacao( async (conexao: any, commit: any) => {
      await mapeador.atualizePontosGanhos(pedido);
      if(pedido.pago)
        await   new CartaoService().salvePontuacaoAutomatica(pedido, empresa)

      commit( () => {resolve(''); })
    })
  })
}

router.get('/pedidos/recalcular/pontuacoes', async (req: any, res: any) => {
  let empresa: any  = await new MapeadorDeEmpresa().selecioneSync(req.empresa.id);

  if(empresa.integracaoPedidoFidelidade){
    let resposta: any = { pedidos: []};
    let integracao: any = empresa.integracaoPedidoFidelidade;

    let pedidos: any = await new MapeadorDePedido().listeAsync({recalcularPontos: true});

    console.log('total pedidos recalcular: ' + pedidos.length)

    for( let i = 0; i < pedidos.length; i++){
      let pedido: Pedido = pedidos[i];

      if(!pedido.pontosGanhos){
        await pedido.calculePontuacaoFidelidade(integracao)

        if(pedido.pontosGanhos){
          await insiraPontosPedido(empresa, pedido)
          resposta.pedidos.push({id: pedido.id, horario: pedido.horario, pontos: pedido.pontosGanhos, pago: pedido.pago});
        }
      }
    }
    res.json(resposta)
  } else {
    res.json(Resposta.erro('Nenhum plano integrado'))
  }
})

router.get('/gendai/pedidos/calculePontos', async (req: any, res: any) => {
  let resposta: any = [], mapeadorEmpresa = new MapeadorDeEmpresa();

  let empresasGendai =  await  mapeadorEmpresa.listeEmpresasRede({lojasGendaiEcletica: true});

  for(let i = 0; i < empresasGendai.length; i++){
    let empresa: any = empresasGendai[i];
    let contexto = require('domain').active.contexto;

    contexto.idEmpresa = empresa.id;
    contexto.empresa  = empresa

    let pedidosPeriodo = await new MapeadorDePedido().listeAsync({naoPontuados: true});

    if(pedidosPeriodo.length){
      let empresaCompleta = await mapeadorEmpresa.selecioneSync(empresa.id);
      let respostaEmpresa: any = { nomeEmpresa: empresa.nome, pedidos: []};
      if(empresaCompleta.integracaoPedidoFidelidade){
        console.log(String(`Empresa ${empresa.nome}, total pedidos nao pontuados: ${pedidosPeriodo.length}`))

        for(let j = 0;  j < pedidosPeriodo.length; j++){
          let pedido: any = pedidosPeriodo[j];
          await pedido.calculePontuacaoFidelidade(empresaCompleta.integracaoPedidoFidelidade)
          if(pedido.pontosGanhos){
            await insiraPontosPedido(empresaCompleta, pedido)
            respostaEmpresa.pedidos.push({id: pedido.id, horario: pedido.horario, pontos: pedido.pontosGanhos, pago: pedido.pago});
          }
        }
      }

      if( respostaEmpresa.pedidos.length) resposta.push(respostaEmpresa)
    }
  }
  res.json(resposta)
});

router.get('/:id/enderecos', async (req: any, res: any) => {
  let idContato = Number(req.params.id)

  let enderecos = await new MapeadorDeEndereco().listeAsync({idContato: idContato})

  res.json(Resposta.sucesso(enderecos));
});

router.post('/:id/bloqueie', async(req: any, res: any) => {
  let idContato = Number(req.params.id);
  let telefone = req.query.tel;
  const empresa = req.empresa;

  const mapeadorDeContatoBloqueado = new MapeadorDeContatoBloqueado();

  let contatoBloqueado: ContatoBloqueado = new ContatoBloqueado();
  contatoBloqueado.contato = new Contato(idContato, '', '');
  contatoBloqueado.telefone = telefone;
  contatoBloqueado.empresa = empresa;

  let inseriu = 0;

  inseriu = await mapeadorDeContatoBloqueado.insiraGraph(contatoBloqueado).catch( (erro: any) => {
    return 0;
  });

  res.json({
    sucesso: (inseriu > 0)
  });
});

router.post('/:id/desbloqueie', async(req: any, res: any) => {
  let idContato = Number(req.params.id)
  let telefone = req.query.tel;
  const empresa = req.empresa;

  const mapeadorDeContatoBloqueado = new MapeadorDeContatoBloqueado();

  mapeadorDeContatoBloqueado.desativeMultiCliente();
  let removeu = 0;

  removeu = await mapeadorDeContatoBloqueado.removaAsync({telefone: telefone}).catch( (erro: any) => {
    return 0;
  });

  res.json({
    sucesso: (removeu > 0)
  });
})


router.post('/:id/pontos/estorne', async(req: any, res: any) => {
  let idContato = Number(req.params.id),
    codigo =  req.body.codigo ,    motivo = req.body.m;

  if(!codigo || !motivo)
    return res.json(Resposta.erro('Parametros inválidos'))

  let pontuacaoRegistrada = await new MapeadorDePontuacaoRegistrada().selecioneSync({codigo: codigo});

  if(!pontuacaoRegistrada)
    return res.json(Resposta.erro('Codigo inválido: '  + codigo))

  let erro: string =
    await new CartaoService().estornePontosPorOperador(pontuacaoRegistrada, req.user, motivo);

  if(!erro){
    res.json(Resposta.sucesso())
  } else {
    res.json(Resposta.erro(erro))
  }
})



router.post('/:id/tags', async(req: any, res: any) => {
  let idContato = Number(req.params.id),
      tag = req.body;

  if(!tag || !tag.id)
      return   res.json(Resposta.erro('Nenhuma tag informada'))

  let contato =  await new MapeadorDeContato().selecioneSync(idContato);

  if(contato.tags.find((item: any) => item.id === tag.id))
    return     res.json(Resposta.erro('Contato já tem essa tag'))


  await new MapeadorDeContato().insiraTag(contato, tag);


  res.json(Resposta.sucesso())

})

router.post('/:id/tags/remova', async(req: any, res: any) => {
  let idContato = Number(req.params.id),
    tag = req.body;

  if(!tag || !tag.id)
    return   res.json(Resposta.erro('Nenhuma tag informada'))


  let contato =  await new MapeadorDeContato().selecioneSync(idContato);

  let erroRemover, mapeador = new MapeadorDeContato();
  mapeador.desativeMultiCliente();

  await mapeador.removaTag(contato, tag).catch((err: any) => {
    erroRemover = err.message ? err.message : err;
  });

  res.json(!erroRemover ? Resposta.sucesso() :  Resposta.erro(erroRemover))
})






export const ContatosController: Router = router;
