import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeTarefaDeEnvioDeNfse} from "../mapeadores/MapeadorDeTarefaDeEnvioDeNfse";
import {TarefaDeEnvioDeNfse} from "../domain/faturamento/TarefaDeEnvioDeNfse";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {MapeadorDeFatura} from "../mapeadores/MapeadorDeFatura";
import {Fatura} from "../domain/faturamento/Fatura";
import {MapeadorDeNotaFiscalDeServico} from "../mapeadores/MapeadorDeNotaFiscalDeServico";
import {DTOFatura, DTOFaturaCompleta} from "../lib/dto/DTOFatura";

const router: Router = Router();

function ehAdmin(req: any, res: any, next: any){
  let usuario: any = req.user;

  if(!usuario)
    return  res.json(Resposta.erro('Faça login para realizar a operação'))

  if(!usuario.admin)
    res.json(Resposta.erro('Operação não permitida'))

  return next();
}

router.get('/faturas/:mes/:ano', ehAdmin, (req: any, res: any) => {
  let mes = Number.parseInt(req.params.mes, 10)
  let ano = Number.parseInt(req.params.ano, 10)

  let dataInicio = new Date(mes === 1 ? ano - 1 : ano, mes === 1 ? 11 : mes - 3, 1)
  let dataFim = new Date(mes === 12 ? ano + 1 : ano, mes % 12, 1)

  let mapeador = new MapeadorDeFatura()

  mapeador.listeComNota({dataPagamentoInicio: dataInicio, dataPagamentoFim: dataFim, semNotaAprovada: true}).then((faturas) => {
    let dtos = faturas.map((it: Fatura) => {
        let dto = new DTOFaturaCompleta(it)
        if(dto.tarefa && dto.tarefa.nota) dto.tarefa.nota.link = dto.tarefa.nota.obtenhaLinkNota();
        return dto
    })
    dtos.sort((a: any, b: any) => {
      return a.id - b.id
    })

    res.json(Resposta.sucesso(dtos))
  })

})

router.get('/tarefas', ehAdmin, (req: any, res: any) => {
  const inicio = req.query.i || 0,
         total = req.query.t || 100;

  let mapeador = new MapeadorDeTarefaDeEnvioDeNfse()

  mapeador.listeAsync({inicio: inicio, total: total}).then((tarefas: any) => {
    let dtosTarefas: any[] = tarefas.map((tarefa: TarefaDeEnvioDeNfse) => {
      return     {
        id: tarefa.id,
        nota: {
          fatura: {
            id: tarefa.nota.fatura.id
          },
          empresaTomador: {
            nome: tarefa.nota.empresaTomador.nome,
            id: tarefa.nota.empresaTomador.id
          },
          valorServicos: tarefa.nota.valorServicos,
          discriminacao: tarefa.nota.discriminacao,
          aprovada: tarefa.nota.aprovada,
          numeroRps: tarefa.nota.numeroRps,
          numeroDaNota: tarefa.nota.numeroDaNota,
          codigoVerificacao: tarefa.nota.codigoVerificacao,
          link: tarefa.nota.obtenhaLinkNota()
        },
        status: tarefa.status,
        mensagem: tarefa.mensagem,
        tentativas: tarefa.tentativas
      }
    })

    return res.json(Resposta.sucesso(dtosTarefas))
  }).catch((reason) => {
    console.log("Houve um erro ao consultar as tarefas: " + reason);
    res.json(Resposta.erro(reason))
  })
})

router.post('/fatura/:idFatura/envieNota', ehAdmin, async(req: any, res: any) => {
  let mapeadorDeFatura = new MapeadorDeFatura()

  mapeadorDeFatura.selecioneSync({id: req.params.idFatura}).then((fatura: Fatura) => {
    if(!fatura)
      return res.json(Resposta.erroObjeto({
        tarefa: null,
        mensagem: "Não foi encontrada a fatura com id " + req.params.idFatura
      }))

    TarefaDeEnvioDeNfse.crieTarefaOuObtenha(fatura).then((resposta: Resposta<any>) => {
      if(!resposta.sucesso)
        return res.json(Resposta.erroObjeto({
          tarefa: null,
          mensagem: resposta.erro
        }))

      let tarefa = resposta.data as TarefaDeEnvioDeNfse


      tarefa.nota.fatura = fatura //completando dados da fatura
      tarefa.execute().then((respostaTarefa: Resposta<any>) => {
      tarefa.nota.link =  tarefa.nota.obtenhaLinkNota()
        if(respostaTarefa.sucesso)
          res.json(Resposta.sucesso(tarefa))
        else
          res.json(Resposta.erroObjeto({
            tarefa: tarefa,
            mensagem: respostaTarefa.erro
          }))
      }).catch((reason) => {
        res.json(Resposta.erroObjeto({
          tarefa: tarefa,
          mensagem: reason
        }))
      })
    })

  })


})

router.post('/execute/:idTarefa', ehAdmin,  async(req: any, res: any) => {
  let mapeadorDeTarefa = new MapeadorDeTarefaDeEnvioDeNfse()

  mapeadorDeTarefa.selecioneSync({id: req.params.idTarefa}).then((tarefa: TarefaDeEnvioDeNfse) => {
    if(!tarefa)
      return res.json(Resposta.erro("Não foi encontrada tarefa de nfse com id " + tarefa.id))

    if(tarefa.status === "Concluida")
      return res.json(Resposta.erro("Não é possível executar a tarefa " + tarefa.id + " pois ela já está concluída"))

    if(tarefa.status === "EmProcessamento")
      return res.json(Resposta.erro("Não é possível executar a tarefa " + tarefa.id + " pois ela já está em processamento"))

     new MapeadorDeFatura().selecioneSync({id: tarefa.nota.fatura.id}).then((fatura: Fatura) => {
       tarefa.nota.fatura = fatura //completando dados da fatura
       tarefa.execute().then((resposta) => {
         res.json(resposta)
       }).catch((reason) => {
         res.json(Resposta.erro(reason))
       })
     }).catch((reason) => {
       res.json(Resposta.erro(reason))
     })
  }).catch((reason) => {
    res.json(Resposta.erro(reason))
  })
})


export const NotasFiscaisController: Router = router;
