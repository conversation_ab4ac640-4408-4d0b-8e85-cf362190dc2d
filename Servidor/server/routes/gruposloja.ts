import {Router} from "express";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {DTOPedido} from "../lib/dto/DTOPedido";
import {Pedido} from "../domain/delivery/Pedido";
import * as _ from "underscore";
import {Resposta} from "../utils/Resposta";
// @ts-ignore
import moment = require("moment");
import {MapeadorDeGrupoDeLojas} from "../mapeadores/MapeadorDeGrupoDeLojas";
import {FiltroTelaPedidos} from "../utils/FiltroTelaPedidos";
import {PedidoService} from "../service/PedidoService";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {IServiceIntegracaoExternaERP} from "../domain/integracoes/IServiceIntegracaoExternaERP";

async function tahLogado(req: any, res: any, next: any) {
  if (req.user && req.grupoDeLojas) return next();

  if (req.user && req.user.adminGrupo) {
    let grupoDeLojas = await new MapeadorDeGrupoDeLojas().selecioneSync({nome: req.user.adminGrupo})

    if (grupoDeLojas) {
      req.grupoDeLojas = grupoDeLojas;

      if(req.params.idEmpresa){
        let contexto: any =    require('domain').active.contexto;
        contexto.empresa = await new MapeadorDeEmpresa().selecioneCachePoId( Number(req.params.idEmpresa));
        contexto.idEmpresa =  contexto.empresa.id;
        req.empresaDoGrupo =  contexto.empresa;
      }

      return next()
    }
  }

  res.status(401).json('')
}

const router: Router = Router();

router.get('/pedidos',  tahLogado, async (req: any, res: any) => {
  let formatHU = 'YYYYMMDDHHmmss';
  let query: any = new FiltroTelaPedidos(req.query, req.empresa, req.user).toSqlQuery();

  const grupoDeLojas = req.grupoDeLojas;

  query.empresasIds = grupoDeLojas.empresas.map((e: any) => e.id);

  let pedidos = await new MapeadorDePedido().listeAsync(query);

  let resposta: any = { pedidos: pedidos.map( (pedido: any) => new DTOPedido(pedido,   req.empresa))};

  let ultimoPedido: Pedido = _.sortBy( pedidos, (pedido: any) => -moment(pedido.horarioAtualizacao).format(formatHU))[0];

  resposta.ultimaAtualizacao =  ultimoPedido ? moment(ultimoPedido.horarioAtualizacao).format(formatHU) : null

  res.json(Resposta.sucesso(resposta))
})

router.get('/pedidos/ultimo/:tempo', tahLogado, async (req: any, res: any) => {
  let tempoAtras = Number(req.params.tempo);
  let filtro: any = {};

  const grupoDeLojas = req.grupoDeLojas;

  filtro.empresasIds = grupoDeLojas.empresas.map((e: any) => e.id);

  if(req.query.o) filtro.origens = req.query.o.split(',');

  let pedidos = await  new MapeadorDePedido().listeUltimos(filtro);
  let ultimo;

  for(let i = pedidos.length - 1; i >= 0 ; i--){
    if(moment().diff(moment(pedidos[i].horario), 's')  >= tempoAtras)
      ultimo = pedidos[i];
  }

  let resumoMesas: any =  { totalNovo: 0, mesas: []}

  resumoMesas.mesas = await new MapeadorDePedido().listeTotaisDePedidosMesasAbertos();
  resumoMesas.totalNovo = resumoMesas.mesas.reduce( (sum: any, mesa: any) => sum + mesa.totalNovo, 0);

  let resposta = { ultimo: ultimo, resumoMesas: resumoMesas}

  res.json(Resposta.sucesso(resposta));
})


router.get('/pedidos/:idEmpresa/:codigo', tahLogado, async (req: any, res: any) => {
  let query: any = { codigo: req.params.codigo}

  let pedido = await Pedido.get(query);

  if(pedido){
    res.json(Resposta.sucesso(new DTOPedido(pedido, pedido.empresa)))
  } else {
    res.json(Resposta.sucesso({}))
  }
});

router.put('/pedidos/visualizado/:idEmpresa/:id',  tahLogado, async(req: any, res: any) => {
  let idPedido = req.params.id;

  let pedido: Pedido = await Pedido.get(Number(idPedido));

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))

  pedido.visualizado = true;

  await new MapeadorDePedido().atualizeVisualizado(pedido);

  res.json(Resposta.sucesso())
})


router.get('/pedidos/chave/:idEmpresa/:guid', tahLogado, async (req: any, res: any) => {
  let pedido = await Pedido.get({guid: req.params.guid });
  const empresa: any =  req.empresaDoGrupo;

  if( !pedido )
    return res.json(Resposta.erro("Não encontrado!"));

  pedido.empresa = empresa

  res.json(Resposta.sucesso(new DTOPedido(pedido, pedido.empresa)))
});

router.put('/pedidos/status/:idEmpresa/:id', tahLogado, async (req: any, res: any) => {
  let novoStatus = req.body.status;
  let idPedido =  req.params.id;
  let notificar: boolean = req.body.notificar != null;

  if(!novoStatus && novoStatus !== 0) return res.json(Resposta.erro('Status não informado!'))


  let pedido: Pedido = await Pedido.get(Number(idPedido));
  const empresa: any =  req.empresaDoGrupo;

  console.log(`Mudar o status pedido ${idPedido} para ${empresa.nome}`);
  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))
  let resposta: any = await new PedidoService().operadorAlterouStatus(pedido, empresa, novoStatus, notificar, false, req.user);

  if(!resposta.erro){
    if(!resposta.erroNotificar){
      res.json(Resposta.sucesso());
    } else {
      res.json(Resposta.sucesso(resposta));
    }
  }else {
    res.json(Resposta.erro(resposta.erro));
  }
});

router.put('/pedidos/pago/:idEmpresa/:id', tahLogado, async (req: any, res: any) => {
  let pago = req.body.pago;
  let formaDePagamento = req.body.formaDePagamento;
  let idPedido = req.params.id;
  const empresa: any =  req.empresaDoGrupo;

  let pedido: Pedido = await Pedido.get(Number(idPedido));

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))

  let erro: any = await new PedidoService().usuarioAlterouFoiPago(pedido, empresa, pago, formaDePagamento);

  if(!erro){
    res.json(Resposta.sucesso({ redirectAdicionarPontos: false}))
  }  else{
    res.json(Resposta.erro(erro))
  }

})

router.post('/pedidos/integracao/notifique/:idEmpresa/:id', tahLogado, async(req: any, res: any) => {
  const  idPedido = req.body.id;
  const empresa: any =  req.empresaDoGrupo;

  if(!empresa.integracaoPDVParceiroAtiva())
    return res.json(Resposta.erro('Nenhum integração ativa '));

  let pedido: Pedido = await Pedido.get(Number(idPedido));

  if(!pedido) return res.json(Resposta.erro('Pedido não econtrado!'))

  let service: IServiceIntegracaoExternaERP = empresa.integracaoDelivery.obtenhaService();

  service.adicionePedido(pedido, empresa).then(   (uuid: string) => {
    pedido.referenciaExterna = uuid;
    new MapeadorDePedido().atualizeReferenciaExterna(pedido).then( () => {
      res.json(Resposta.sucesso( pedido.referenciaExterna));
    })
  } ).catch( (erroNotificar: any) => {
    pedido.erroExterno = erroNotificar
    new MapeadorDePedido().atualizeErroExterno(pedido).then( () => {
      res.json(Resposta.erro(erroNotificar));
    })
  })

});


export const GrupoLojasController: Router = router;
