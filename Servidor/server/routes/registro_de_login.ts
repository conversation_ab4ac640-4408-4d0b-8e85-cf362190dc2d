import { Router, Request, Response } from 'express';
import {UsuarioService} from "../service/UsuarioService";
import {Filtro} from "../domain/Filtro";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeFiltro} from "../mapeadores/MapeadorDeFiltro";
import * as _ from "underscore";
import {MapeadorDeUsuario} from "../mapeadores/MapeadorDeUsuario";
import {Usuario} from "../domain/Usuario";
import {MapeadorDeRegistroDeLogin} from "../mapeadores/MapeadorDeRegistroDeLogin";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";

const router: Router = Router();
const axios = require('axios');

router.get('/dados-localizacao', async (req: Request, res: Response) => {
  const idSessao = req.query.sid;
  const idEmpresa: any = req.query.eid;

  if (!idSessao) {
      return res.status(400).json({ erro: 'Faltando parâmetros.' });
  }

  try {
      if( !idEmpresa ) {
          return res.json(Resposta.erro('Faltando parâmetros.'));
      }

      let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(idEmpresa));

      if( !empresa ) {
          return res.json(Resposta.erro('Empresa não encontrada.'));
      }

      const registroDeLogin = await new MapeadorDeRegistroDeLogin().selecioneSync({idEmpresa: empresa.id, idSessao: idSessao});

      if( !registroDeLogin ) {
        return res.json(Resposta.erro('Sessão não encontrada.'));
      }


      const token = '73950b60f37650';
      const ip = registroDeLogin.ip;

      console.log('buscando ip: ' + ip);

      if( ip.indexOf('127.0.0.1') !== -1 || ip === '::1' || ip === 'localhost' ) {
        registroDeLogin.city = 'localhost';
        new MapeadorDeRegistroDeLogin().atualizeSync(registroDeLogin);

        return res.json(Resposta.sucesso({city: 'localhost'}));
      }

      const url = `https://ipinfo.io/${ip}?token=${token}`;

      console.log(`buscando: ` + url);

      axios.get(url).then( (response: any) => {
        registroDeLogin.hostname = response.data.hostname || null;
        registroDeLogin.city = response.data.city || null;
        registroDeLogin.region = response.data.region || null;
        registroDeLogin.country = response.data.country || null;
        registroDeLogin.loc = response.data.loc || null;
        registroDeLogin.org = response.data.org || null;
        registroDeLogin.postal = response.data.postal || null;
        registroDeLogin.timezone = response.data.timezone || null;

        new MapeadorDeRegistroDeLogin().atualizeSync(registroDeLogin);

        return res.json(Resposta.sucesso(response.data));
      })
      .catch( (error: any) => {
        console.error('Error:', error);
      });
    } catch( erro: any ) {
      return res.json({
        sucesso: false
      })
    }
});

router.get('/sessoes', async (req: any, res: Response) => {
  const uid = req.query.uid;
  const idEmpresa = req.query.e;
  if (!uid) {
      return res.status(400).json({ erro: 'Faltando parametros.' });
  }

  const mapeadorDeRegistroDeLogin = new MapeadorDeRegistroDeLogin();

  try {
      // Busca os registros de login ativos usando o mapeador
      const sessoesAtivas = await mapeadorDeRegistroDeLogin.listeAsync({idEmpresa: idEmpresa, idUsuario: uid, sessaoAtiva: true});
      res.json(Resposta.sucesso(sessoesAtivas));
  } catch (erro) {
      console.error(erro);
      res.status(500).json({ erro: 'Erro ao buscar sessões ativas' });
  }
});

router.post('/deslogar-todas', async (req: Request, res: Response) => {
  const uid = req.query.uid;
  const idEmpresa: any = req.query.eid;

  if (!uid) {
      return res.status(400).json({ erro: 'Faltando parâmetros.' });
  }

  try {
    if( !idEmpresa ) {
      return res.json(Resposta.erro('Faltando parâmetros.'));
    }

    let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(idEmpresa));

    if( !empresa ) {
      return res.json(Resposta.erro('Empresa não encontrada.'));
    }

    const usuario = await new MapeadorDeUsuario().selecioneSync(uid);

    await new UsuarioService().deslogarTodasSessoes(empresa, usuario);

    res.json(Resposta.sucesso({ mensagem: 'Todas as sessões foram deslogadas com sucesso.' }));
  } catch (erro) {
      console.error(erro);
      res.status(500).json({ erro: 'Erro ao deslogar todas as sessões' });
  }
});

router.post('/deslogue', async (req: Request, res: Response) => {
  const idSessao = req.query.sid;
  const idEmpresa: any = req.query.eid;

  if (!idSessao) {
      return res.status(400).json({ erro: 'Faltando parâmetros.' });
  }

  try {
      if( !idEmpresa ) {
          return res.json(Resposta.erro('Faltando parâmetros.'));
      }

      let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(idEmpresa));

      if( !empresa ) {
          return res.json(Resposta.erro('Empresa não encontrada.'));
      }

      const registroDeLogin = await new MapeadorDeRegistroDeLogin().selecioneSync({idEmpresa: empresa.id, idSessao: idSessao});

      if( !registroDeLogin ) {
          return res.json(Resposta.erro('Sessão não encontrada.'));
      }

      await new UsuarioService().deslogarSessao(registroDeLogin);

      res.json(Resposta.sucesso({ mensagem: 'Sessão foi deslogada com sucesso.' }));
  } catch (erro) {
      console.error(erro);
      res.status(500).json({ erro: 'Erro ao deslogar todas as sessões' });
  }
});

export const RegistrosDeLoginController: Router = router;
