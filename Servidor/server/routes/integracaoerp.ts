// @ts-ignore
import {Router} from "express";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {Resposta} from "../utils/Resposta";
import * as _ from 'underscore';
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {EcleticaService} from "../service/EcleticaService";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {IntegracaoRPInfo} from "../domain/integracoes/IntegracaoRPInfo";
import {MapeadorDeIntegracaoDelivery} from "../mapeadores/MapeadorDeIntegracaoDelivery";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {RPInfoService} from "../service/RPInfoService";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {Contato} from "../domain/Contato";
import {MapeadorDeEndereco} from "../mapeadores/MapeadorDeEndereco";
import {ImportadorProdutosRPInfo} from "../lib/integracao/RPInfo/ImportadorProdutosRPInfo";
import {DTOPedidoEcletica} from "../lib/integracao/ecletica/DTOPedidoEcletica";
import {ImportardorTrendFoods} from "../lib/integracao/ImportardorTrendFoods";
import {BlueSoftERPService} from "../service/integracoes/BlueSoftERPService";
import {ImportadorProduto} from "../lib/integracao/ImportadorProduto";
import {DTOPedidoSaipos} from "../lib/integracao/saipos/DTOPedidoSaipos";
import {DTOProdutoSincronizar} from "../lib/integracao/ecletica/DTOProdutoSincronizar";
import {IServiceIntegracaoExternaERP} from "../domain/integracoes/IServiceIntegracaoExternaERP";
import {BlueSoftTarefa} from "../lib/integracao/BlueSoftTarefa";
import {Empresa} from "../domain/Empresa";
import {IntegradorUtils} from "../utils/IntegradorUtils";
import {Produto} from "../domain/Produto";
import {IFonteProdutosExternos} from "../domain/integracoes/IFonteProdutosExternos";
import {IntegracaoFoodyDelivery} from "../domain/integracoes/IntegracaoFoodyDelivery";
import {FoodyDeliverYService} from "../service/FoodyDeliverYService";
import {Pedido} from "../domain/delivery/Pedido";
import {EnumStatusPedidoFoodyDelivery} from "../service/EnumStatusPedidosIntegrados";
import {MapeadorDeNotificacaoDelivery} from "../mapeadores/MapeadorDeNotificacaoDelivery";
import {NotificacaoDelivery} from "../domain/integracoes/NotificacaoDelivery";
import {IntegracaoEcleticaERP} from "../domain/integracoes/IntegracaoEcleticaERP";
import {ComandaService} from "../service/ComandaService";
import {Comanda} from "../domain/comandas/Comanda";
import {DTOComanda} from "../lib/dto/DTOComanda";
import {NotificacaoMesaService} from "../service/NotificacaoMesaService";
import {NotificacaoMesa} from "../domain/integracoes/NotificacaoMesa";
import {DTOPedidoInLocoEcletica} from "../lib/integracao/ecletica/DTOPedidoInLocoEcletica";
import {DTOPedidoTiny} from "../lib/integracao/tiny/DTOPedidoTiny";


const router: Router = Router();

function temIntegracaoAtiva(req: any, res: any, next: any){
  let  erro: string;

  if(req.empresa){
    if(!req.query.openDelivery){
      if( !req.empresa.integracaoDelivery)
        erro = 'Integração com sistema parceiro não está ativada'
    } else {
      if( !req.empresa.integracaoOpendelivery)
        erro = 'integração open delivery não está ativada'
    }
  } else {
    erro = 'Nenhuma empresa encontrada'
  }

  if(erro) {
    res.json(Resposta.erro(erro))
  } else {
    return next();
  }
}

router.get('/pedidos/polling/dispare', temIntegracaoAtiva, async (req: any, res ) => {
    let empresa: any = req.empresa;

    if(!empresa.integracaoDelivery) return res.json(Resposta.erro('Nenhum integraçaõ ativa'))

    let sistema = empresa.integracaoDelivery.sistema;
    let pedidos = await  new MapeadorDePedido().listeAbertosIntegrado(sistema);

    console.log('Total pedidos em aberto: ' + pedidos.length)

    if(pedidos.length){
      let serviceInstance: any =  empresa.integracaoDelivery.obtenhaService();

      ExecutorAsync.execute( async (cbAsync: any) => {
        try{
          let contexto = require('domain').active.contexto;
          contexto.idEmpresa = empresa.id;
          contexto.empresa = empresa;
          let mapNovosStatus = await serviceInstance.veririqueBaixasNosPedidos(pedidos)

          if(mapNovosStatus && Object.keys(mapNovosStatus).length)
            await IntegradorUtils.executeAlteracaoStatusEmLote(pedidos, mapNovosStatus, contexto)

          cbAsync();
        } catch (err){
          console.log('#erro execute async')
          console.log(err)
          cbAsync();
        }
      }, () => {}, 0)
    }

    res.json(Resposta.sucesso(  pedidos.map( (pedido: any) => pedido.referenciaExterna) ))
})


router.get('/produtos/faltantes', temIntegracaoAtiva, async (req: any, res ) => {
  let service = req.empresa.integracaoDelivery.obtenhaService();

  let produtosErp  = await service.listeProdutosIndisponiveis( ).catch( (err: any) => {
    res.json(Resposta.erro(err))
  })

  res.json(produtosErp)

})




router.get('/produtos/indisponiveis', temIntegracaoAtiva, async (req: any, res ) => {
  let empresa: Empresa = req.empresa
  let produtosIntegrados = await new MapeadorDeProduto(empresa.catalogo).listeAsync({ integrado: true, idEmpresa: empresa.id});

  if(produtosIntegrados.length){
    let integracao: any =  req.empresa.integracaoDelivery,
      service = integracao.obtenhaService();

    let produtosIndisponiveis  =
      await service.listeProdutosDisponiblidadeAtualizada( produtosIntegrados, integracao.ultimaSincronizacaoEstoque).catch( (err: any) => {
      res.json(Resposta.erro(err))
    })

    if(produtosIndisponiveis){
      res.json(Resposta.sucesso(produtosIndisponiveis))
    }
  } else {
    res.json(Resposta.erro('Nenhum produto integrado, sincronização naõ realizada.'))
  }
})

router.get('/arvoremercadologica', temIntegracaoAtiva, async (req: any, res ) => {
  let service: any = req.empresa.integracaoDelivery.obtenhaService();


  let resposta: any = await service.obtenhaArvoreMercadologica().catch( (erroIntegracao: any) => {
    res.json(Resposta.erro(erroIntegracao))
  })

  if(resposta){
    res.json(resposta)
  }
})

router.get('/produtos/novos/async', temIntegracaoAtiva, async (req: any, res: any ) => {

  ExecutorAsync.execute( (cbAsync: any) => {
    let contexto = require('domain').active.contexto;
    contexto.idEmpresa = req.empresa.id;
    contexto.empresa = req.empresa;
    let tarefaImportacao: any =  { id: new Date().getTime()  };
    req.session.tarefaImportacao = tarefaImportacao;
    new ImportadorProduto().obtenhaNovosProdutos(req.empresa, req.query.openDelivery != null).then( (resposta: any) => {
      console.log('Setar resposta produtos na sessao: ' + req.session.tarefaImportacao.id)
      ImportadorProduto.setRespostaImportacao(tarefaImportacao.id, resposta)
      cbAsync();
    }).catch( (erroIntegracao: any) => {
      console.log('Setar resposta erro na sessao: ' + req.session.tarefaImportacao.id)
      ImportadorProduto.setRespostaImportacao(tarefaImportacao.id, { erro: erroIntegracao})
      cbAsync();
    })
  }, (err: any) => {  req.session.tarefaImportacao.erro = err; console.error(err)}, 0)

 setTimeout( () => {
   res.json(Resposta.sucesso())
 }, 500)
})

router.get('/produtos/importacao/status', temIntegracaoAtiva, async (req: any, res ) => {
  let tarefaImportacao = req.session.tarefaImportacao;

  if(!tarefaImportacao)
      return  res.json( Resposta.erro('Nenhum importação iniciada'))

  let resposta: any = await ImportadorProduto.obtenhaRespostaImportacao(tarefaImportacao.id);

  if(resposta) {
    res.json(Resposta.sucesso(resposta))
  } else {
    res.json(Resposta.sucesso({id: tarefaImportacao.id, processando: true}))
  }
})

router.get('/produtos/novos', temIntegracaoAtiva, async (req: any, res ) => {

  new ImportadorProduto().obtenhaNovosProdutos(req.empresa).then( (resposta: any) => {
    res.json(Resposta.sucesso(resposta))
  }).catch( (erroIntegracao: any) => {
    res.json(Resposta.erro(erroIntegracao))
  })

})

router.get('/produtos/sincronizar/estoque/async', temIntegracaoAtiva, async (req: any, res ) => {
  let todos: any = req.query.todos != null;

  ExecutorAsync.execute( async (cbAsync: any) => {
    let contexto = require('domain').active.contexto;
    contexto.idEmpresa = req.empresa.id;
    contexto.empresa = req.empresa;
    let tarefaImportacao: any =  { id: new Date().getTime()  };

    req.session.tarefaImportacao = tarefaImportacao;

    let produtosIntegrados = await new MapeadorDeProduto(req.empresa.catalogo).listeAsync(
      { integrado: true, idEmpresa: contexto.empresa.id});

    let integracao: any =  req.empresa.integracaoDelivery,
      service = integracao.obtenhaService(),
      dataUltimaSincronizacao: any =  todos ? null :  integracao.ultimaSincronizacaoEstoque;

    let produtosIndisponiveis  =
      await service.listeProdutosDisponiblidadeAtualizada( produtosIntegrados, dataUltimaSincronizacao)
        .catch( (err: any) => {
            console.log('Setar resposta erro na sessao: ' + req.session.tarefaImportacao.id);
            ImportadorProduto.setRespostaImportacao(tarefaImportacao.id, { erro: err})
            cbAsync();
    })

    if(produtosIndisponiveis){
      console.log('Setar resposta estoque na sessao: ' + req.session.tarefaImportacao.id)
      ImportadorProduto.setRespostaImportacao(tarefaImportacao.id, produtosIndisponiveis)
      cbAsync();
    }

  }, (err: any) => {  req.session.tarefaImportacao.erro = err; console.error(err)}, 0)


  setTimeout( () => {
    res.json(Resposta.sucesso())
  }, 500)
})


router.get('/produtos/sincronizar/precos/async', temIntegracaoAtiva, async (req: any, res ) => {
  let todos: any = req.query.todos != null;

  ExecutorAsync.execute( (cbAsync: any) => {
    let contexto = require('domain').active.contexto;
    contexto.idEmpresa = req.empresa.id;
    contexto.empresa = req.empresa;
    let tarefaImportacao: any =  { id: new Date().getTime()  };
    req.session.tarefaImportacao = tarefaImportacao;
    new ImportadorProduto().obtenhaNovosPrecos(req.empresa, todos).then( (resposta: any) => {
      console.log('Setar resposta produtos na sessao: ' + req.session.tarefaImportacao.id)
      ImportadorProduto.setRespostaImportacao(tarefaImportacao.id, resposta)
      cbAsync();
    }).catch( (erroIntegracao: any) => {
      console.log('Setar resposta erro na sessao: ' + req.session.tarefaImportacao.id)
      ImportadorProduto.setRespostaImportacao(tarefaImportacao.id, { erro: erroIntegracao})
      cbAsync();
    })
  }, (err: any) => {  req.session.tarefaImportacao.erro = err; console.error(err)}, 0)

  setTimeout( () => {
    res.json(Resposta.sucesso())
  }, 500)
})

router.get('/produtos/sincronizar/precos', temIntegracaoAtiva, async (req: any, res ) => {

  new ImportadorProduto().obtenhaNovosPrecos(req.empresa).then( (resposta: any) => {
    res.json(Resposta.sucesso(resposta))
  }).catch( (erroIntegracao: any) => {
    res.json(Resposta.erro(erroIntegracao))
  })

})


router.get('/produtos', temIntegracaoAtiva, async (req: any, res ) => {

  let service = req.empresa.integracaoDelivery.obtenhaService();

  service.listeProdutos().then( (produtos: any) => {
    res.json(produtos)
  }).catch( (erro: any) => res.json(erro));

})

router.get('/categorias', temIntegracaoAtiva, async (req: any, res ) => {
  let service = req.empresa.integracaoDelivery.obtenhaService();

  service.listeCategorias().then((categorias: any) => {
    res.json(categorias)
    }).catch( (erro: any) => res.json(erro));
})


router.get('/produtos/precos', temIntegracaoAtiva, async (req: any, res ) => {
  let service = req.empresa.integracaoDelivery.obtenhaService();

  let produtos = await service.listePrecosProdutos();

  res.json(produtos)

})

router.get('/pedido/:guid', temIntegracaoAtiva, async (req: any, res ) => {
  let pedido = await new MapeadorDePedido().selecioneSync({ guid : req.params.guid});
  let dadosPedido = {};

  if(pedido && pedido.referenciaExterna){
    let service = req.empresa.integracaoDelivery.obtenhaService();

    dadosPedido = await service.obtenhaPedido(pedido).catch( (err: any) => {
      res.json(Resposta.erro(err))
    })
  }

  if(dadosPedido)
    res.json(Resposta.sucesso(dadosPedido))
})

router.get('/pedido/:guid/dtosaipos', temIntegracaoAtiva, async (req: any, res ) => {
  try {
    let pedido = await new MapeadorDePedido().selecioneSync({ guid : req.params.guid});

    await pedido.carregueDeParaTamanhoSabores();

    let codigoStore = req.empresa.id.toString();

    res.json(new DTOPedidoSaipos(pedido, req.empresa, codigoStore))
  } catch (error){
    res.json(error.message)
  }
})

router.get('/pedido/:guid/dtoecletica', temIntegracaoAtiva, async (req: any, res ) => {
  try {
    let pedido = await new MapeadorDePedido().selecioneSync({ guid : req.params.guid});

    await pedido.carregueDeParaTamanhoSabores();

    if(pedido.mesa && pedido.mesa.id){
      res.json(new DTOPedidoInLocoEcletica(pedido))
    } else {
      res.json(new DTOPedidoEcletica(pedido, req.empresa))
    }

  } catch (error){
    console.error(error)
    res.json(error.message)
  }

})


router.get('/pedido/:guid/dtotiny', temIntegracaoAtiva, async (req: any, res ) => {
  try {
    let pedido = await new MapeadorDePedido().selecioneSync({ guid : req.params.guid});

    await pedido.carregueDeParaTamanhoSabores();

    res.json(new DTOPedidoTiny(pedido, req.empresa))

  } catch (error){
    console.error(error)
    res.json(error.message)
  }

})

router.get('/pedido/referencia/:referencia', temIntegracaoAtiva, async (req: any, res ) => {
  let pedido = await new MapeadorDePedido().selecioneSync({ referenciaExterna : req.params.referencia});
  let dadosPedido = {};

  if(pedido && pedido.referenciaExterna){
    let service: EcleticaService = req.empresa.integracaoDelivery.obtenhaService();

    dadosPedido = await service.obtenhaPedido(pedido).catch( (err: any) => {
      res.json(Resposta.erro(err))
    })
  }

  if(dadosPedido)
    res.json(Resposta.sucesso(dadosPedido))
})


router.get('/cartoes/bandeiras', temIntegracaoAtiva, async (req: any, res ) => {
  let tipo = req.query.tipo;
  let service = req.empresa.integracaoDelivery.obtenhaService();

  let bandeiras  = await service.listeBandeiras(tipo ).catch( (err: any) => {
    res.json(Resposta.erro(err))
  })

  if(bandeiras){
    bandeiras = _.sortBy( bandeiras, (bandeira: any) => bandeira.nome);

    bandeiras.forEach( (bandeira: any) => {
      bandeira.codigoPdv = bandeira.id;
      delete bandeira.id;
    })

    res.json(Resposta.sucesso(bandeiras))
  }
})

router.get('/veririqueUpdates', temIntegracaoAtiva, async (req: any, res ) => {
  let service = req.empresa.integracaoDelivery.obtenhaService();

  service.veririqueUpdates({}).then( (ultimas: any) => {
    res.json(ultimas)
  })
})
//

router.get('/bluesoft/:grupo/lojas', async (req: any, res ) => {
   let token = req.query.token;
   let grupo = req.params.grupo; //soneda, sacolaodasanta

   if(!token ) return  res.json(Resposta.erro('Token não informado.'))

   if(token){
      new BlueSoftERPService({ token: token, grupo: grupo}).listeLojas().then((lojas: any) => {
        res.json(Resposta.sucesso(lojas));
      }).catch(erro => {
        res.json(Resposta.erro(erro))
      })
   } else {
     res.json(Resposta.erro('Token não informado.'))
   }
})

router.get('/bluesoft/categorias/ecommerce', async (req: any, res ) => {
  let servico: BlueSoftERPService = req.empresa.integracaoDelivery.obtenhaService() as BlueSoftERPService


  let categorias = await servico.listeCategoriasEcommerce(0, 1000);

  res.json(categorias)
})

router.get('/produtos/sincronizar/:codigo', async (req: any, res ) => {
  let servico: IServiceIntegracaoExternaERP = req.empresa.integracaoDelivery.obtenhaService();
  let codigo = req.params.codigo;

  let produto = await servico.obtenhaProdutoConvertido(req.params.codigo)

  if(produto){
    let resposta: any = { novos: [], atualizar: [], categorias: [] };

    let produtoExistente = await new MapeadorDeProduto(req.empresa.catalogo).selecioneSync({codigoPdv: codigo})

    if(!produtoExistente){
      produto.origem = (servico as any).obtenhaTipoDeOrigem();
      produto.dataCadastro = new Date()
      resposta.novos.push(produto);
      resposta.categorias.push(produto.categoria);
      res.json(Resposta.sucesso(resposta));
    } else {
      resposta.categorias.push(produtoExistente.categoria);
      DTOProdutoSincronizar.obtenhaProdutoSincronizar(produto, produtoExistente, resposta.atualizar, true, false);

      res.json(Resposta.sucesso(resposta))
    }

  } else {
    res.json(Resposta.erro('Nenhum produto encontrado: ' + codigo))
  }
})


router.get('/produtos/erp/:codigo', async (req: any, res ) => {
  let servico: any = req.empresa.integracaoDelivery.obtenhaService();

  let produto = await servico.obtenhaProduto(req.params.codigo)

  res.json(produto)

})

router.get('/produtos/corrija/unidade', async (req: any, res ) => {
  let empresa: any = req.empresa;

  if(req.empresa.integracaoDelivery){
    ExecutorAsync.execute(async () => {
      let contexto = require('domain').active.contexto;
      contexto.idEmpresa = empresa.id
      contexto.empresa = empresa;

      let servico: any = req.empresa.integracaoDelivery.obtenhaService();

      let todosProdutos = await new MapeadorDeProduto(empresa.catalogo).listeAsync({ integrado: true});

      let produtosErp = await servico.listeProdutosConvertidos(null);
      let produtosAtualizar = []
      for(let i = 0; i < todosProdutos.length; i++){
        let produto: any = todosProdutos[i];
        let produtoAtualizado = produtosErp.find((item: any) => item.codigoPdv === produto.codigoPdv )

        if(produtoAtualizado && produtoAtualizado.unidadeMedida && !produto.unidadeMedida){
          produtoAtualizado.id = produto.id;
          produtosAtualizar.push(produtoAtualizado)
        }
      }

      console.log('Total produtos atualizar: ' + produtosAtualizar.length);

      let mapeador = new MapeadorDeProduto(empresa.catalogo);

      for(let i = 0; i < produtosAtualizar.length; i++){
        let produto = produtosAtualizar[i];
        console.log(String(`Atualizar ${produto.id} => ${produto.codigoPdv} - ${produto.nome}`))

        await mapeador.atualizeUnidadeMedida(produto);
      }

      await mapeador.removaCacheProdutos();

    }, 0);

    res.json(Resposta.sucesso( 'disparou...'))

  } else {
    res.json(Resposta.erro('integração na ativa'))
  }
})


router.get('/china/skus', async (req: any, res ) => {
   let skus = await new ImportardorTrendFoods( req.empresa.integracaoDelivery).obtenhaSkus();

   res.json(skus)
})
router.get('/china/promocoes', async (req: any, res ) => {
   let promocoes = await new ImportardorTrendFoods( req.empresa.integracaoDelivery).listePromocoes();

   res.json(promocoes)
})

router.get('/china/combos', async (req: any, res ) => {
  let promocoes = await new ImportardorTrendFoods( req.empresa.integracaoDelivery).obtenhaCombosUnidade();

  res.json(promocoes)
})

router.get('/china/categorias', async (req: any, res ) => {

  let categorias = await new ImportardorTrendFoods( req.empresa.integracaoDelivery).listeCategorias();

  res.json(categorias)
})


router.get('/china/categorias/:codigo/produtos', async (req: any, res ) => {
  let produtos =
    await new ImportardorTrendFoods( req.empresa.integracaoDelivery).obtenhaProdutosDaCategoria({id: req.params.codigo});

  res.json(produtos)
})



router.get('/china/categorias/site', async (req: any, res ) => {
  let catalogo = req.empresa.integracaoDelivery.unidadeChina ? 2 : 3;
  let categorias = await new ImportardorTrendFoods( req.empresa.integracaoDelivery).listeCategoriasSite();

  res.json(categorias)
})

router.get('/china/produto/:codigo', async (req: any, res ) => {
  let produto = await new ImportardorTrendFoods('').obtenhaProduto(req.params.codigo);

  res.json(produto)
})



router.get('/china/sku/:codigo', async (req: any, res ) => {
  let produto = await new ImportardorTrendFoods('').obtenhaSku(req.params.codigo);

  res.json(produto)
})

router.get('/china/skus/:codigos', async (req: any, res ) => {
  let produto = await new ImportardorTrendFoods('').obtenhaSkus(req.params.codigos.split(','));

  res.json(produto)
})


router.get('/teste', async (req: any, res: any) => {
  IntegracaoRPInfo.crieIntegracao(req.empresa, '100014', '102030',
    2, 3009, "7062", "00905513000141",
    "http://177.107.188.137:9000").then(async (integracao) => {
    if(!integracao)
      return res.json(Resposta.erro("Não foi possível criar objeto integracao"))

    let mapeadorDeIntegracaoDelivery = new MapeadorDeIntegracaoDelivery()

    if(req.empresa.integracaoDelivery)
      await mapeadorDeIntegracaoDelivery.removaAsync(req.empresa.integracaoDelivery)

    mapeadorDeIntegracaoDelivery.insiraSync(integracao).then((inseriu: any) => {

      new MapeadorDeEmpresa().removaDasCaches(req.empresa)

      res.json(Resposta.sucesso(integracao))
    })

  }).catch((erro: any) => {
    res.json(Resposta.erro(erro))
  })
})

router.get('/testeListarCategoria', async(req: any, res: any) => {


  let integracao: IntegracaoRPInfo = req.empresa.integracaoDelivery

  let servico: RPInfoService = integracao.obtenhaService() as RPInfoService

  servico.listeCategorias().then((categorias: any) => {
    res.json(categorias)
  }).catch((reason: any) => {
    res.json({erro: reason});
  })

})

router.get('/testeInserirContato', async (req: any, res: any) => {
  let integracao: IntegracaoRPInfo = req.empresa.integracaoDelivery

  let servico: RPInfoService = integracao.obtenhaService() as RPInfoService;

  let mapeador = new MapeadorDeContato()

  mapeador.selecioneSync({id: 27977}).then(async (contato: Contato) => {
    let mapeadorEndereco = new MapeadorDeEndereco()

    let enderecos = await new MapeadorDeEndereco().listeAsync({idContato: contato.id});

    mapeadorEndereco.listeAsync({idContato: contato.id}).then((enderecos2: any[]) => {
      servico.inserirContato(contato, enderecos[0], req.empresa).then((clienteInserido: any) => {
        res.json(clienteInserido)
      }).catch((erro) => {
        res.json(erro)
      })
    })

  })

})

router.get('/testeListarProdutos', async (req: any, res: any) => {
  let integracao: IntegracaoRPInfo = req.empresa.integracaoDelivery


  integracao.obtenhaService().listeProdutos().then((resposta: any) => {
    //console.log(resposta)
    res.json(resposta)
  })
})

router.get('/testeImporteProdutos', async (req: any, res: any) => {
  let importador = new ImportadorProdutosRPInfo(req.empresa);

  importador.importeTodosProdutos().then((resultado) => {
    res.json("Os produtos foram importados.")
  });
})

router.get('/importador/rpinfo', async (req: any, res: any) => {
  let empresa = req.empresa
  ExecutorAsync.execute( async (cbAsync: any) => {
    try{
      let contexto = require('domain').active.contexto;
      contexto.idEmpresa = empresa.id
      contexto.empresa = empresa;

      let importador = new ImportadorProdutosRPInfo(req.empresa)

      console.log("*** Iniciando importação dos produtos ***")
      importador.importeTodosProdutos().then((resultado) => {

        console.log("*** Importação finalizada com sucesso ***")
        cbAsync();
      }).catch((reason) => {
        cbAsync();
        console.log("*** Houve um erro com o processo de importação ***")
      })

    } catch (err){
      console.log('#erro execute async')
      console.log(err)
      cbAsync();
    }

  }, () => {}, 0)

  res.json(Resposta.sucesso("Processo iniciado com sucesso"))
})


router.get('/china/categorias/:id/produtos', async (req: any, res ) => {
  let produtos = await new ImportardorTrendFoods( req.empresa.integracaoDelivery).obtenhaProdutosDaCategoria({id: req.params.id});

  res.json(produtos)
})

router.get('/oracle/categorias/:id/produtos', async (req: any, res ) => {
  let produtos = await new ImportardorTrendFoods( req.empresa.integracaoDelivery).listeProdutosOracle({id: req.params.id});

  res.json(produtos)
})

router.get('/china/produtos/:id', async (req: any, res ) => {
  let produto = await new ImportardorTrendFoods( req.empresa.integracaoDelivery).obtenhaProduto(req.params.id);

  res.json(produto)
})

router.get('/china/lojas', async (req: any, res ) => {
   let lojas =  await new MapeadorDeEmpresa().listeLojasChinas();

   res.json(Resposta.sucesso(lojas))
})
router.get('/gendai/lojas', async (req: any, res ) => {
   let lojas =  await new MapeadorDeEmpresa().listeLojasGendais();

   res.json(Resposta.sucesso(lojas))
})


router.get( '/bluesoft/sincronize/loja', async (req: any, res ) => {
  let empresa: any = req.empresa;

  ExecutorAsync.execute( async (cbasync: any) => {
    let contexto = require('domain').active.contexto;
    contexto.idEmpresa = empresa.id
    contexto.empresa = empresa;

    let retorno: any = { totalEmpresas: 0, sincronizadas: [], erros: []};
    await new BlueSoftTarefa().sincronizeCatalogoEmpresa(empresa, retorno);
    console.log('finalizou disparo tarefa')
    console.log(retorno)
    cbasync();

  }, (errorTarefa: any) => {
    console.log('Falha ao disparar sincronizar empresa blusoft')
    console.log(errorTarefa)
  }, 100)

  res.json(Resposta.sucesso('disparou...'));
})


router.get('/ecletica/disponibilidade/dispare', async (req: any, res ) => {

  let produtosSincronizados: any =  await EcleticaService.monitoreProdutosIndisponiveis({}, req.empresa.catalogo);

  res.json(Resposta.sucesso(produtosSincronizados));
})

router.get('/ecletica/disponibilidade/dispare/todos', async (req: any, res ) => {

  let produtosSincronizados: any =  await EcleticaService.monitoreProdutosIndisponiveis({});

  res.json(Resposta.sucesso(produtosSincronizados));
})


router.post('/foodydelivery/pedidos/:guid/cancele', async (req: any, res: any) => {
  let integracao: IntegracaoFoodyDelivery = req.empresa.integracaoFoodyDelivery;

  if(integracao){
    let pedido: Pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

    let erro: any;
    await integracao.obtenhaService().cancelePedido(pedido)
      .catch((_erro: string) => {
        erro = _erro
      })

    if(!erro){
      await  pedido.deliveryPedido.atualizeRetorno(EnumStatusPedidoFoodyDelivery.Cancelado,
        null, 'Loja cancelou entrega')

      res.json(Resposta.sucesso(pedido.deliveryPedido.toDTO()))
    } else {
      res.json({erro : erro})
    }
  } else {
    res.json(Resposta.erro('Nenhum integração ativa'))
  }
})

router.post('/foodydelivery/pedidos/:guid/novaEntrega', async (req: any, res: any) => {
  let integracao: IntegracaoFoodyDelivery = req.empresa.integracaoFoodyDelivery;

  if(integracao){
    let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

    let service: FoodyDeliverYService = integracao.obtenhaService();

    let novaEntrega: any =  await service.notifiqueNovaEntrega(pedido, req.empresa, req.user);

    if(novaEntrega){
      res.json(Resposta.sucesso(novaEntrega.toDTO()))
    } else {
      res.json(Resposta.erro(pedido.erroExternoDelivery))
    }
  } else {
    res.json(Resposta.erro('Nenhum integração com Food Delivery ativa'))
  }
})


router.get('/foodydelivery/:guid', async (req: any, res: any) => {
  let integracao: IntegracaoFoodyDelivery = req.empresa.integracaoFoodyDelivery;

  if(integracao){
    let pedido: Pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

    let uid = pedido.deliveryPedido ? pedido.deliveryPedido.deliveryId : pedido.referenciaExternaDelivery;

    let delivery: any = await integracao.obtenhaService().obtenhaPedido(uid);

    res.json(delivery)
  } else {
    res.status(404).json('Nenhum pedido encontrado')
  }

})

router.get('/foodydelivery/:guid/delivery/sincronize', async (req: any, res: any) => {
  let integracaoFoody: any = req.empresa.integracaoFoodyDelivery;

  if(integracaoFoody){
    let pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid})
    let uid = pedido.deliveryPedido ? pedido.deliveryPedido.deliveryId : pedido.referenciaExternaDelivery;

    let delivery: any = await integracaoFoody.obtenhaService().obtenhaPedido(uid).catch((err: any) => {
      console.error(err)
      res.json(Resposta.erro(err));
    })

    if(delivery ){
      if(delivery.status !== pedido.deliveryPedido.status)
        await  pedido.deliveryPedido.atualizeRetorno(delivery.status, delivery)

      res.json(Resposta.sucesso(pedido.deliveryPedido.toDTO()))
    }
  } else {
    res.json(Resposta.erro('Nenhuma integração ativa'));
  }
})



router.get('/foodydelivery/pedidos/:id/notificacoes', async (req: any, res: any) => {
  let eventos = await new MapeadorDeNotificacaoDelivery().listeAsync({ idPedido: req.params.id, origem: 'foodydelivery'})

  res.json(Resposta.sucesso(eventos.map((item: NotificacaoDelivery) => item.toDTO())))
})

router.get('/produtos/imagens/baixenovas', async (req: any, res ) => {
    let produtosSemImagem: any = [], naoEncontrados = []

    if(req.empresa.integracaoDelivery){
      produtosSemImagem = await new MapeadorDeProduto(req.empresa.catalogo).listeAsync({ semImagens: true});

      console.log('Total produtos sem imagem: ' + produtosSemImagem.length);

      ExecutorAsync.execute(async (cbAsync: Function) => {
        let contexto = require('domain').active.contexto;
        contexto.idEmpresa =  req.empresa.id;
        contexto.empresa =  req.empresa;

        let service: IFonteProdutosExternos = req.empresa.integracaoDelivery.obtenhaService();

        console.log('Buscar produtos do ' +  req.empresa.integracaoDelivery.sistema);
        let produtosErp: any = await service.listeProdutosConvertidos( null);

        console.log('Total de produtos listados: ' + produtosErp.length)

        let produtosAtualizar: any = [];

        for(let i = 0  ; i < produtosSemImagem.length; i++){
          let produtoSemImagem: Produto =  produtosSemImagem[i];

           if(produtoSemImagem.codigoPdv){
             let produtoErp: Produto = produtosErp.find((item: any) => item.codigoPdv === produtoSemImagem.codigoPdv)

             if(!produtoErp){
               console.log(String(`Produto não encontrado: ${produtoSemImagem.codigoPdv} => ${produtoSemImagem.nome}`))
               naoEncontrados.push(produtosErp);
               continue;
             }

             if((produtoErp as any).linkImagem){
               let dto: DTOProdutoSincronizar =  new DTOProdutoSincronizar(produtoSemImagem, null, true);
               dto.setNovaImagem(produtoErp);

               produtoSemImagem.imagens = produtoErp.imagens;
               produtosAtualizar.push(dto)
             }
           }
        }

        console.log('Total de produtos nao encontrados: ' + naoEncontrados.length)
        console.log('Total de produtos atualizar: ' + produtosAtualizar.length)

        let importador: ImportadorProduto = new ImportadorProduto();

        for(let i = 0; i < produtosAtualizar.length; i++){
          let produtoAtualizar: DTOProdutoSincronizar =  produtosAtualizar[i];
          console.log(String(`Baixar imagem produto: ${produtoAtualizar.codigoPdv} => ${produtoAtualizar.nome}`))
          await importador.sincronizeImagem(produtoAtualizar)
        }

        cbAsync();
      }, 0)
    }

    res.json(Resposta.sucesso(String(`disparou baixar imagens de ${produtosSemImagem.length} produtos`)))
})

router.get('/formaspagamento/mesas/ativas', async (req: any, res ) => {
  let formasEmpresaIntegradas = req.empresa.formasDePagamento;

  let integracao: IntegracaoEcleticaERP = req.empresa.integracaoDelivery;

  if(integracao){
    if(integracao.integradoComServicoDMWS()){
      let service = integracao.obtenhaService();

      let bandeirasMesa: any = await (service as EcleticaService).listaBandeirasDMWS().catch((err: any) => {
          console.error(err) ;
          res.json(Resposta.erro(err));
      });

      if(bandeirasMesa){
        formasEmpresaIntegradas =  formasEmpresaIntegradas.filter((formaPagamento: any) =>  formaPagamento.exibirEmMesas(bandeirasMesa) )

        res.json(Resposta.sucesso(formasEmpresaIntegradas))
      }
    } else {
      res.json(Resposta.sucesso(formasEmpresaIntegradas))
    }
  } else {
    res.json(Resposta.erro('Nenhum integração ativa'));
  }

})


router.get('/ecletica/dmws/configuracoes', async (req: any, res ) => {
    let integracao: IntegracaoEcleticaERP = req.empresa.integracaoDelivery;

    if(integracao){
      let service = integracao.obtenhaService();

      let retorno = await (service as EcleticaService).obtenhaConfiguracoesLoja().catch((err: any) => {
        console.error(err)
        res.json(Resposta.erro(err));
      });

      if(retorno)
        res.json(retorno)

    } else {
      res.json(Resposta.erro('Nenhum integração ativa'));
    }
})

router.post('/comanda/prechamento', async (req: any, res ) => {
  let dados: any = req.body;

  const comandaService: ComandaService = new ComandaService();

  comandaService.preFechamento(dados, req.empresa).then( (comanda: Comanda) => {
    let resposta: any = { comanda: new DTOComanda(comanda,  req.empresa)};

    res.json(Resposta.sucesso(resposta));
  }).catch((erro) => {
    res.json(Resposta.erro(erro))
  })


})

router.get('/ecletica/dmws/bandeiras', async (req: any, res ) => {
    let integracao: IntegracaoEcleticaERP = req.empresa.integracaoDelivery;

    if(integracao){
      let service = integracao.obtenhaService();

      let retorno = await (service as EcleticaService).listaBandeirasDMWS().catch((err: any) => {
        console.error(err)
        res.json(Resposta.erro(err));
      });

      if(retorno)  res.json(retorno)
    } else {
      res.json(Resposta.erro('Nenhum integração ativa'));
    }
})

router.get('/ecletica/dmws/produtos', async (req: any, res ) => {
    let integracao: IntegracaoEcleticaERP = req.empresa.integracaoDelivery;

    if(integracao){
      let service = integracao.obtenhaService();

      let retorno = await (service as EcleticaService).listeProdutosDMWS().catch((err: any) => {
        console.error(err)
        res.json(Resposta.erro(err));
      });

      if(retorno)  res.json(Resposta.sucesso(retorno))
    } else {
      res.json(Resposta.erro('Nenhum integração ativa'));
    }
})
router.get('/me/operadores/pdv', async (req: any, res ) => {
    let integracao: IntegracaoEcleticaERP = req.empresa.integracaoDelivery;

    if(integracao){
      let service = integracao.obtenhaService();

      let operacao = integracao.integrarComComandas() ? 'comanda' : 'mesa';

      let retorno = await (service as EcleticaService).listeOperadoresDMWS(operacao).catch((err: any) => {
        console.error(err)
        res.json(Resposta.erro(err));
      });

      if(retorno)  res.json(Resposta.sucesso(retorno))
    } else {
      res.json(Resposta.erro('Nenhum integração ativa'));
    }
})

router.get('/produtos/faltantes', temIntegracaoAtiva, async (req: any, res ) => {
  let service = req.empresa.integracaoDelivery.obtenhaService();

  let produtosErp  = await service.listeProdutosIndisponiveis( ).catch( (err: any) => {
    res.json(Resposta.erro(err))
  })

  res.json(produtosErp)

})


router.post('/ecletica/notificacao/mesa/execute', async (req: any, res: any) => {
   let dados: any = req.body;

   let notificacao = Object.assign(new NotificacaoMesa(), dados)

   notificacao.empresa  = req.empresa;
   await   NotificacaoMesaService.executeFecharConta(notificacao);

   if(notificacao.executada){
     res.json(Resposta.sucesso())
   } else {
     res.json(Resposta.erro(notificacao.erro))
   }
})

export const IntegracaoERPController: Router = router;
