import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {Vantagem} from "../domain/faturamento/Vantagem";

const router: Router = Router();

router.post('/', async (req, res) =>  {
  let dados = req.body;

  let vantagem = new Vantagem(null, dados.descricao);

  await vantagem.salve(true);

  res.json(Resposta.sucesso({id: vantagem.id }));
})

router.put('/', async (req, res) =>  {
  let dados = req.body;

  let vantagem: Vantagem = await Vantagem.get(dados.id);

  vantagem.descricao = dados.descricao;

  await vantagem.atualize();

  res.json(Resposta.sucesso( {}))

})

router.get('/', async (req, res) => {
  let vangatens = await Vantagem.liste();

  res.json(Resposta.sucesso(vangatens));
})

export const VantagensController: Router = router;
