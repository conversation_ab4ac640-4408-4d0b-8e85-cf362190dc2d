import {Router} from "express";
import {Empresa} from "../domain/Empresa";
import {EnumMeioDePagamento} from "../domain/delivery/EnumMeioDePagamento";
import {ERedeItauApi} from "../lib/ERedeItauApi";
import {MapeadorDeFormaDePagamento} from "../mapeadores/MapeadorDeFormaDePagamento";
import {MCITokenizeUtils} from "../lib/integracao/MCITokenizeUtils";
import * as moment from "moment";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {Pedido} from "../domain/delivery/Pedido";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {NotificacaoPedidoService} from "../service/NotificacaoPedidoService";
import {NotificacaoERede} from "../domain/faturamento/NotificacaoERede";
import {NotificacaoMeioPagamentoService} from "../service/NotificacaoMeioPagamentoService";

const router: Router = Router();

router.post('/cartao/tokenize', async (req: any, res: any) => {
  const empresa: Empresa = req.empresa;
  const {email, number, exp_month, exp_year, holder_name, cvv, kind} = req.body;

  const formaProcurada = empresa.obtenhaFormaDePagamentoDoMeio(EnumMeioDePagamento.ERede);


  new   MapeadorDeFormaDePagamento().selecioneSync({id: formaProcurada.id}).then(
    async (formaDePagamento: any) => {
    let service: ERedeItauApi = new ERedeItauApi(formaDePagamento.configMeioDePagamento)

    let token: any, isVisaCard = number.toString().startsWith('4');

    let testVisaCard = number.toString().startsWith('00000000000');

    if(formaDePagamento.configMeioDePagamento.tokenizar && (isVisaCard || testVisaCard)){
      token = await service.crieTokenCartao(email, number, exp_month, exp_year, holder_name, cvv)
        .catch((err) => {
          console.log(err)
          res.status(400).json({erro: err})
        });
      if(token){
        res.json(token)
      }

     }  else {

      let payload: any = {
         email: email, numero: number, mes: exp_month, ano: exp_year, nome: holder_name, cvv: cvv,
         tipo: kind,
         expiresIn: moment().add(2, 'h').toDate()
      }

      token = MCITokenizeUtils.encryptCardData(payload, formaDePagamento.id)
      res.json(token)
    }


  })
})

router.get('/pedido/pagamento/teste', async (req: any, res: any) => {
  let   formaDePagamento = await new MapeadorDeFormaDePagamento().selecioneSync(14660); //

  let transacao: any = await
    new ERedeItauApi(formaDePagamento.configMeioDePagamento).obtenhaTransacao('41162409181825154474').catch((err) => {
      res.json(err);
    })


  if (transacao)
    res.json(transacao);

})

router.get('/pedido/:guid/pagamento', async (req: any, res: any) => {
  let formaDePagamento =
    req.empresa.formasDePagamento.find((item: any) =>   item.ERede());

  if(!formaDePagamento) return  res.json({erro: 'Forma pagamento Erede nao encontrado'})

  let pedido: Pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

  if(!pedido) return   res.json({erro: 'Pedido nao encontrado'});
  let pagamentoOnline: any = pedido.obtenhaPagamentoOnline();

  if(pagamentoOnline) {
    formaDePagamento = await new MapeadorDeFormaDePagamento().selecioneSync(formaDePagamento.id);
    let erro: string;
    let transacao: any = await
      new ERedeItauApi(formaDePagamento.configMeioDePagamento).obtenhaTransacao(pagamentoOnline.codigoTransacao).catch((err) => {
        erro = err
      })

    if (transacao)
      res.json(transacao);
    else {
      res.json({erro: erro || 'Nenhuma transação econtrada para: ' + pagamentoOnline.codigoTransacao})
    }
  } else {
    res.json({erro: 'Pagamento online nao encontrado no pedido'})
  }
})

router.get('/pedido/:guid/sincronize', async (req: any, res: any) => {
  let formaDePagamento =
    req.empresa.formasDePagamento.find((item: any) =>   item.ERede());

  if(!formaDePagamento) return  res.json({erro: 'Forma pagamento Erede nao encontrado'})

  let pedido: Pedido = await new MapeadorDePedido().selecioneSync({ guid: req.params.guid});

  if(!pedido) return   res.json({erro: 'Pedido nao encontrado'});

  let pagamentoOnline: any = pedido.obtenhaPagamentoOnline();

  if(pagamentoOnline){
    let erro =
      await new ERedeItauApi(formaDePagamento.configMeioDePagamento).sincronizePagamento(pedido, pagamentoOnline, req.empresa );

    if(!erro){
      delete pagamentoOnline.pedido;
      res.json(pagamentoOnline);
    } else {
      res.json({erro: erro || 'Nenhuma transação econtrada para: ' + pagamentoOnline.codigoTransacao})
    }
  } else {
    res.json({erro: 'Pagamento online nao encontrado no pedido'})
  }

})


router.post('/3ds/retorno/sucesso/:id', async (req: any, res: any) => {
  console.log('retornou 3ds autenticado')
  console.log(req.body)
  let pedido: Pedido = await new MapeadorDePedido().selecioneSync(req.params.id);

  let pagamento: any = pedido.obtenhaPagamentoOnline();
  let transacao: any = req.body;
  await pagamento.atualizeRetornoRede(transacao);

  ExecutorAsync.execute( async (cbAsync: any) => {
    await NotificacaoPedidoService.executeEredePostback(req.empresa, pedido, pagamento)
    cbAsync();
  }, (erroAsync: any) => { console.log(erroAsync)}, 100);


  // Redirecionar para uma página que enviará a mensagem ao pai
  //res.redirect('/loja/3ds/retorno/success-page');
  res.redirect(`/loja/pedido/acompanhar/${pedido.guid}/3dsretorno/success`);

})

router.post('/3ds/retorno/falha/:id', async (req: any, res: any) => {
  console.log('retornou 3ds erro autenticação')
  console.log(req.body)
  let pedido: Pedido = await new MapeadorDePedido().selecioneSync(req.params.id);

  let pagamento: any = pedido.obtenhaPagamentoOnline();
  let transacao: any = req.body;

  let msgErroAutenticacao: string  = transacao['threeDSecure.returnMessage'] ;
  let codigoErroAutenticacao: any = transacao['threeDSecure.returnCode'];
  let msgErroBandeira: string  = transacao['brand.returnMessage'];
  let returnMessage: string  = transacao['returnMessage'];

  if(codigoErroAutenticacao && codigoErroAutenticacao.toString() === '202')
    msgErroAutenticacao = 'Cartão não foi autenticado (3ds)'

  let msgErro = msgErroBandeira && msgErroBandeira.trim() !== '' ? msgErroBandeira :
    (msgErroAutenticacao  && msgErroAutenticacao.trim() !== '' ? msgErroAutenticacao : returnMessage );

  await pagamento.atualizeRetornoRede({tid:  transacao.tid, erro: msgErro})

  // no iframe Redirecionar para uma página que enviará a mensagem ao pai
  //res.redirect('/loja/3ds/retorno/error-page?err=' + msgErro);

  res.redirect(`/loja/pedido/acompanhar/${pedido.guid}/3dsretorno/error?autherr=` + msgErro);

})

router.post('/hooks/tokenization', async (req: any, res: any) => {
  console.log('notificou tokenização');
  console.log(req.body)

  const {id, merchantId, events, data} = req.body;

  let dados: any = {events: events, merchantId: merchantId};

  if(data.tokenizationId){
    let notificacao = new NotificacaoERede(id, 'tokenization', data.tokenizationId, dados);
    await notificacao.salve();

    ExecutorAsync.execute( async (cbAsync: any) => {
      await NotificacaoMeioPagamentoService.executeDaRede(notificacao)
      cbAsync();
    }, (err: any) => { console.error(err)}, 2000);
  } else {
    console.log('ignorar notificação')
  }



  res.json({sucess: true});
})

export const ERedeItauController: Router = router;

