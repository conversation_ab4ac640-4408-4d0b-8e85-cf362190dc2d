import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {EmpresaService} from "../service/EmpresaService";
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import {Empresa} from "../domain/Empresa";
import {MapeadorDeBrinde} from "../mapeadores/MapeadorDeBrinde";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {MapeadorDeFoto} from "../mapeadores/MapeadorDeFoto";
import {MapeadorDeAtividade} from "../mapeadores/MapeadorDeAtividade";
import {MapeadorDeUsuario} from "../mapeadores/MapeadorDeUsuario";
import {ContatoEmpresa} from "../domain/ContatoEmpresa";
import {HorarioFuncionamento} from "../domain/HorarioFuncionamento";
import {ContatoService} from "../service/ContatoService";
import {MapeadorDePlano} from "../mapeadores/MapeadorDePlano";
import {MapeadorDePet} from "../mapeadores/MapeadorDePet";
import {Categoria} from "../domain/delivery/Categoria";
import {MapeadorDeCategoria} from "../mapeadores/MapeadorDeCategoria";
import {Ambiente} from "../service/Ambiente";
import {MapeadorDeProspect} from "../mapeadores/MapeadorDeProspect";
import {ApiCloudfare} from "../lib/cloudfare/ApiCloudfare";
import {Prospect} from "../domain/faturamento/Prospect";
import {MapeadorDeNotificacao} from "../mapeadores/MapeadorDeNotificacao";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {Notificacao} from "../domain/Notificacao";
import {ConfigImpressao} from "../domain/ConfigImpressao";
import {MapeadorDeConfigImpressao} from "../mapeadores/MapeadorDeConfigImpressao";
import {MapeadorDeProdutoTemplate} from "../mapeadores/MapeadorDeProdutoTemplate";
import {MapeadorDeSegmento} from "../mapeadores/MapeadorDeSegmento";
import {IntegracaoDelivery} from "../domain/integracoes/IntegracaoDelivery";
import {MapeadorDeIntegracaoDelivery} from "../mapeadores/MapeadorDeIntegracaoDelivery";
import {FactoryIntegracaoDelivery} from "../domain/integracoes/FactoryIntegracaoDelivery";
import {MapeadorDePausaProgramada} from "../mapeadores/MapeadorDePausaProgramada";
import {ProdutoTemplate} from "../domain/templates/ProdutoTemplate";
import {MapeadorDeRegistroDeOperacao} from "../mapeadores/MapeadorDeRegistroDeOperacao";
import {UsuarioService} from "../service/UsuarioService";
import {MapeadorDeCidadeEntrega} from "../mapeadores/MapeadorDeCidadeEntrega";
import {MapeadorFormaDeEntregaEmpresa} from "../mapeadores/MapeadorFormaDeEntregaEmpresa";
import {FormaDeEntrega} from "../domain/delivery/FormaDeEntrega";
import {DominioDaEmpresa} from "../domain/DominioDaEmpresa";
import {FiltroTelaProdutos} from "../utils/FiltroTelaProdutos";
import {Usuario} from "../domain/Usuario";
import {ConfigWhatsapp} from "../domain/ConfigWhatsapp";
import {MapeadorDeConfigWhatsapp} from "../mapeadores/MapeadorDeConfigWhatsapp";
import {DTOObjetoComNome} from "../domain/DTOObjetoComNome";
import {MapeadorDeCancelamentoEmpresa} from "../mapeadores/MapeadorDeCancelamentoEmpresa";
import {MapeadorDeProdutoTemplateAdicional} from "../mapeadores/MapeadorDeProdutoTemplateAdicional";
import {EnumTemas} from "../domain/temas/EnumTemas";
// @ts-ignore
import moment = require("moment");
// @ts-ignore
import _ = require("underscore");
import {PizzaTamanhoSaboresDePara} from "../domain/integracoes/PizzaTamanhoSaboresDePara";
import {MapeadorDePapel} from "../mapeadores/MapeadorDePapel";
import {Papel} from "../domain/permisao/Papel";
import {Tag} from "../domain/Tag";
import {RotaGuard} from "../lib/permissao/RotaGuard";
import {Catalogo} from "../domain/catalogo/Catalogo";
import {MapeadorDeCatalogo} from "../mapeadores/MapeadorDeCatalogo";
import {MapeadorDeRede} from "../mapeadores/MapeadorDeRede";
import {MapeadorDeTag} from "../mapeadores/MapeadorDeTag";
import {LojaTrendFoods} from "../domain/integracoes/LojaTrendFoods";
import {GcomERPService} from "../service/integracoes/GcomERPService";
import {CacheUtils} from "../service/CacheUtils";
import {OpcaoDeAdicionalDeProduto} from "../domain/delivery/OpcaoDeAdicionalDeProduto";
import {MapeadorDeOpcaoDeAdicionalDeProduto} from "../mapeadores/MapeadorDeOpcaoDeAdicionalDeProduto";
import {EmpresaMock} from "../EmpresaMock";
import {EnumOperacao} from "../domain/auditoria/EnumOperacao";
import {ProdutoTemplateTamanho} from "../domain/templates/ProdutoTemplateTamanho";
import { MapeadorDeTemaPersonalizado } from "../mapeadores/MapeadorDeTemaPersonalizado";
import {NumeroWhatsapp} from "../domain/NumeroWhatsapp";

import {MapeadorMotivoCancelamentoEmpresa} from "../mapeadores/MapeadorMotivoCancelamentoEmpresa";
import {PausaProgramada} from "../domain/delivery/PausaProgramada";

moment.locale('pt-br');


const router: Router = Router();

function logado(req: any, res: any, next: any){
  let usuario: any = req.user;

  if(!usuario)  return res.json(Resposta.erro('Faça login para realizar a operação'))

  return next();
}

function ehAdmin(req: any, res: any, next: any){
  let usuario: any = req.user;

  let mapeador = new MapeadorDeUsuario()

  if(!usuario)  return res.json(Resposta.erro('Faça login para realizar a operação'))

  mapeador.selecioneSync(usuario).then((usuarioAtualizado: any) => {
    if(!usuarioAtualizado || !usuarioAtualizado.admin)   return res.json(Resposta.erro('Operação não permitida'))

    return next();
  });
}

function ehAdminRede(req: any, res: any, next: any){
  let usuario: any = req.user;

  let mapeador = new MapeadorDeUsuario()

  if(!usuario)  return res.json(Resposta.erro('Faça login para realizar a operação'))

  mapeador.selecioneSync(usuario).then((usuarioAtualizado: any) => {
    if(!usuarioAtualizado || !usuarioAtualizado.adminRede)   return res.json(Resposta.erro('Operação não permitida'))

    return next();
  });
}

router.get('/altereEmpresaMock/:dominio', ehAdmin, async(req: any, res: any) => {
  //a propriedade estática domínio da classe EmpresaMock deve ser estada para o parâmetro passado

  EmpresaMock.dominio = req.params.dominio;
  res.json(Resposta.sucesso('Sucesso!'));
})

router.get('/salveConfiguracaoWhatsapp', async(req: any, res: any) => {
  const empresa = req.empresa;
  const novoTempo = req.query.t;

  const configWhatsapp = new ConfigWhatsapp();
  configWhatsapp.empresa = empresa;
  configWhatsapp.ajusteTempoEmHoras(novoTempo);

  const mapeador = new MapeadorDeConfigWhatsapp();

  if( !empresa.configWhatsapp ) {
    await mapeador.insiraGraph(configWhatsapp);
  } else {
    await mapeador.atualizeSync(configWhatsapp);
  }

  await new MapeadorDeEmpresa().removaDasCaches(empresa);

  res.json(Resposta.sucesso({
    mensagem: "Alterado"
  }));
});

router.get('/cidadesEntrega', async(req: any, res: any) => {
  const mapeador = new MapeadorDeCidadeEntrega();

  const cidadesEscolhidas = await mapeador.listeAsync({});

  res.json(Resposta.sucesso(cidadesEscolhidas));
});


router.get('/',  ehAdmin, async (req: any, res: any) => {
  let filtro: any = {};

  if(req.query.a) filtro.ativas = true;
  if(req.query.b) filtro.bloqueadas = true;
  if(req.query.n) {
    filtro.naoUsaram = true;
    filtro.ordernaMaisNovas = true;
  }
  if(req.query.o) {
    filtro.oportunidades = true;
    filtro.ordernaMaisNovas = true;
  }
  if(req.query.pb) filtro.proximosBloqueios = true;
  if(req.query.ufid) filtro.estadoId = req.query.ufid;
  if(req.query.vdif) filtro.vencimentoDiferente = true;
  if(req.query.q) filtro.termo = String(`%${req.query.q}%`);
  if(req.query.dt) filtro.dataCadastro = moment(req.query.dt).format('YYYY-MM-DD')

  if(Number(req.query.q) > 0){
    delete filtro.termo;
    filtro.id = Number(req.query.q)
  }


  let empresas = await   new MapeadorDeEmpresa().selecioneBusca(filtro)

  empresas.forEach( (empresa: any) => {
    empresa.bloqueada = empresa.venceuDataBloqueio();
    if(empresa.dataBloqueioAuto)
      empresa.diasDoBloqueio = moment(empresa.dataBloqueioAuto).diff(moment(), 'd' ) + 1
  })

  res.json(Resposta.sucesso(empresas))

});


router.get('/resumo', ehAdmin, async (req: any, res: any) => {
  let filtro: any = {};

  if(req.query.a) filtro.ativas = true;
  if(req.query.b) filtro.bloqueadas = true;
  if(req.query.n) filtro.naoUsaram = true;
  if(req.query.ufid) filtro.estadoId = req.query.ufid;
  if(req.query.q) filtro.termo = String(`%${req.query.q}%`);

  if(req.query.dt)
    filtro.dataCadastro = moment(req.query.dt).format('YYYY-MM-DD')

  let empresas = await   new MapeadorDeEmpresa().selecioneTodasComResumo(filtro);

  for( const empresa of empresas ) {
    empresa.bloqueada =  empresa.estaBloqueada();

    if( empresa.ultimaAcao ) {
      empresa.tempoAtras = moment(empresa.ultimaAcao.horario).fromNow();
    } else {
      empresa.tempoAtras = '-'
    }
  }

  res.json(Resposta.sucesso(empresas));
});


router.get('/empresas-rede/chinagcom', ehAdminRede, async (req: any, res: any) => {
  const usuario = req.user;
  const empresa = req.empresa;
  const query: any = {
    redeDaEmpresa: req.empresa.dadosRede.grupo,
    lojasChinaGcom: true
  }

  new MapeadorDeEmpresa().listeEmpresasRede(query).then( async (empresas) => {
    res.json(Resposta.sucesso({
      empresas: empresas,
      total: empresas.length
    }));
  })
})

router.get('/empresas-rede/china', ehAdminRede, async (req: any, res: any) => {
  const usuario = req.user;
  const query: any = {
    redeDaEmpresa: req.empresa.dadosRede.grupo,
    lojasChinaEcletica: true
  }

  new MapeadorDeEmpresa().listeEmpresasRede(query).then( async (empresas) => {
    res.json(Resposta.sucesso({
      empresas: empresas,
      total: empresas.length
    }));
  })
});

router.get('/empresas-rede/gendai', ehAdminRede, async (req: any, res: any) => {
  const usuario = req.user;
  const query: any = {
    redeDaEmpresa: req.empresa.dadosRede.grupo,
    lojasGendaiEcletica: true
  }

  new MapeadorDeEmpresa().listeEmpresasRede(query).then( async (empresas) => {
    res.json(Resposta.sucesso({
      empresas: empresas,
      total: empresas.length
    }));
  })
});

router.get('/empresas-rede/gendaigcom', ehAdminRede, async (req: any, res: any) => {
  const usuario = req.user;
  const query: any = {
    redeDaEmpresa: req.empresa.dadosRede.grupo,
    lojasGendaiGcom: true
  }

  new MapeadorDeEmpresa().listeEmpresasRede(query).then( async (empresas) => {
    res.json(Resposta.sucesso({
      empresas: empresas,
      total: empresas.length
    }));
  })
});

router.get('/empresas-grupo/me', ehAdminRede, async (req: any, res: any) => {
  let resposta: any  = {
    empresas: [],
    total: 0
  }

  if( req.empresa.grupoDaLoja){
    const query: any = {
      idGrupo: req.empresa.grupoDaLoja.id,
      buscaRasa: true
    }

    let empresas: any = await new MapeadorDeEmpresa().listeEmpresasRede(query);

    if(empresas){
      resposta.empresas = empresas;
      resposta.total = empresas.length;
    }
  }

  res.json(Resposta.sucesso( resposta));
});

router.get('/empresas-rede/me', ehAdminRede, async (req: any, res: any) => {
  let resposta: any  = {
    empresas: [],
    total: 0
  }

  if( req.empresa.rede){
    const query: any = {
      redeDaEmpresa: req.empresa.rede,
      buscaRasa: true
    }

    let empresas: any = await new MapeadorDeEmpresa().listeEmpresasRede(query);

    if(empresas){
      resposta.empresas = empresas;
      resposta.total = empresas.length;
    }
  }

  res.json(Resposta.sucesso( resposta));
});

router.get('/catalogo-modelo/:id', ehAdminRede, async (req: any, res: any) => {
  new MapeadorDeEmpresa().listeEmpresasRede({idModeloCatalogo: req.params.id, buscaRasa: true}).then( async (empresas) => {
    res.json(Resposta.sucesso({
      empresas: empresas,
      total: empresas.length
    }));
  })
});


router.get('/empresas-rede', ehAdminRede, async (req: any, res: any) => {
  const usuario = req.user;
  const inicio = req.query.i || 0,
    total = req.query.t || 10;
  const query: any = {
    inicio: parseInt(inicio, 10),
    total: parseInt(total, 10),
    buscaRasa: true,
    q: '%' + req.query.q + '%'
  };
  query['redeDaEmpresa'] = req.empresa.dadosRede.grupo;

  if( !req.query.q ) {
    query.q = null;
  }

  new MapeadorDeEmpresa().listeEmpresasRede(query).then( async (empresas) => {
    for( const empresa of empresas ) {
      empresa.bloqueada =  empresa.estaBloqueada();

      if( empresa.ultimaAcao ) {
        empresa.tempoAtras = moment(empresa.ultimaAcao.horario).fromNow();
      } else {
        empresa.tempoAtras = '-'
      }
    }

    const qtdeLojas = await new MapeadorDeEmpresa().selecioneTotalEmpresasRede(query);

    res.json(Resposta.sucesso({
      empresas: empresas,
      total: qtdeLojas
    }));
  });
});

router.get('/me', async (req: any, res) => {
    let empresa: any = req.empresa;

    empresa.exibirWizard =  !empresa.completouWizard();

    if(empresa.temPedidos())
      empresa.linkLoja = empresa.obtenhaLinkLoja(Ambiente._instance.producao)

    const mapeadorDeNotificacao = new MapeadorDeNotificacao();

    const notificacao: Notificacao = await mapeadorDeNotificacao.selecioneSync({
      tipoDeNotificacao: TipoDeNotificacaoEnum.MensagemSaudacaoWhatsappPedido
    });

    empresa.enviarMensagemSaudacao = (notificacao && notificacao.ativada);

    res.json(empresa);
});

router.get('/cnpj/:cnpj' , async (req: any, res) => {
  let empresas = await new MapeadorDeEmpresa().listeAsync({ cnpj: req.params.cnpj});

  res.json(Resposta.sucesso(empresas))
});

router.get('/leads', ehAdmin, (req: any, res) => {

  const inicio = req.query.i || 0,
    total = req.query.t || 100,
    query: any = { inicio: parseInt(inicio, 10), total: parseInt(total, 10) };

  query.naoCadastrou = true;

  let mapeador = new MapeadorDeProspect();

  mapeador.listeAsync(query).then((resultado) => {
    resultado.forEach( (lead: Prospect) => {lead.obtenhaDados(); });
    res.json(Resposta.sucesso(resultado));
  })
})

router.put('/leads/cadastrou', ehAdmin, (req, res) => {
  const idLead = req.body.id;
  const user: any = req.user;

  if(!idLead)
    return res.json(Resposta.erro("É necessário informar o id do lead cadastrado"))

  let mapeador = new MapeadorDeProspect()

  let query = {id: idLead, cadastrou: true, operador: req.user, atualizacao: new Date()}

  console.log('cadastrou lead:' + idLead)
  console.log(String(`${user.id} -  ${user.nome}`))

  mapeador.cadastrouLead(query).then((resultado: any) => {
    return res.json(Resposta.sucesso(query))
  })

})

router.get('/todas', ehAdmin, async(req: any, res: any) => {
  const mapeador = new MapeadorDeEmpresa();

  const empresas: Array<DTOObjetoComNome> = await mapeador.selecioneTodasDTO();

  res.json(Resposta.sucesso(empresas));
});

router.get('/categorias/ordens/recalcule/todas', ehAdmin, async(req: any, res: any) => {
  const empresas: Array<DTOObjetoComNome> = await new MapeadorDeEmpresa().selecioneTodasDTO();
  let contexto = require('domain').active.contexto;

  for(let i = 0; i < empresas.length; i++){
    let empresa: any = empresas[i];
    contexto.empresa = empresa;
    contexto.idEmpresa = empresa.id;

    console.log(String(`Recalculando ordens empresa ${empresa.nome}`));
    await new MapeadorDeCategoria(empresa.catalogo).recalculeOrdens(empresa);
    await new MapeadorDeEmpresa().removaDasCaches(empresa)
  }

  res.json(Resposta.sucesso('Sucesso! Total: ' + empresas.length));
});

router.get('/valideDominio', async(req: any, res: any) => {
  const dominio = req.query.d;

  let empresaDaRequest = await new MapeadorDeEmpresa().selecioneCachePorDominio(dominio);

  if( !empresaDaRequest ) {
    return res.json(Resposta.erro('Domínio inválido'));
  }

  res.json(Resposta.sucesso('Sucesso!'));
});

router.get('/canceladas', ehAdmin, async(req: any, res: any) => {
  let query: any = {ativadas: true};

  if(req.query.opt)
    query = { oportunidades: true};

  if(req.query.dt)
    query.dataCancelamento = moment(req.query.dt).format('YYYY-MM-DD')

  let cancelamentos = await new MapeadorDeCancelamentoEmpresa().listeAsync(query);

  res.json(Resposta.sucesso(cancelamentos ));
})

router.get('/redes', (req, res) => {
  const mapeador = new MapeadorDeRede();

  mapeador.listeAsync({ } ).then(  redes => {
    res.json(Resposta.sucesso(redes))
  })
})

router.get('/redes/grupo/:grupo', (req, res) => {
  const mapeador = new MapeadorDeRede();

  const grupo = req.params.grupo

  if(!grupo)
    return res.json(Resposta.erro("É necessário informar o grupo a listar."))

  mapeador.desativeMultiCliente()

  mapeador.listeAsync({grupo: grupo } ).then(  redes => {
    res.json(Resposta.sucesso(redes))
  })
})


router.get('/:idEmpresa', async (req, res) => {
  const idEmpresa = req.params.idEmpresa;

  new MapeadorDeEmpresa().selecioneSync({id: idEmpresa}).then( empresa => {
    res.json(Resposta.sucesso(empresa));
  });
});

router.get('/:idEmpresa/brindes', async (req, res) => {
  const idEmpresa = req.params.idEmpresa;

  new MapeadorDeBrinde().listeAsync({idEmpresa: idEmpresa}).then( (brindes: any) => {
    res.json(Resposta.sucesso(brindes));
  });
});

router.get('/:idEmpresa/produtos', async (req: any, res) => {
  let dados: any = new FiltroTelaProdutos(req.query, req.empresa).toSqlQuery();

  new MapeadorDeProduto(req.empresa.catalogo).listeAsync(dados).then( (produtos: any[]) => {
    for(let produto of produtos) {
      let valorMinimo = produto.obtenhaValorMinimo()

      if(valorMinimo > produto.preco)
        produto.valorMinimo = valorMinimo

      if(dados.termo && produto.camposAdicionais) {
        let termoDebusca = dados.termo.toLowerCase().replaceAll('+', '').replaceAll('*', '')
        let camposAdicionaisFiltrados = []

        for(let campoAdicional of produto.camposAdicionais) {
          let opcoes = campoAdicional.opcoesDisponiveis
          campoAdicional.opcoesDisponiveis = []

          if(!opcoes) continue;

          for(let opcao of opcoes)
            if(opcao.nome.toLowerCase().indexOf(termoDebusca) >= 0)
              campoAdicional.opcoesDisponiveis.push(opcao)

          if(campoAdicional.opcoesDisponiveis.length > 0)
            camposAdicionaisFiltrados.push(campoAdicional)
        }


        produto.camposAdicionais = camposAdicionaisFiltrados
      }

    }


    new MapeadorDeProduto(req.empresa.catalogo).obtenhaQtdeProdutos(dados).then( (qtde: any) => {
      res.json(Resposta.sucesso({
        produtos: produtos,
        total: qtde
      }));
    });
  });
});

router.get('/:idEmpresa/fotos', async (req, res) => {
  const idEmpresa = req.params.idEmpresa;

  new MapeadorDeFoto().listeAsync({idEmpresa: idEmpresa}).then( (fotos: any) => {
    res.json(Resposta.sucesso(fotos));
  });
});
router.get('/:idEmpresa/atividades', async (req, res) => {
  const idEmpresa = req.params.idEmpresa;

  new MapeadorDeAtividade().listeAsync({idEmpresa: idEmpresa}).then( (atividades: any) => {
    res.json(Resposta.sucesso(atividades));
  });
});

router.post('/', async(req, res) => {
  const dados = req.body,
        dadosResponsavel = dados.responsavel;

  const empresa = new Empresa();

  Object.assign(empresa, dados);
  empresa.meioDeEnvio = EnumMeioDeEnvio.Mock;
  empresa.exibirBandeiras = true;

  const catalogo = new Catalogo(empresa.nome);

  new MapeadorDeCatalogo().insiraGraph(catalogo).then(() => {
    empresa.catalogo = catalogo

    new EmpresaService().insira(empresa, dadosResponsavel).then( (atualize) => {
      let dadosResposta: any = {
        id: empresa.id,
        localizacao: dados.latitudeLongitude
      };

      if(empresa.responsavel)
        dadosResposta.idResponsavel = empresa.responsavel.id;

      let apiCloudfarePromokit = ApiCloudfare.getApiPromokit()
      let apiCloudfareMeuCardapio = ApiCloudfare.getApiMeuCardapio()
      let apiCloudfareMeuCatalogo = ApiCloudfare.getApiMeuCatalogo()

      let ip = '**************'
      if(Ambiente.Instance.producao) {
        apiCloudfarePromokit.crieRegistroAParaNovaEmpresa(empresa, ip).then((cadastrouPromokit: any) => {
          apiCloudfareMeuCardapio.crieRegistroAParaNovaEmpresa(empresa, ip).then((cadastrouMeuCardapio: any) => {
            apiCloudfareMeuCatalogo.crieRegistroAParaNovaEmpresa(empresa, ip).then((cadastrouMeuCatalogo: any) => {
              res.json(Resposta.sucesso( dadosResposta));
            })
          })
        })
      } else {
        res.json(Resposta.sucesso( dadosResposta));
      }
    }).catch( (erro: any) => {
      res.json( Resposta.erro(erro))
    })

  })

});

export const encontrarTema = (tema: string): EnumTemas | null => {
  for (const prop in EnumTemas) {
    // @ts-ignore
    if (EnumTemas[prop] === tema)    return EnumTemas[prop] ;
  }

  return null;
};


router.post('/ativeTema', async(req: any, res: any) => {
  const empresa: Empresa = req.empresa;
  const ativar = Number(req.query.a);
  const tema = req.query.t;

  const temaEhNumero = !isNaN(Number(tema));

  console.log('tema: ' + tema);

  if( ativar === 1 ) {
    //checa se tema é um numero, se sim é um tema personalizado
    console.log('checando se tema é numero')
    if( temaEhNumero ) {
      console.log('buscando tema');
      //buscar tema personalizado
      empresa.temaPersonalizado = await new MapeadorDeTemaPersonalizado().selecioneSync({id: tema});
      empresa.tema = EnumTemas.TemaPersonalizado;
    } else {
      console.log('não achou');

      let novoTema = encontrarTema(tema);
      empresa.tema = novoTema;
    }
  } else {
    empresa.tema = null;
    empresa.temaPersonalizado = null;
  }

  console.log(empresa.temaPersonalizado);

  const mapeadorDeEmpresa = new MapeadorDeEmpresa();

  await mapeadorDeEmpresa.atualizeTema(empresa);

  res.json(Resposta.sucesso(true));
});

router.put('/', async(req, res) => {
  const dados = req.body;

  new EmpresaService().atualize(dados).then( (empresa) => {
    res.json(Resposta.sucesso({
      localizacao: dados.latitudeLongitude
    }))
  }).catch( (erro: any) => {
    res.json( Resposta.erro(erro))
  })
});

router.put('/atualizeEnviarLinksBotao', async(req, res) => {
  const dados = req.body;
  console.log(dados);

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(dados.id));

  empresa.enviarLinksBotao = dados.enviarLinksBotao;

  await new EmpresaService().atualizeEnviarLinksBotao(empresa, dados.enviarLinksBotao);

  res.json(Resposta.sucesso());
});

router.put('/cardapios' , async(req, res) => {
  const dados = req.body;
  console.log(dados)

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(dados.id));

  try{
    let cardapio: any = await new EmpresaService().salveCardapio(empresa, dados.cardapio);

    res.json(Resposta.sucesso( {id: cardapio.id }));
  }catch (e) {
    res.json(Resposta.erro(e.message ? e.message : e));
  }
})

router.put('/pedidos/aceitar/auto', async(req: any, res: any) => {
  let aceitarPedidoAutomatico = req.body.aceitarAutomatico;
  let empresa: any = req.empresa;

  await new EmpresaService().atualizeAceitePedidoAuto(empresa, aceitarPedidoAutomatico );

  res.json(Resposta.sucesso());
})



router.put('/atualizeLinksApps', async(req, res) => {
  res.json({
    sucesso: true
  });
});

router.put('/formasentrega', RotaGuard.alterarLoja,  async(req, res) => {
  const dados = req.body;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(dados.id));

  new EmpresaService().atualizeFormasEntrega(empresa, dados.formas).then( () => {
    res.json(Resposta.sucesso(dados.formas))
  }).catch( (erro: any) => {
    res.json( Resposta.erro(erro))
  });
});

router.post('/removaCidadeEntrega', async(req: any, res: any) => {
  const dados = req.body;
  const cidade = dados.cidade;
  const empresa: any = req.empresa;

  const dadosObjeto = {
    cidade: cidade,
    empresa: empresa
  };

  const mapeador = new MapeadorDeCidadeEntrega();

  const resposta = await mapeador.removaAsync(dadosObjeto);

  res.json(Resposta.sucesso('Removido'));
});

router.put('/permitirCidadeEntrega', async(req: any, res: any) => {
  const dados = req.body;
  const cidade = dados.cidade;
  const empresa: any = req.empresa;

  const mapeadorDeFormaDeEntrega = new MapeadorFormaDeEntregaEmpresa();

  const formaDeEntregaEmpresa = await mapeadorDeFormaDeEntrega.selecioneSync({idEmpresa: empresa.id,
    formaDeEntrega: FormaDeEntrega.ENTREGA});

  const dadosObjeto = {
    cidade: cidade,
    formaDeEntregaEmpresa: formaDeEntregaEmpresa,
    empresa: empresa
  };

  const mapeador = new MapeadorDeCidadeEntrega();

  mapeador.selecioneSync(dadosObjeto).then( async(cidadeExistente: any) => {
    if( cidadeExistente ) {
      return res.json(Resposta.erro('Cidade já está cadastrada'));
    }

    const resposta = await mapeador.insiraSync(dadosObjeto).catch( (erro: any) => {
      console.log('erro');
    });

    res.json(Resposta.sucesso(formaDeEntregaEmpresa));
  });
});

router.post('/salveZonaEntrega', async(req: any, res: any) => {
  const dados = req.body;
  const zona = dados.zona;
  const empresa: any = req.empresa;

  console.log(zona)

  const formaDeEntregaEmpresa = await new MapeadorFormaDeEntregaEmpresa().selecioneSync({idEmpresa: empresa.id,
    formaDeEntrega: FormaDeEntrega.ENTREGA});

  await new EmpresaService().salveZonaEntrega(empresa, formaDeEntregaEmpresa, zona);

  res.json(Resposta.sucesso(zona));
});

router.put('/zonaEntrega/ative', async(req: any, res: any) => {
  const dados = req.body;
  let zona: any = dados.zona;
  const empresa: any = req.empresa;

  console.log(zona)

  if(zona.id){
    zona.desativada = null;
    const formaDeEntregaEmpresa = await new MapeadorFormaDeEntregaEmpresa().selecioneSync({idEmpresa: empresa.id,
      formaDeEntrega: FormaDeEntrega.ENTREGA});

    await new EmpresaService().atualizeZonaEntregaAtiva(empresa, formaDeEntregaEmpresa, zona);
  }
  res.json(Resposta.sucesso({}));
})

router.put('/zonaEntrega/desative', async(req: any, res: any) => {
  const dados = req.body;
  let zona: any = dados.zona;
  const empresa: any = req.empresa;

  console.log(zona)

  if(zona.id){
    zona.desativada = true;
    const formaDeEntregaEmpresa = await new MapeadorFormaDeEntregaEmpresa().selecioneSync({idEmpresa: empresa.id,
      formaDeEntrega: FormaDeEntrega.ENTREGA});

    await new EmpresaService().atualizeZonaEntregaAtiva(empresa, formaDeEntregaEmpresa, zona);
  }
  res.json(Resposta.sucesso({}));
})

router.post('/removaZonaEntrega', async(req: any, res: any) => {
  const dados = req.body;
  const zona = dados.zona;
  const empresa: any = req.empresa;

  console.log(zona)

  if(zona.id){
    const formaDeEntregaEmpresa = await new MapeadorFormaDeEntregaEmpresa().selecioneSync({idEmpresa: empresa.id,
      formaDeEntrega: FormaDeEntrega.ENTREGA});

    await new EmpresaService().removaZonaEntrega(empresa, formaDeEntregaEmpresa, zona);
  }


  res.json(Resposta.sucesso({}));
})

router.post('/pausaProgramada', async(req: any, res: any) => {
    const dados = req.body;

    let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(dados.id));

    new EmpresaService().salvePausaProgramada(empresa, dados.pausaProgramada, req.user).then( (pausaProgramada: any) => {
      res.json(Resposta.sucesso({ id: pausaProgramada.id }));
    }).catch( (erro: any) => {
      res.json( Resposta.erro(erro))
    });
});

router.post('/pausaProgramada/reabra', async(req: any, res: any) => {
  const dados = req.body;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(dados.id));

  new EmpresaService().reabraDaPausa(empresa, dados.novaPausa, req.user).then( (novaPausa: any) => {

    res.json(Resposta.sucesso(novaPausa));
  }).catch( (erro: any) => {
    res.json( Resposta.erro(erro))
  });
});



router.delete('/pausaProgramada/:id', async(req: any, res: any) => {
  if(!req.query.eid)
    return res.json(Resposta.erro('Parametros inválidos'))

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(req.query.eid));

  let pausaProgramada = new PausaProgramada(null, null, null);

  pausaProgramada.id = Number(req.params.id);

  await pausaProgramada.cancelePorOperador(req.user, req.empresa);

  await new MapeadorDeEmpresa().removaDasCaches(empresa);

  res.json(Resposta.sucesso())
});


router.put('/camposextras', async(req, res) => {
  const dados = req.body;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(dados.id));

  new EmpresaService().atualizeCamposExtra(empresa, dados.campos).then( () => {
    res.json(Resposta.sucesso())
  }).catch( (erro: any) => {
    res.json( Resposta.erro(erro))
  })

});



router.put("/nomedestaques", async(req: any, res) => {
  const dados = req.body

  let empresa: Empresa = req.empresa

  empresa.nomeCategoriaDestaques = dados.nomeCategoriaDestaques
  empresa.imagemCategoriaDestaque = dados.imagemCategoriaDestaque

  let mapeador = new MapeadorDeEmpresa()
  mapeador.atualizeNomeCategoriaDestaques(empresa).then(async () => {
    await mapeador.removaDasCaches(empresa);
    res.json(Resposta.sucesso())
  }).catch((erro: any) => {
    res.json(Resposta.erro(erro))
  })
})

router.put('/scripts/atualizar', async(req, res) => {
  const dados = req.body;

  if(!dados.id)
    return res.json(Resposta.erro('É necessário informar o id da empresa'))

  let mapeador = new MapeadorDeEmpresa()

 mapeador.atualizeScriptsMarketing(dados).then((atualizou: any) => {
    res.json(Resposta.sucesso())
  }).catch((erro: any)  => {
    res.json(Resposta.erro(erro))
  });
})

router.put('/segmentos' , async(req, res) => {
  const dados = req.body;
  console.log(dados)

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(dados.id));


  new EmpresaService().atualizeSegmento(empresa, dados.segmento, dados.agruparCategoriasPizza).then( () => {
    res.json(Resposta.sucesso())
  }).catch( (erro: any) => {
    res.json( Resposta.erro(erro))
  })


})


router.put('/:id/fusohorario', async(req, res) => {
  const dados = req.body;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(dados.id));

  empresa.fusoHorario = dados.fh;

  new MapeadorDeEmpresa().atualizeFusoHorario(empresa).then( () => {
    res.json(Resposta.sucesso())
  }).catch( (erro: any) => {
    res.json( Resposta.erro(erro))
  })
})

router.put('/:id/pedidos/sempreReceber', RotaGuard.alterarLoja, async(req, res) => {
  const dados = req.body;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(dados.id));

  empresa.sempreReceberPedidos = dados.srp;

  new MapeadorDeEmpresa().atualizeSempreReceberPedidos(empresa).then( () => {
    res.json(Resposta.sucesso())
  }).catch( (erro: any) => {
    res.json( Resposta.erro(erro))
  })

});

router.put('/:id/pedidos/imprimirTXT', async(req, res) => {
  const dados = req.body;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(dados.id));

  empresa.imprimirTXT = dados.txt;

  new MapeadorDeEmpresa().atualizeImprimirTXT(empresa).then( () => {
    res.json(Resposta.sucesso())
  }).catch( (erro: any) => {
    res.json( Resposta.erro(erro))
  })

});

router.post('/:id/pedidos/impressao', RotaGuard.alterarLoja, async (req, res) => {
  const dados = req.body;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(req.params.id));

  let configImpressao = new ConfigImpressao( )

  Object.assign(configImpressao, dados);

  new MapeadorDeConfigImpressao().salveConfigImpressao(empresa, configImpressao).then((erro: any) => {
    if(!erro){
      configImpressao.impressoras = configImpressao.impressoras.filter((impressora: any) => !impressora.removida)
      res.json(Resposta.sucesso(configImpressao))
    } else {
      res.json(Resposta.erro(erro))
    }


  }).catch((erro) => {
    res.json(Resposta.erro(erro))
  })
});

router.put('/:id/categorias/posicoes', async(req, res) => {
  const categorias = req.body;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(req.params.id));

  for (let i = 0; i < categorias.length; i++)
      await new MapeadorDeCategoria(empresa.catalogo).atualizeSync(categorias[i])

  await new MapeadorDeEmpresa().removaDasCaches(empresa);

  const mapeadorDeProduto = new MapeadorDeProduto(empresa.catalogo);
  await mapeadorDeProduto.recalculeOrdens(empresa);

  await mapeadorDeProduto.removaCacheProdutos();

  res.json(Resposta.sucesso( ))
})

router.delete('/:id/categoria/:idCategoria', async(req: any, res) => {
  const dados: any = {id: Number(req.params.idCategoria)} ;

  if(!dados.id) return res.json(Resposta.erro("É necessário informar o id da categoria que será removida."));

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(req.params.id));

  let existente = _.find(empresa.categorias, (cat: Categoria) =>  cat.id === dados.id);

  if(!existente)
    return res.json(Resposta.erro('Não foi localizada uma categoria com o id: ' + dados.id))

  dados.empresa = empresa

  new MapeadorDeCategoria(empresa.catalogo).removaCategoria(dados).then(async (resultado: any) => {
    new MapeadorDeEmpresa().removaDasCaches(empresa)
    await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();

    res.json(Resposta.sucesso(dados))
  })


})

router.post('/:id/categoria', async(req, res) => {
  const dados = req.body;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(Number(req.params.id));
  let existente = _.find(empresa.categorias, (cat: Categoria) =>  cat.nome.toLowerCase() === dados.nome.toLowerCase() );

  if(existente && existente.id !== dados.id)
    return res.json(Resposta.erro('Já existe um categoria com esse nome: ' + existente.nome))

  let categoria;

  if(!dados.id){
    categoria = new Categoria(dados.id, dados.nome.trim(), empresa);
    categoria.posicao = empresa.categorias.length + 1;


  } else {
    categoria = empresa.categorias.find( (cat: any) => cat.id === dados.id);
    categoria.nome = dados.nome;
  }

  categoria.impressoras = dados.impressoras;

  await new EmpresaService().salveCategoria(empresa.catalogo, categoria, empresa);


  res.json(Resposta.sucesso(categoria))

})

router.put('/mesas/configuracoes', RotaGuard.alterarLoja, async(req, res) => {
  const request: any = req;
  const empresa: any = request.empresa;

  const dados = req.body;

  dados.id = empresa.id;

  if(!dados.usarCartaoCliente || !dados.permitirMultiplasComandasMesa)
    dados.associarCartaoFechamentoPedido = false;

  return  new MapeadorDeEmpresa().atualizeConfiguracoesMesas(dados).then( () => {
    res.json(Resposta.sucesso())
  })


})


router.put('/atualizeConfiguracoesClassificacao', async(req, res) => {
  const request: any = req;
  const empresa: any = request.empresa;

  const dados = req.body;

  dados.id = empresa.id;

  new EmpresaService().atualizeConfiguracoesClassificacao(dados).then( () => {
    res.json(Resposta.sucesso())
  })
})

router.put('/atualizeBotAtivo', async(req, res) => {
  const dados = req.body;

  new EmpresaService().atualizeBotAtivo(dados).then( () => {
    res.json(Resposta.sucesso())
  })
});

router.put('/meioenvio', async(req, res) => {
  const dados = req.body;

  new EmpresaService().atualizeMeioEnvio(dados).then( () => {
    res.json(Resposta.sucesso())
  })
})

router.put('/atualizeLocalizacao', ehAdmin, async(req, res) => {
  const dados = req.body;
  let empresa: Empresa = await new MapeadorDeEmpresa().selecioneSync(dados.id);

  empresa.latitudeLongitude = dados.latitude + "," + dados.longitude;

  new EmpresaService().atualizeLocalizacao(empresa).then( () => {
      res.json(Resposta.sucesso( 'Localização alterada com sucesso'));
  });
});


router.put('/bloqueada', ehAdmin, async(req, res) => {
  const dados = req.body;
  let empresa: Empresa = await new MapeadorDeEmpresa().selecioneSync(dados.id);

  new EmpresaService().atualizeBloqueio(empresa, dados.dataBloqueioAuto).then( () => {
    res.json(Resposta.sucesso( { bloqueada: empresa.bloqueada}))
  })
})


router.put('/:id/desativacao/cancele', ehAdmin, async (req, res) => {
  let operador: any = req.user;
  let dados =  req.body;

  if(!dados.id || !dados.empresa.id)
    return    res.json(Resposta.erro('Dados inválidos'));

  await new MapeadorDeEmpresa().atualizeRemovido(dados.empresa);
  await new MapeadorDeCancelamentoEmpresa().removaAsync({id: dados.id, operador: operador.id});

  res.json(Resposta.sucesso());

})

router.get('/motivos/cancelamentos', async (req: any, res: any) => {
  let lista = await new MapeadorMotivoCancelamentoEmpresa().listeAsync({});

  res.json(Resposta.sucesso(lista))
})

router.put('/:id/desative', ehAdmin, async (req, res) => {
  let operador: any = req.user;
  let justificava = req.body.justificava;
  let motivos = req.body.motivos;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(req.params.id );

  if(empresa){
      let erro: string = await new EmpresaService().canceleEmpresa(empresa, motivos, justificava, operador);

      if(!erro){
        res.json(Resposta.sucesso())
      } else {
        res.json(Resposta.erro(erro))
      }
  } else {
    res.json(Resposta.erro('Nenhuma empresa encontrada'))
  }
})

router.get('/bloqueadas/remova', async (req: any, res: any) => {
  let empresas = await new EmpresaService().removaEmpresasBloquedasPorTempo();

  res.json(empresas.map((item: any) => {  return  { id: item.id, nome: item.nome, dataBloqueio: item.dataBloqueioAuto } }))
})

router.put( '/:id/usuario',  ehAdmin, async (req, res) => {
  let dados: any = req.body;

  const mapeador = new MapeadorDeUsuario();

  mapeador.desativeMultiCliente();

  dados.empresa = { id: Number( req.params.id) }

  if(dados.id)
    await new MapeadorDeUsuario().atualizeSync(dados);
  else
    await new MapeadorDeUsuario().insiraSync(Object.assign(new Usuario(), dados));

  res.json(Resposta.sucesso())
});

router.put( '/:id/garcom',  async (req, res) => {
  let dados: any = req.body;

  const mapeador = new MapeadorDeUsuario();

  mapeador.desativeMultiCliente();

  dados.empresa = { id: Number( req.params.id)};
  if(dados.operadorPdv) dados.codigoPdv = dados.operadorPdv.cod_fun;

  if(dados.id) {
    await new MapeadorDeUsuario().atualizeSync(dados);
    res.json(Resposta.sucesso())

  } else {
    let usuarioService: UsuarioService = new UsuarioService()

    let usuario = usuarioService.crieUsuario(dados);
    usuario.garcom = true
    usuarioService.verifiqueExistencia(usuario.email).then(jaExiste => {
      if(jaExiste) return res.json( Resposta.erro( "Já existe um usuário com esse e-mail"))

      usuarioService.insiraUsuario(usuario).then( () => {
         res.json(Resposta.sucesso({id: usuario.id}))
      })
    })
  }
})

router.get('/:id/usuarios', (req, res) => {
  console.log(`[${new Date().toISOString()}] Buscando usuários para empresa ID: ${req.params.id}`);
  const mapeador = new MapeadorDeUsuario();
  mapeador.desativeMultiCliente();
  console.log('[Mapeador] Multi-cliente desativado');

  const empresaId = Number(req.params.id);
  const filtro: any = { idEmpresa: empresaId, daEmpresa: true, listeSessoes: true };

  if (empresaId === 1 || empresaId === 1322) {
    delete filtro.daEmpresa;
    console.log(`[Filtro] Empresa ID ${empresaId} - Listando tbm usuarios admin`);
  }

  new MapeadorDeUsuario().listeAsync(filtro).then(usuarios => {
    console.log(`[Usuários] Encontrados ${usuarios.length} usuários para a empresa ${empresaId}`);
    usuarios.forEach((usuario: any) => {
      delete usuario.senha;
      console.log(`[Usuário] Processado usuário: ${usuario.id} - ${usuario.nome || 'Sem nome'}`);
    });
    res.json(Resposta.sucesso(usuarios));
    console.log('[Resposta] Enviada com sucesso');
  }).catch(erro => {
    console.error(`[Erro] Falha ao buscar usuários: ${erro.message}`);
    res.status(500).json(Resposta.erro('Erro ao buscar usuários' || erro));
  });
})

router.get('/:id/garcons', (req, res) => {
  const mapeador = new MapeadorDeUsuario();
  mapeador.desativeMultiCliente();

  new MapeadorDeUsuario().listeAsync({ idEmpresa: Number(req.params.id), daEmpresa: true, apenasGarcons: true } ).then(  usuarios => {
    usuarios.forEach( (usuario: any ) => { delete  usuario.senha});
    res.json(Resposta.sucesso(usuarios))
  })
})

router.put('/:id/usuario/:usuarioId/remova', async (req, res) => {
  try {
    const usuarioId = req.params.usuarioId;
    const empresaId = Number(req.params.id);
    const usuarioLogado: any = req.user; // Usuário que está fazendo a requisição

    const mapeador = new MapeadorDeUsuario();
    mapeador.desativeMultiCliente();

    const usuario = await mapeador.selecioneSync({ id: usuarioId, idEmpresa: empresaId });

    if (!usuario) {
      return res.json(Resposta.erro('Usuário não encontrado'));
    }

    // Verifica se o usuário é superadmin
    if (usuario.superadmin) {
      return res.json(Resposta.erro('Não é possível remover um usuário superadmin'));
    }

    // Verifica se o usuário a ser removido é admin e se o usuário logado não é superadmin
    if (usuario.admin && (!usuarioLogado || !usuarioLogado.superadmin)) {
      return res.json(Resposta.erro('Apenas usuários superadmin podem remover usuários admin'));
    }

    await new UsuarioService().marqueComoRemovido(usuario);

    res.json(Resposta.sucesso({ mensagem: 'Usuário removido com sucesso' }));
  } catch (error) {
    console.error('Erro ao remover usuário:', error);
    res.json(Resposta.erro('Erro ao remover usuário'));
  }
})

router.get('/:id/papeis', async (req, res) => {

  let papeis = await new MapeadorDePapel().listeAsync({ idEmpresa: Number(req.params.id) });

  res.json(Resposta.sucesso(papeis))
})


router.post('/:id/papeis', async (req: any, res: any) => {
  let dados = req.body;

  let papel =  new Papel(dados.nome);

  if(!papel.nome)
    return res.json(Resposta.erro('Informe um nome ' ))

  let operacoes = await new MapeadorDePapel().listeOperacoes();

  papel.setOperacoesAtivas(operacoes);

  let papeis = await new MapeadorDePapel().listeAsync({ idEmpresa: Number(req.params.id) });
  let existente = papeis.find((item: any) => item.nome.toUpperCase().trim() === papel.nome.toUpperCase().trim() );

  if(existente)
    return res.json(Resposta.erro('Já existe um papel com esse nome: ' + papel.nome))

  papel.empresa = {id:  Number(req.params.id)};

  await papel.salve(true)

  res.json(Resposta.sucesso(papel))
})

router.get('/:id/papeis-operacoes', async (req, res) => {

  let papeis = await new MapeadorDePapel().listeAsync({ idEmpresa: Number(req.params.id) });

  let operacoes = await new MapeadorDePapel().listeOperacoes();

  papeis.forEach((papel: Papel) => {
    papel.setOperacoesAtivas(operacoes)
  })

  res.json(Resposta.sucesso(papeis))
})

router.post('/:id/papeis/:papel/operacoes', async (req: any, res: any) => {
  let dados = req.body;

  let papel =
    await new MapeadorDePapel().selecioneSync({ idEmpresa: Number(req.params.id), id:  req.params.papel });

  let operacaoExistente = papel.operacoes.find((item: any) => item.id === dados.id);

  if(dados.ativa){
    if(operacaoExistente) return res.json(Resposta.erro('Operação já está ativada no papel: ' + operacaoExistente.nome))

    await new MapeadorDePapel().insiraOperacao(papel, dados);
  } else {
    if(!operacaoExistente) return res.json(Resposta.erro('Operação não está ativada no papel: ' + operacaoExistente.nome))

    await new MapeadorDePapel().removaOperacao(papel, operacaoExistente);
  }

  res.json(Resposta.sucesso());
})

router.post('/:id/usuarios/:usuario/papeis', async (req: any, res: any) => {
  let papel = req.body;

  let usuario =
    await new MapeadorDeUsuario().selecioneSync({ idEmpresa: Number(req.params.id), id:  req.params.usuario });

  let papelExistente: any = usuario.papeis.find((item: any) => item.id === papel.id);


  if(papel.ativo){
    if(papelExistente) return res.json(Resposta.erro('Papel já está ativado: ' + papelExistente.nome))

    await new MapeadorDePapel().insiraUsuario(usuario, papel);

  } else {
    if(!papelExistente) return res.json(Resposta.erro('Papel não está ativado: ' + papel.nome))

    await new MapeadorDePapel().removaUsuario(usuario, papel);

  }

  res.json(Resposta.sucesso());
})

router.get('/pets/tipos', async (req, res) => {
  let tipos = await new MapeadorDePet().listeTipos()

  res.json(Resposta.sucesso(tipos))
});

router.delete('/:id/horario/:idHorario', (req, res) => {
  const idEmpresa = req.params.id;
  new MapeadorDeEmpresa().selecioneSync(idEmpresa).then( (empresa) => {
    let empresaService =  new EmpresaService();
    empresaService.removaHorarioFuncionamento(empresa,  Number.parseInt(req.params.idHorario, 10)).then( (erro) => {
      if(!erro)
        res.json(Resposta.sucesso("O horário foi removido"))
      else
        res.json(Resposta.erro(erro))
    })
  })

})

router.put('/:id/horario', (req, res) => {
  let dados = req.body;
  const idEmpresa = req.params.id;

  new MapeadorDeEmpresa().selecioneSync(idEmpresa).then( (empresa) => {
     let empresaService =  new EmpresaService();

    if(!dados.horarioAbertura)
      return res.json(Resposta.erro("É necessário informar o horário de abertura"))

    if(!dados.horarioFechamento)
      return res.json(Resposta.erro("É necessário informar o horário de fechamento"))

     if(dados.id){
       empresaService.atualizeHorarioFuncionamento(empresa, dados ).then( (erro) => {
         if(!erro)
           res.json(Resposta.sucesso({}))
         else
           res.json(Resposta.erro(erro))
       })
     } else {
       let horarioDeFuncionamento: any =  new HorarioFuncionamento(dados.servico , dados.diaDaSemana, dados.horarioAbertura,
                                                                   dados.horarioFechamento, dados.funciona);
       empresaService.insiraHorarioFuncionamento(empresa, horarioDeFuncionamento).then( (erro) => {
         if(!erro)
           res.json(Resposta.sucesso({id: horarioDeFuncionamento.id}))
         else
           res.json(Resposta.erro(erro))
       })
     }
  })
});

router.post('/contato', (req, res) => {
  const dados = req.body;

  let contatoEmpresa = new ContatoEmpresa(dados.nome, dados.empresa, dados.email, dados.telefone, dados.instagram,
    dados.qtdePedidos, dados.ticketMedio);

  new EmpresaService().insiraSolicitacaoContato(contatoEmpresa).then( ( erro => {
    if(!erro){
      res.json(Resposta.sucesso())
    }   else {
      res.json(Resposta.erro(erro))
    }
  }))
});

  router.post('/:id/importe/contatos', async (req, res) => {
  let contatos = req.body;
  let planos: any = await new MapeadorDePlano().listeAsync({idEmpresa: req.params.id , ativo: true})

  new MapeadorDeEmpresa().selecioneSync({id: req.params.id}).then((empresa: Empresa) => {
    if( planos.length > 1)
      return res.json(Resposta.erro('Empresa possui mais de um plano, importação não permitida.'));

    let importarPontos = contatos.find((item: any) => item.pontos != null) != null

    if(importarPontos)
      if(planos.length === 0)
        return res.json(Resposta.erro('Nenhum plano ativo para importação de pontos'));

    new ContatoService().importeContatos(empresa, planos[0], contatos).then( resposta => {
      res.json(Resposta.sucesso(resposta));
    }).catch( (erro) => {
      res.json(Resposta.erro(erro || 'Falha ao importar, tente de novo mais tarde.'))
    })

  })

})

router.post('/:id/importe/pets', async (req, res) => {
  let pets = req.body;

  let planos: any = await new MapeadorDePlano().listeAsync({idEmpresa: req.params.id})
  new MapeadorDeEmpresa().selecioneSync({id: req.params.id}).then((empresa: Empresa) => {
    if( planos.length > 1)
      return res.json(Resposta.erro('Empresa possui mais de um plano, importação não permitida.'))

    if( planos.length === 0)
      return res.json(Resposta.erro('Empresa não possui nenhum plano!'))

    new ContatoService().importePets(empresa, planos[0], pets).then( resposta => {
      res.json(Resposta.sucesso(resposta));
    }).catch( (erro) => {
      res.json(Resposta.erro(erro || 'Falha ao importar, tente de novo mais tarde.'))
    });

  })


});

router.get('/:id/historico', async (req, res) => {
  let idEmpresa = req.params.id
  let filtroTexto =  req.query.texto ? '%' + req.query.texto + '%' : null ;
  let filtroOperacao = req.query.operacao ?  req.query.operacao : null;
  let filtroTipoObjeto = req.query.tipoObjeto ? req.query.tipoObjeto : null;

  let dados: any = {
    idEmpresa: idEmpresa,
    texto: filtroTexto,
    operacao: filtroOperacao,
    tipoObjeto: filtroTipoObjeto

  }

  if(req.query.i) {
    dados.inicio = Number(req.query.i)
    dados.total = Number(req.query.t)
  }

  let mapeador = new MapeadorDeRegistroDeOperacao()
  mapeador.desativeMultiCliente()
  let historico = await mapeador.listeAsync(dados)
  let quantidade = await mapeador.obtenhaQuantidade(dados)

  res.json(Resposta.sucesso({
    registros: historico,
    quantidade: quantidade
  }))
})


router.post('/:id/integracao/desative', ehAdmin, async (req, res) => {
  let empresa = await new MapeadorDeEmpresa().selecioneSync(req.params.id);

  console.log('desativar integração empresa ' + empresa.nome);
  console.log(req.user);

  new EmpresaService().desativeIntegracaoPedidoFidelidade(empresa ).then( (integracao: any) => {
    res.json(Resposta.sucesso(integracao));
  }).catch( (erro) => {
    res.json(Resposta.erro(erro || 'Falha ao ativar integração, tente de novo mais tarde.'))
  });
});


router.post('/:id/integracao/ative', ehAdmin, async (req, res) => {
  let dados: any = req.body;
  let empresa = await new MapeadorDeEmpresa().selecioneSync(req.params.id);
  let plano = await new MapeadorDePlano().selecioneSync({ id: req.body.id, idEmpresa: empresa.id });

  new EmpresaService().ativeIntegracaoPedidoFidelidade(empresa, plano, dados.cashback,
    dados.selosPorAtividade, dados.pontuarSoLoja, dados.pontuarMesas).
    then( (integracao: any) => {
    res.json(Resposta.sucesso(integracao));
  }).catch( (erro) => {
    res.json(Resposta.erro(erro || 'Falha ao ativar integração, tente de novo mais tarde.'))
  });
});


router.post('/:id/fidelidadegcom/desative', ehAdmin, async (req, res) => {
  let empresa = await new MapeadorDeEmpresa().selecioneSync(req.params.id);

  console.log('desativar integração empresa ' + empresa.nome);
  console.log(req.user);

  new EmpresaService().desativeIntegracaoFidelidadeExterna(empresa ).then( (integracao: any) => {
    res.json(Resposta.sucesso(integracao));
  }).catch( (erro) => {
    res.json(Resposta.erro(erro || 'Falha ao ativar integração, tente de novo mais tarde.'))
  });

})


router.post('/:id/fidelidadegcom/ative', ehAdmin, async (req, res) => {
  let dados: any = req.body;
  let empresa = await new MapeadorDeEmpresa().selecioneSync(req.params.id);

  new EmpresaService().ativeIntegracaoFidelidadeExterna(empresa, req.user, 'gcom', dados.loja).
  then( (integracao: any) => {
    res.json(Resposta.sucesso(integracao));
  }).catch( (erro) => {
    res.json(Resposta.erro(erro || 'Falha ao ativar integração, tente de novo mais tarde.'))
  });
});

router.post('/:id/integracao/pontuarSoLoja', logado, async (req: any,  res: any) => {
  let pontuarSoLoja = req.body.pontuarSoLoja;
  let empresa = await new MapeadorDeEmpresa().selecioneSync(req.params.id);

  await new EmpresaService().atualizePontuarSoLoja(empresa, pontuarSoLoja, req.user);
  res.json(Resposta.sucesso( ));

});

router.post('/:id/integracao/pontuarMesas', logado, async (req: any, res: any) => {
  let pontuarMesas = req.body.pontuarMesas;
  let empresa: any = await new MapeadorDeEmpresa().selecioneSync(req.params.id);

  await new EmpresaService().atualizePontuarMesas(empresa, pontuarMesas, req.user);
  res.json(Resposta.sucesso( ));
});


router.post('/:id/integracao/resgarBrindes', logado, async (req: any, res: any) => {
  let resgatarBrindes = req.body.resgatar;

  let empresa: any = await new MapeadorDeEmpresa().selecioneSync(req.params.id);

  await new EmpresaService().atualizeResgatarBrinde(empresa, resgatarBrindes, req.user);
  res.json(Resposta.sucesso( ));
});

router.post('/:id/integracao/ocultarPontos', logado, async (req: any, res: any) => {
  let ocultarPontos = req.body.ocultarPontos;

  let empresa: any = await new MapeadorDeEmpresa().selecioneSync(req.params.id);

  await new EmpresaService().atualizeOcultarPontos(empresa, ocultarPontos, req.user);
  res.json(Resposta.sucesso( ));
});

router.post('/:id/atividade/valor', logado, async (req: any,  res: any) => {
  let empresa = await new MapeadorDeEmpresa().selecioneSync(req.params.id);
  let pontosGanhos = req.body.pontosGanhos;
  let cashback = req.body.cashback;
  let idAtividade = req.body.id;
  let mapeadorAtividade = new MapeadorDeAtividade();

  if(!idAtividade)
    return res.json(Resposta.erro('Parametros inválidos'));

  //esta alterando do superadmin
  if(req.user.admin && req.empresa.id !== empresa.id)
    mapeadorAtividade.desativeMultiCliente();

  let atividade = await mapeadorAtividade.selecioneSync({id: idAtividade, idEmpresa: empresa.id});

  if(atividade){
    console.log('atualizar valor atividade')
    console.log(atividade)

    if(cashback != null){
      if(cashback  <= 0  || cashback >= 100)
        return     res.json(Resposta.erro( 'Valor cashback invalido: ' + cashback));

      atividade.cashback = cashback / 100;
    } else if(pontosGanhos != null) {
      if (!pontosGanhos)
        return res.json(Resposta.erro('Valor invalido: ' + pontosGanhos));
      atividade.pontosGanhos = pontosGanhos
    }

    await new EmpresaService().atualizeValorAtividade(empresa, atividade);
    res.json(Resposta.sucesso( ));
  } else {
    res.json(Resposta.erro( 'Avidade não encontrada: ' + idAtividade));
  }
});




router.post('/:id/integracao/delivery', async (req: any,  res: any) => {
  let empresa = req.empresa;
  let dados = req.body;
  dados.empresa = empresa;

  if(!dados.configuracoesEspecificas)
    dados.configuracoesEspecificas = {}

  dados.configuracoesEspecificas.empresa = {id: empresa.id, dominio: empresa.dominio, cnpj: empresa.cnpj}

  try {
    let integracao: IntegracaoDelivery   =  FactoryIntegracaoDelivery.crie(dados);

    if(integracao.tempoEstimadoEntregaObrigatorio()){
      let erroConfiguracao;
      empresa.formasDeEntrega.forEach((formaDeEntrega: any) => {
        try{
          formaDeEntrega.valideTempoMiminimoConfigurado()
        } catch (error){
          erroConfiguracao =  error.message || error;
        }
      })

      if(erroConfiguracao)
        return res.json(Resposta.erro(erroConfiguracao ))
    }

    if(!integracao.token)
      await integracao.inicializeToken()
    else
      await integracao.obtenhaService().valideToken( );

    if(!integracao.id){
      await new MapeadorDeIntegracaoDelivery().insiraSync(integracao);
      if(integracao.cepObrigatorio()){
        for(let i = 0; i < empresa.formasDeEntrega.length; i++){
          let formaDeEntrega = empresa.formasDeEntrega[i];

          if(formaDeEntrega.ehParaDelivery() && !formaDeEntrega.cepObrigatorio) {
            formaDeEntrega.cepObrigatorio = true;
            await new MapeadorFormaDeEntregaEmpresa().atualizeCepObigatorio(formaDeEntrega);
          }
        }
      }

    } else {
      await new MapeadorDeIntegracaoDelivery().atualizeSync(integracao);
    }

    await new MapeadorDeEmpresa().removaDasCaches(empresa)

    delete integracao.empresa;

    res.json(Resposta.sucesso( integracao));
  } catch (erro){
    res.json(Resposta.erro( erro));
  }
});


router.post('/:id/integracao/fooddelivery/remova', async (req: any,  res: any) => {
  let empresa: any = req.empresa;

  if(empresa.integracaoFoodyDelivery) {
    await new MapeadorDeIntegracaoDelivery().removaAsync(empresa.integracaoFoodyDelivery);
    await new MapeadorDeEmpresa().removaDasCaches(empresa)

    res.json(Resposta.sucesso( ));
  }else {
    res.json(Resposta.erro( 'Não há nenhuma integraçao ativa.'));
  }
});


router.post('/:id/integracao/delivery/remova', async (req: any,  res: any) => {
  let empresa = req.empresa;

  if(empresa.integracaoDelivery  ){

    await new MapeadorDeIntegracaoDelivery().removaAsync(empresa.integracaoDelivery);
    await new MapeadorDeEmpresa().removaDasCaches(empresa)

    res.json(Resposta.sucesso( ));

  } else {
    res.json(Resposta.erro( 'Não há nenhuma integraçao ativa.'));
  }
});

router.post('/fechar', async(req: any, res: any) => {
  let tempo = req.body.tempo;
  let mensagem = req.body.mensagem;

  let empresa: any = req.empresa;

  new EmpresaService().fecharLoja(empresa, tempo, mensagem).then( (resposta) => {
    res.json(Resposta.sucesso('Sucesso'));
  });
});

router.post('/abra', async(req: any, res: any) => {
  let tempo = req.body.tempo;
  let empresa: any = req.empresa;

  new EmpresaService().abra(empresa, tempo).then( (resposta) => {
    res.json(Resposta.sucesso('Sucesso'));
  });
});


router.get('/me/templates', async (req: any,  res: any) => {
  let templatesProdutos  =  await new MapeadorDeProdutoTemplate().listeAsync({idCatalogo: req.empresa.catalogo.id, ativo: true})

  res.json(Resposta.sucesso(templatesProdutos))
})

router.get('/:id/templates', async (req,  res: any) => {
  let templatesProdutos  =  await new MapeadorDeProdutoTemplate().listeAsync({idCatalogo: Number(req.params.id), ativo: true})

  res.json(Resposta.sucesso(templatesProdutos))
})


router.get('/segmentos/liste', async (req,  res: any) => {

  let segmentos = await new MapeadorDeSegmento().listeAsync({});


  res.json(Resposta.sucesso(segmentos))

})



router.post('/:id/templates', async (req, res) => {
  let dados  = req.body;
  let dadosEmpresa = { id: Number(req.params.id)};

  let mapeador = new MapeadorDeEmpresa()
  mapeador.selecioneSync(dadosEmpresa).then((empresa: Empresa) => {

      let produtoTemplate = ProdutoTemplate.novoTemplateDePizzaDoModelo(empresa.catalogo, dados.modelo)

      produtoTemplate.nome = dados.nome;
      produtoTemplate.ativo = dados.ativo;

    mapeador.transacao(async  (conexao: any, commit: Function) => {

        await new EmpresaService().salveTemplateProduto(produtoTemplate);
        await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();

        commit( () => {
          res.json(Resposta.sucesso(produtoTemplate))
        })

      })

    })
})

router.post('/templates/:id/tamanho', async (req: any, res) => {
  let templateTamanho = req.body;
  let mapeador = new MapeadorDeProdutoTemplate();
  mapeador.desativeMultiCliente();

  if(!templateTamanho.id){

     templateTamanho.template = {id:  Number(req.params.id)};

     await mapeador.salveTamanho(templateTamanho);

     await new MapeadorDeProduto(req.empresa.catalogo).removaCacheProdutos();
    res.json(Resposta.sucesso({id: templateTamanho.id}));
  } else {
    await mapeador.atualizeTamanho(templateTamanho);

    await new MapeadorDeProduto(req.empresa.catalogo).removaCacheProdutos();
    res.json(Resposta.sucesso({ }));
  }

})


router.post('/templates/:template/adicional/:adicional', async (req: any, res) => {
  let templateOpcao = req.body;
  let mapeador = new MapeadorDeProdutoTemplate();
  mapeador.desativeMultiCliente();

  let idTemplate = Number(req.params.template);
  let idAdicional = Number(req.params.adicional);

  if(!templateOpcao.id){
    let produtosPizzas = await  new MapeadorDeProduto(req.empresa.catalogo).listeAsync(
      { tipo: 'pizza', idTemplate: idTemplate});

    mapeador.transacao(async (conexao: any, commit: any) => {
      await mapeador.salveOpcao({id:  idAdicional}, templateOpcao);

      let novasOpcoesSabores: any = []

      produtosPizzas.forEach((produto: any) => {
        const adicionalTemplate = produto.camposAdicionais.find(
          (adicional: any) => adicional.template && adicional.template.id === idAdicional);

        if(adicionalTemplate){
          let opcaoExistente =  adicionalTemplate.opcoesDisponiveis.find((opcao: any) =>
            opcao.template && opcao.template.id === templateOpcao.id)

          if(!opcaoExistente){
            let opcaoAdicional = new OpcaoDeAdicionalDeProduto(templateOpcao.nome,
              templateOpcao.valor, templateOpcao.disponivel, templateOpcao.descricao, templateOpcao);

            opcaoAdicional.codigoPdv = templateOpcao.codigoPdv;
            opcaoAdicional.adicional = adicionalTemplate;

            novasOpcoesSabores.push(opcaoAdicional)
          }
        }


      })

      if(novasOpcoesSabores.length){
        for(let i = 0; i < novasOpcoesSabores.length; i++){
          let novaOpcao: any = novasOpcoesSabores[i];
          await new MapeadorDeOpcaoDeAdicionalDeProduto().insiraOpcao(novaOpcao.adicional, novaOpcao);
        }
      }

      commit(async () => {
        await new MapeadorDeProduto(req.empresa.catalogo).removaCacheProdutos();
        res.json(Resposta.sucesso({id: templateOpcao.id}));
      })
    })
  } else {
    await mapeador.atualizeOpcao(templateOpcao);
    const produtoTemplate = await new MapeadorDeProdutoTemplateAdicional().selecioneSync({id: idAdicional});

    await new MapeadorDeProdutoTemplateAdicional().atualizeSync(produtoTemplate);

    await new MapeadorDeProduto(req.empresa.catalogo).removaCacheProdutos();
    res.json(Resposta.sucesso({ }));
  }
})

router.put('/templates/:id', async (req: any, res) => {
  let produtoTemplate = req.body;
  let mapeador = new MapeadorDeProdutoTemplate();
  mapeador.desativeMultiCliente();

  await mapeador.atualizeSync(produtoTemplate);
  await new MapeadorDeProduto(req.empresa.catalogo).removaCacheProdutos();

  res.json(Resposta.sucesso({ }));
})

router.delete('/templates/:id', async (req: any, res: any) => {
    let template =  await new MapeadorDeProdutoTemplate().selecioneSync(Number(req.params.id));

    let temProduto = await
      new MapeadorDeProdutoTemplate().existeSync({id: template.id, catalogo: req.empresa.catalogo});

    if(!temProduto){
      await new MapeadorDeProdutoTemplate().removaAsync(template);
      res.json(Resposta.sucesso({ }));
    } else {
      res.json(Resposta.erro('Template é usado por algum produto, remova o produto primeiro.'));
    }
})


router.put('/templates/:id/identificador', async (req: any, res) => {
  let produtoTemplate = req.body;
  let mapeador = new MapeadorDeProdutoTemplate();
  mapeador.desativeMultiCliente();


  await mapeador.atualizeIdentificador(produtoTemplate);
  await new MapeadorDeProduto(req.empresa.catalogo).removaCacheProdutos();

  res.json(Resposta.sucesso({ }));
})

router.put('/templates/:id/tipodecobranca', async (req: any, res) => {

  let produtoTemplate = req.body;
  let mapeador = new MapeadorDeProdutoTemplate();
  mapeador.desativeMultiCliente();


  await mapeador.atualizeTipoDeCobranca(produtoTemplate);
  await new MapeadorDeProduto(req.empresa.catalogo).removaCacheProdutos();

  res.json(Resposta.sucesso({ }));

})


router.put('/templates/:id/pontuacao', async (req: any, res: any) => {
  let dados: any = req.body;


  let template = await  new MapeadorDeProdutoTemplate().selecioneSync(Number(req.params.id));

  for(let i = 0; i < dados.tamanhos.length; i++){
    let dadosTamanho: any = dados.tamanhos[i];

    let tamanho: ProdutoTemplateTamanho = template.tamanhos.find((item: any) => item.id === dadosTamanho.id)

    if(tamanho){
      tamanho.pontosGanhos = dadosTamanho.pontosGanhos
      tamanho.cashback = dadosTamanho.cashback

      await new MapeadorDeProdutoTemplate().atualizePontosFidelidade(tamanho)
    }
  }

  await new MapeadorDeEmpresa().removaDasCaches(req.empresa)
  await new MapeadorDeProduto(req.empresa.catalogo).removaCacheProdutos();

  res.json(Resposta.sucesso());
})


router.put('/templates/:id/dePara/remova', async (req: any, res: any) => {
  let dados: any = req.body;

  let template = await  new MapeadorDeProdutoTemplate().selecioneSync(Number(req.params.id));
  let templateTamanho = template.tamanhos.find((t: any) => t.id === dados.idTamanho);

  if(templateTamanho){
    let dePara: PizzaTamanhoSaboresDePara = templateTamanho.deParaTamanhoSabores.find((item: any) => item.id === dados.id);
    await dePara.remova();
    res.json(Resposta.sucesso());
  } else {
    console.log(dados)
    res.json(Resposta.erro(String(`Dados invalidos`)))
  }

})

router.put('/templates/:id/dePara', async (req: any, res: any) => {
  let dados: any = req.body;

  let template = await  new MapeadorDeProdutoTemplate().selecioneSync(Number(req.params.id));
  let templateTamanho = template.tamanhos.find((t: any) => t.id === dados.idTamanho);
  let dePara: any = new PizzaTamanhoSaboresDePara();

  dePara.id = dados.id;
  dePara.codigoPdv = dados.codigoPdv;
  dePara.qtdeSabores = dados.qtdeSabores.id;
  dePara.templateTamanho = templateTamanho;
  dePara.nome = templateTamanho.descricao;

  if(!templateTamanho.deParaTamanhoSabores.find((item: any) => item.qtdeSabores === dePara.qtdeSabores)) {
    await dePara.salve( );
    res.json(Resposta.sucesso(dePara));
  } else {
    res.json(Resposta.erro(String(`Já existe um código de pdv cadastrado para o tamanho ${templateTamanho.descricao} e ${dePara.qtdeSabores} sabor(es)`)))
  }
})

router.get('/link/:id', ehAdmin, async(req: any, res) => {
  let hostname = req.query.hostname
  let catalogo: boolean = req.query.catalogo != null
  let cardapio: boolean = req.query.cardapio != null
  let idEmpresa = Number (req.params.id)

  if(!hostname)
    return res.json(Resposta.erro('É necessário informar o novo hostname da empresa'))



  let mapeador = new MapeadorDeEmpresa()

  mapeador.selecioneCachePoId(idEmpresa).then((empresa: any) => {
    if(!empresa) return res.json(Resposta.erro('Não foi encontrada empresa com o id ' + idEmpresa))
    let novoDominio = new DominioDaEmpresa(hostname, cardapio, catalogo, empresa)
    mapeador.atualizeLinkDaEmpresa(empresa, novoDominio).then(() => {
      res.json(Resposta.sucesso('O link ' + hostname + ' foi associado à empresa '  + empresa.nome))
    })
  })
})

router.get('/link/:id/desvincule', ehAdmin, async(req, res) => {
  let idEmpresa = Number (req.params.id)

  let mapeador = new MapeadorDeEmpresa()



  mapeador.selecioneCachePoId(idEmpresa).then((empresa: any) => {
    if(!empresa) return res.json(Resposta.erro('Não foi encontrada empresa com o id ' + idEmpresa))
    mapeador.removaLinkDaEmpresa(empresa).then(() => {
      res.json(Resposta.sucesso('O link foi desvinculado da empresa '  + empresa.nome))
    })
  })
});

router.get('/categorias/ordens/recalcule',  ehAdmin, async (req: any, res) => {
  let empresa =  req.empresa;

  if(empresa){
    await new MapeadorDeCategoria(empresa.catalogo).recalculeOrdens(empresa);

    res.json(Resposta.sucesso('ordens recalculadas'))
  } else {
    res.json(Resposta.erro('Empresa não encontrada'))
  }
})


router.post('/pedidos/resumo/recalcule',  ehAdmin, async (req: any, res) => {
  await new EmpresaService().recalculeResumoPedidos();

  res.json(Resposta.sucesso({sucesso: true}))

})


router.post('/me/catalogo/limpe',  ehAdmin, async (req: any, res: any) => {
    let erro: string =
      await new EmpresaService().limpeCatalogoLoja(req.empresa, req.user );

    if(!erro){
      res.json(Resposta.sucesso());
    } else {
      res.json(Resposta.erro(erro));
    }
})

router.get('/me/tags', async(req: any, res: any) => {
  let tags = await new MapeadorDeTag().listeAsync({   })

  res.json(Resposta.sucesso(tags))

})

router.post('/:id/tags', logado, async (req: any, res: any) => {
  let tag: Tag = new Tag(req.body.nome)

  await tag.salve(true).catch((erro) => {
    res.json(Resposta.erro(erro));
  })

  if(tag.id)
    res.json(Resposta.sucesso(tag));
})

router.get('/me/cache/reset', async (req: any, res ) => {
  await CacheUtils.limpeCacheEmpresa(req.empresa)
  res.json(Resposta.sucesso( ))

})

router.get('/rede/:idrede/cache/reset', async (req: any, res ) => {
  let empresas = await new MapeadorDeEmpresa().listeAsync({idRede: Number(req.params.idrede)})

  for(let i  = 0; i <  empresas.length; i++){
    await new MapeadorDeEmpresa().removaDasCaches(empresas[i], true)
    await new MapeadorDeProduto(empresas[i].catalogo).removaCacheProdutos()
  }

  res.json(Resposta.sucesso(empresas.length))

})



router.get('/rede/:idrede/produtos/cache/reset', async (req: any, res ) => {
  let empresas = await new MapeadorDeEmpresa().listeAsync({idRede: Number(req.params.idrede)})

  for(let i  = 0; i <  empresas.length; i++)
    await new MapeadorDeProduto(empresas[i].catalogo).removaCacheProdutos()

  res.json(Resposta.sucesso(empresas.length))

})

router.get('/gendai/fidelidade/integracao/gere', async (req: any, res ) => {
  let mapeador =  new MapeadorDeEmpresa(), empresaService = new EmpresaService();

  let unidadesGendai: Array<LojaTrendFoods> = await mapeador.listeUnidadesGendais( );

  let resposta: any  = { totalLojas: unidadesGendai.length, erros: [],
                         naoEncontradas: [], gerados: [],  jaGerados: [],
                         naoVinculadas: []}

  try{

  }catch (erro){

  }
  for(let i = 0; i < unidadesGendai.length; i++){
    let lojaGendai = unidadesGendai[i];

    if(lojaGendai.idEmpresa){
      let empresa: Empresa = await mapeador.selecioneSync(lojaGendai.idEmpresa)

      if(empresa){
        if(empresa.ehUmaGedai()){
          if(!empresa.integracaoFidelidade){
            await empresaService.ativeIntegracaoFidelidadeExterna(empresa, req.user, 'gcom', lojaGendai.idLoja);
            resposta.gerados.push(lojaGendai.unidade)
          } else {
            resposta.jaGerados.push(lojaGendai.unidade)
          }
        } else {
          resposta.erros.push(String(`Empresa não é uma gendai: ${lojaGendai.unidade} - ${lojaGendai.idEmpresa}`))
        }
      } else {
        resposta.naoEncontradas.push(String(`${lojaGendai.unidade} - ${lojaGendai.idEmpresa}`))
      }
    } else {
      console.log('Loja china sem empresa vinculada: ' + lojaGendai.unidade)
      resposta.naoVinculadas.push(lojaGendai.unidade)
    }
  }

  res.json(resposta);
})

router.get('/china/fidelidade/integracao/gere', async (req: any, res ) => {
  let mapeador =  new MapeadorDeEmpresa(), empresaService = new EmpresaService();

  let unidadesChinas: Array<LojaTrendFoods> = await mapeador.listeUnidadesChinas( );

  let resposta: any  = { totalLojas: unidadesChinas.length, naoEncontradas: [], gerados: [],
    erros: [], jaGerados: [], naoVinculadas: []}

  for(let i = 0; i < unidadesChinas.length; i++){
    let lojaChina = unidadesChinas[i];

    if(lojaChina.idEmpresa){
      let empresa: Empresa = await mapeador.selecioneSync(lojaChina.idEmpresa)

      if(empresa){
        if(empresa.ehUmaCib()){
          if(!empresa.integracaoFidelidade){
            await empresaService.ativeIntegracaoFidelidadeExterna(empresa, req.user, 'gcom', lojaChina.idLoja);
            resposta.gerados.push(lojaChina.unidade)
          } else {
            resposta.jaGerados.push(lojaChina.unidade)
          }
        } else {
          resposta.erros.push(String(`Empresa não é uma china: ${lojaChina.unidade} - ${lojaChina.idEmpresa}`))
        }
      } else {
        resposta.naoEncontradas.push(String(`${lojaChina.unidade} - ${lojaChina.idEmpresa}`))
      }
    } else {
      console.log('Loja china sem empresa vinculada: ' + lojaChina.unidade)
      resposta.naoVinculadas.push(lojaChina.unidade)
    }
  }

  res.json(resposta)
})

router.get('/gcom/fidelidade/saldo/:telefone', async (req: any, res ) => {
   let resposta = await new GcomERPService(null).obtenhaSaldoCashback(req.empresa, req.params.telefone);

   console.log(resposta)

   res.json(resposta)
})

router.get('/operacoes/historico/lista', async (req: any, res: any) => {
  let operacoes: any = [];

  let keys: any = Object.keys(EnumOperacao);

  function transformString(input: string): string {
    return input.replace(/([A-Z])/g, ' $1').trim();
  }

  keys.forEach((key: string) => {
    // @ts-ignore
    operacoes.push({id: EnumOperacao[key], descricao: transformString(key)});
  })


  operacoes.sort((a: any, b: any) => a.id.localeCompare(b.id));

  res.json(Resposta.sucesso(operacoes))
})


router.get('/me/pausasprogramadas', async(req, res) => {

  const total = req.query.t || 20;
  const inicio = req.query.i || 0;
  const mapeador =  new MapeadorDePausaProgramada();

  let pausas = await mapeador.listeAsync({ todas: true, inicio: Number(inicio), total: Number(total)})

  let totalBanco: any =  await mapeador.selecioneTotal({});

  res.json(Resposta.sucesso({total: totalBanco, pausas: pausas}))

});

router.get('/importar/info/:id', async (req: any, res: any) => {
   let empresa = await new MapeadorDeEmpresa().selecioneSync(req.params.id)

   res.json(Resposta.sucesso({ empresa: empresa, totalProdutos: 10}));
})

// Rota para gerenciar número WhatsApp específico para campanhas
router.post('/numeroWhatsappCampanhas', (req: any, res) => {
  const empresa: Empresa = req.empresa;
  const numeroId = req.body.numeroId;

  try {
    const mapeadorDeEmpresa = new MapeadorDeEmpresa();

    // Atualizar o número de campanhas da empresa (null remove, number define)
    mapeadorDeEmpresa.atualizarNumeroWhatsappCampanhas(empresa, numeroId).then(() => {
      if (numeroId === null) {
        res.json(Resposta.sucesso('Campanhas voltarão a usar o número principal'));
      } else {
        res.json(Resposta.sucesso('Número WhatsApp para campanhas definido com sucesso'));
      }
    }).catch((erro: any) => {
      console.error('Erro ao configurar número para campanhas:', erro);
      res.json(Resposta.erro('Erro ao configurar número para campanhas: ' + erro.message));
    });
  } catch (erro: any) {
    console.error('Erro ao criar mapeador:', erro);
    res.json(Resposta.erro('Erro ao configurar número para campanhas: ' + erro.message));
  }
});

export const EmpresasController: Router = router;
