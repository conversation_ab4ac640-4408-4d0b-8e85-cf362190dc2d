<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="logFatura">
  <resultMap id="logFaturaRM" type="LogFatura">
    <id property="id" column="log_fatura_id" />

    <result property="descricao" column="log_fatura_descricao" />
    <result property="criadoEm" column="log_fatura_criado_em" />
    <result property="notas" column="log_fatura_notas" />
  </resultMap>



  <insert id="insira" parameterType="map" useGeneratedKeys="true" keyProperty="id">

    insert into log_fatura(id,descricao,criado_em,notas,fatura_id)
      values (#{id}, #{descricao}, #{criadoEm}, #{notas},#{fatura.id})
        ON DUPLICATE KEY     UPDATE descricao = #{descricao}, criado_em = #{criadoEm} , notas = #{notas}
  </insert>


</mapper>
