<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cardapio">

  <resultMap id="cardapioRM" type="Cardapio">
    <id property="id" column="cardapio_id"/>

    <result property="arquivo" column="cardapio_arquivo"/>

    <result property="botAtivo" column="cardapio_bot_ativo"/>

    <result property="modoVisualizacao" column="cardapio_modo_visualizacao"/>
    <result property="modoVisualizacaoQRcode" column="cardapio_modo_visualizacao_qr_code"/>

    <result property="modoTesteBot" column="cardapio_modo_teste_bot"/>
    <result property="exibirIndisponiveis" column="cardapio_exibir_indisponiveis"/>
    <result property="exibirProdutosValorZeradoMesa" column="cardapio_exibir_produtos_valor_zerado_mesa"/>
    <result property="limiteProdutos" column="cardapio_limite_produtos"/>
    <result property="exibirSelecaoCategorias" column="cardapio_exibir_selecao_categorias"/>
  </resultMap>

  <update id="atualize">
    update cardapio
        set arquivo = #{arquivo}, modo_visualizacao = #{modoVisualizacao}, modo_teste_bot = #{modoTesteBot},
            modo_visualizacao_qr_code = #{modoVisualizacaoQRcode}, exibir_selecao_categorias = #{exibirSelecaoCategorias},
            exibir_indisponiveis = #{exibirIndisponiveis}, limite_produtos = #{limiteProdutos},
            exibir_produtos_valor_zerado_mesa = #{exibirProdutosValorZeradoMesa}
            where id = #{id}
  </update>

  <insert id="insira">
    insert into cardapio(empresa_id, arquivo, modo_visualizacao, modo_teste_bot, modo_visualizacao_qr_code, exibir_indisponiveis, limite_produtos,exibir_selecao_categorias, exibir_produtos_valor_zerado_mesa)
      values(#{empresa.id}, #{arquivo}, #{modoVisualizacao}, #{modoTesteBot}, #{modoVisualizacaoQRcode}, #{modoVisualizacaoQRcode}, #{limiteProdutos}, #{exibirSelecaoCategorias}, #{exibirProdutosValorZeradoMesa});
  </insert>

  <update id="atualizeBotAtivo" parameterType="map">
    update cardapio set bot_ativo = #{botAtivo}, modo_teste_bot = #{modoTesteBot} where id = #{id}
  </update>
</mapper>
