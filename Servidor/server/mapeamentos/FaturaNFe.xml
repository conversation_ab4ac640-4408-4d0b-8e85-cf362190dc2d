<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
export class FaturaNFe {
  id: number
  numeroDuplicata: any;
  dataDeVencimento: any;
  valorDuplicata: number;
}
-->
<mapper namespace="faturaNFe">
  <resultMap id="faturaNFeRM" type="FaturaNFe">
    <id property="id" column="fatura_nfe_id"/>
    <result property="numeroDuplicata" column="fatura_nfe_numero_duplicata"/>
    <result property="dataDeVencimento" column="fatura_nfe_data_de_vencimento"/>
    <result property="valorDuplicata" column="fatura_nfe_valor_duplicata"/>

    <assocition property="cobranca" column="cobranca_id" resultMap="cobranca.cobrancaRM"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists fatura_nfe (
      id bigint(20) primary key,
      numero_duplicata bigint(20),
      data_de_vencimento date,
      valor_duplicata decimal(10,2),
      cobranca_id bigint(20),
      foreign key (cobranca_id) references cobranca(id)
    );
  </create>
</mapper>
