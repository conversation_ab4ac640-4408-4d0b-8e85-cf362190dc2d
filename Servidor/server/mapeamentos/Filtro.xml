<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="filtro">
  <resultMap id="filtroResultMap" type="Filtro">
    <id property="id" column="filtro_id"/>
    <result property="nome" column="filtro_nome"/>
    <result property="dados" column="filtro_dados"/>
  </resultMap>

  <insert id="insira" parameterType="Filtro" keyProperty="id">
    insert into filtro (nome,dados,usuario_id, empresa_id)
             values(#{nome},  #{dados}, #{usuario.id},  #{empresa.id}  );
  </insert>

  <select id="selecione" parameterType="map" resultMap="filtroResultMap">
    select f.id filtro_id,
           f.nome filtro_nome,
           f.dados filtro_dados
       from filtro f   join usuario u on u.id = f.usuario_id
    where  f.empresa_id = #{idEmpresa}
    <if test="idUsuario != null">
    and   u.id = #{idUsuario}
    </if>
    and f.excluido is not true
  </select>

  <select id="existe" parameterType="map" resultType="int">
    select count(*) total from filtro f join usuario u   on u.id = f.usuario_id
        where  f.empresa_id = #{idEmpresa} and    u.id = #{idUsuario}  and f.nome = #{nome}
           <if test="id"> and f.id != #{id}</if>
  </select>

  <update id="atualize">
    update filtro
      set  dados = #{dados}
        where  id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="remova">
    update filtro set excluido =  true where id = #{id}
  </update>
  <update id="replicarFiltroCib">
    insert into filtro(empresa_id,nome,usuario_id,dados)
      select empresa.id, 'Ultima Compra a mais de 6 meses',8, ''
        from empresa where rede_id = 1 and not exists (select 1 from filtro where empresa_id = empresa.id and nome  =  'Comprou nos ultimos 6 meses' );

  </update>

</mapper>
