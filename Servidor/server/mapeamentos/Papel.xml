<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="papel">
  <resultMap id="papelRM" type="Papel">
    <id property="id" column="papel_id"/>
    <result property="nome" column="papel_nome"/>
    <result property="escopo" column="papel_escopo"/>

    <collection  property="operacoes"   resultMap="papel.operacaoSistemaRM"/>
  </resultMap>

  <resultMap id="operacaoSistemaRM" type="OperacaoDoSistema">
    <id property="id" column="operacao_do_sistema_id"/>

    <result property="nome" column="operacao_do_sistema_nome"/>
    <result property="descricao" column="operacao_do_sistema_descricao"/>
  </resultMap>

  <select id="selecione"     resultMap="papelRM" prefix="true">
    select  *
      from papel left join papel_operacao on papel_id = papel.id
                 left join operacao_do_sistema on  operacao_do_sistema.id = operacao_id

          where empresa_id = #{idEmpresa}
              <if test="idUsuario">
                 and exists (select 1 from usuario_papel where  usuario_id = #{idUsuario} and papel_id = papel.id)
              </if>
              <if test="id">
                 and papel.id = #{id}
              </if>

  </select>

  <select id="listeOperacoes"     resultMap="operacaoSistemaRM" prefix="true">
      select * from operacao_do_sistema;
  </select>

  <insert id="insiraUsuario">
      insert into usuario_papel(usuario_id,papel_id) values (#{usuario}, #{papel});
  </insert>

  <update id="removaUsuario">
    delete from usuario_papel where papel_id = #{papel} and usuario_id = #{usuario};
  </update>


  <insert id="insiraOperacao">
      insert into papel_operacao(papel_id, operacao_id) values (#{papel}, #{operacao});
  </insert>

  <update id="removaOperacao">
     delete from papel_operacao where papel_id = #{papel} and operacao_id = #{operacao};
  </update>

</mapper>
