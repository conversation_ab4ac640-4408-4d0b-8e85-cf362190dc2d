<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mensagemEnviada">
  <resultMap id="mensagemEnviadaRM" type="MensagemEnviada">
    <id property="id" column="mensagem_enviada_id"/>

    <result property="mensagem" column="mensagem_enviada_mensagem"/>
    <result property="telefone" column="mensagem_enviada_telefone"/>
    <result property="tipoDeNotificacao" column="mensagem_enviada_tipo_de_notificacao"/>
    <result property="horario" column="mensagem_enviada_horario"/>
    <result property="horarioModificacao" column="mensagem_enviada_horario_modificacao"/>
    <result property="status" column="mensagem_enviada_status"/>

    <result property="imagem" column="mensagem_enviada_imagem"/>
    <result property="idSMSExterno" column="mensagem_enviada_id_sms_externo"/>

    <result property="idWhatsapp" column="mensagem_enviada_id_whatsapp"/>

    <result property="meioDeEnvio" column="mensagem_enviada_meio_de_envio"/>

    <result property="temMenu" column="mensagem_enviada_tem_menu"/>
    <result property="menu" column="mensagem_enviada_menu"/>

    <result property="fazerPreview" column="mensagem_fazer_preview"/>

    <result property="qtdeTentativas" column="mensagem_enviada_qtde_tentativas"/>

    <association property="numeroWhatsapp" columnPrefix="msg_" resultMap="numeroWhatsapp.numeroWhatsappRM"/>

    <association property="campanha" column="campanha_id" columnPrefix="mes_" resultMap="campanha.dtoCampanhaResultMap"/>

    <association property="empresa" column="empresa_id" resultMap="empresa.empresaRM"/>

    <association property="contato" column="contato_id" resultMap="contato.contatoRM"/>
    <collection  property="links"  resultMap="linkEncurtado.linkEncurtadoRM"/>
  </resultMap>

  <resultMap id="resumoRM" type="DTOGenerico">
    <id property="id" column="resumo_id"/>

    <result property="qtde" column="resumo_qtde"/>
    <result property="status" column="resumo_status"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="mensagemEnviadaRM" prefix="true">
    select mensagem_enviada.*, contato.*, link.*, msg_numero_whatsapp.*, mes_campanha.*, empresa.*,
    case
    WHEN mensagem_enviada.tipo_de_notificacao = 'Marketing' THEN 0
    ELSE 10
    END prioridade
    from mensagem_enviada
    left join empresa on(mensagem_enviada.empresa_id = empresa.id)
    left join campanha mes_campanha on(mensagem_enviada.campanha_id = mes_campanha.id)
    left join contato on(mensagem_enviada.contato_id = contato.id and contato.removido is not true)
    left join links_mensagem_enviada  lm on lm.mensagem_enviada_id = mensagem_enviada.id
    left join link_encurtado link  on lm.link_encurtado_id = link.id
    left join numero_whatsapp msg_numero_whatsapp on msg_numero_whatsapp.id = mensagem_enviada.numero_whatsapp_id
    where   mensagem_enviada.id > 32560044 and
    <if test="idEmpresa != null">
      mensagem_enviada.empresa_id = #{idEmpresa}
    </if>
    <if test="idEmpresa == null">
      1 = 1
    </if>
    <if test="id != null">
      and mensagem_enviada.id = #{id}
    </if>
    <if test="idWhatsapp != null">
      and mensagem_enviada.id_whatsapp = #{idWhatsapp}
    </if>
    <if test="status != null">
      and mensagem_enviada.status = #{status}
    </if>
    <if test="nome != null">
      and contato.nome like #{nome}
    </if>
    <if test="tipo != null">
      and mensagem_enviada.tipo_de_notificacao = #{tipo}
      <if test="envioRecente != null">
        and mensagem_enviada.horario >= now() - interval 1 minute
      </if>
    </if>
    <if test="ultimaMensagem != null">
      and mensagem_enviada.numero_whatsapp_id = #{numeroWhatsapp.id}
      and mensagem_enviada.status in ('AGUARDANDO_ATIVACAO', 'NOVA')
    </if>
    <choose>
      <when test="pendentesDark != null">
        and msg_numero_whatsapp.whatsapp = #{numeroWhatsapp.whatsapp}
        and mensagem_enviada.status in ('AGUARDANDO_ATIVACAO', 'NOVA')
        and (
        (mensagem_enviada.id > #{idm} and mensagem_enviada.tipo_de_notificacao &lt;&gt; 'Marketing')
        or (mensagem_enviada.tipo_de_notificacao = 'Marketing' and
        mes_campanha.status = 'Enviando'
        and mes_campanha.status_aprovacao = 'Aprovada' and
        mensagem_enviada.horario > date_sub(now(), interval 1200 HOUR)))
      </when>
      <when test="pendentes != null">
        and mensagem_enviada.numero_whatsapp_id = #{numeroWhatsapp.id}
        and mensagem_enviada.status in ('AGUARDANDO_ATIVACAO', 'NOVA')
        and (
        (mensagem_enviada.id > #{idm} and mensagem_enviada.tipo_de_notificacao &lt;&gt; 'Marketing')
        or (mensagem_enviada.tipo_de_notificacao = 'Marketing' and
        mes_campanha.status = 'Enviando'
        and mes_campanha.status_aprovacao = 'Aprovada' and
        mensagem_enviada.horario > date_sub(now(), interval 1200 HOUR)))
      </when>
    </choose>
    <if test="idContato != null">
      and mensagem_enviada.contato_id = #{idContato}
    </if>

    <if test="idCampanha">
      and mensagem_enviada.campanha_id = #{idCampanha}
    </if>

    <if test="enviadasNosultimosSegundos != null">
      and mensagem_enviada.horario &gt;= CURDATE() - INTERVAL #{enviadasNosultimosSegundos} SECOND
      and mensagem_enviada.contato_id = #{contato.id} and  mensagem_enviada.status != 'DUPLICADA'
      and mensagem_enviada.status != 'CANCELADA'
    </if>

    <if test="enviadasNosultimosDias != null">
      and mensagem_enviada.horario &gt;= CURDATE() - INTERVAL #{enviadasNosultimosDias} DAY
      and mensagem_enviada.contato_id = #{contato.id} and  mensagem_enviada.status != 'DUPLICADA'
      and mensagem_enviada.status != 'CANCELADA'
    </if>

    <if test="soQueRecebeu">
      and  link.visitas > 0
    </if>

    <if test="dataInicio != null">
      and mensagem_enviada.horario >= #{dataInicio}
    </if>

    <choose>
      <when test="pendentes != null || ultimaMensagem != null || pendentesDark != null">
        order by prioridade desc, mensagem_enviada.id limit #{inicio}, #{total}
      </when>
      <when test="inicio != null">
        order by mensagem_enviada.id desc limit #{inicio},#{total}
      </when>
    </choose>
  </select>

  <select id="selecioneResumo" parameterType="map" resultMap="resumoRM">
    select
      mensagem_enviada.status resumo_id,
      mensagem_enviada.status resumo_status,
      count(*) resumo_qtde
      from mensagem_enviada
      left join contato on(mensagem_enviada.contato_id = contato.id  and contato.removido is not true)
      where
        mensagem_enviada.empresa_id = #{idEmpresa}
        <if test="status != null">
          and mensagem_enviada.status = #{status}
        </if>
        <if test="nome != null">
          and contato.nome like #{nome}
        </if>
        <if test="dataInicio != null">
          and mensagem_enviada.horario >= #{dataInicio}
        </if>
        <if test="tipo != null">
          and mensagem_enviada.tipo_de_notificacao = #{tipo}
          <if test="envioRecente != null">
            and mensagem_enviada.horario >= now() - interval 1 minute
          </if>
        </if>
      group by mensagem_enviada.status;
  </select>

  <select id="selecioneTodas" parameterType="map" resultMap="mensagemEnviadaRM" prefix="true">
    select mensagem_enviada.*, contato.*, link.*, empresa.*
    from mensagem_enviada
    left join contato on(mensagem_enviada.contato_id = contato.id  and contato.removido is not true)
    inner join empresa on(mensagem_enviada.empresa_id = empresa.id)
    left join links_mensagem_enviada  lm on lm.mensagem_enviada_id = mensagem_enviada.id
    left join link_encurtado link  on lm.link_encurtado_id = link.id
    where
    <choose>
      <when test="status != null">
        mensagem_enviada.status = #{status}
      </when>
    </choose>

    <if test="sms">
      and mensagem_enviada.meio_de_envio not in ( 'Whatsapp' , 'Mock')
    </if>

    <if test="enviadasNosultimosDias != null">
      and mensagem_enviada.horario &gt;= CURDATE() - INTERVAL #{enviadasNosultimosDias} DAY
      and mensagem_enviada.contato_id = #{contato.id} and  mensagem_enviada.status != 'DUPLICADA'
    </if>

    <if test="soQueRecebeu">
      and  link.visitas > 0
    </if>

    <if test="inicio != null">
      order by mensagem_enviada.id desc limit #{inicio},#{total}
    </if>
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int">
    select
      count(*) total
    from mensagem_enviada
    left join contato on(mensagem_enviada.contato_id = contato.id and contato.removido is not true)
    where
    mensagem_enviada.empresa_id = #{idEmpresa}
      <if test="id != null">
        and mensagem_enviada.id = #{id}
      </if>
      <if test="status != null">
        and mensagem_enviada.status = #{status}
        <if test="nome != null">
          and contato.nome like #{nome}
        </if>
      </if>
      <if test="dataInicio != null">
        and mensagem_enviada.horario >= #{dataInicio}
      </if>
      <if test="horario != null">
        and mensagem_enviada.horario >= #{horario}
      </if>
      <if test="nome != null">
        and contato.nome like #{nome}
      </if>
      <if test="tipo != null">
        and mensagem_enviada.tipo_de_notificacao = #{tipo}
        <if test="envioRecente != null">
          and mensagem_enviada.horario >= now() - interval 1 minute
        </if>
      </if>
      <if test="pendentes != null">
        and mensagem_enviada.numero_whatsapp_id = #{numeroWhatsapp.id}
        and mensagem_enviada.status in ('AGUARDANDO_ATIVACAO', 'NOVA')
        and mensagem_enviada.horario_modificacao > #{horarioModificacao}
      </if>
      <if test="idContato != null">
        and mensagem_enviada.contato_id = #{idContato}
      </if>

      <if test="idCampanha">
         and mensagem_enviada.campanha_id = #{idCampanha}
      </if>

    <if test="soQueRecebeu">
      and  exists ( select 1 from links_mensagem_enviada lm  join link_encurtado link  on lm.link_encurtado_id = link.id  where   lm.mensagem_enviada_id = mensagem_enviada.id and visitas > 0 )
    </if>

    <if test="enviadasNosultimosDias != null">
      and mensagem_enviada.horario &gt;= CURDATE() - INTERVAL #{enviadasNosultimosDias} DAY
      and mensagem_enviada.contato_id = #{contato.id} and  mensagem_envidada.tipo_de_notificacao = #{tipo} and mensagem_enviada.status != 'DUPLICADA'
    </if>
  </select>

  <insert id="insira" parameterType="MensagemEnviada" keyProperty="id">
      insert into mensagem_enviada(mensagem, horario, tipo_de_notificacao, status, telefone, contato_id, empresa_id,
        horario_modificacao, campanha_id, id_sms_externo, meio_de_envio, imagem, numero_whatsapp_id, fazer_preview, tem_menu, menu, id_whatsapp, qtde_tentativas) values
            (#{mensagem}, #{horario}, #{tipoDeNotificacao}, #{status}, #{telefone}, #{contato.id}, #{empresa.id}, #{horario},
          #{campanha.id}, #{idSMSExterno}, #{meioDeEnvio}, #{imagem}, #{numeroWhatsapp.id}, #{fazerPreview}, #{temMenu}, #{menu}, #{idWhatsapp}, #{qtdeTentativas});
  </insert>

  <insert id="insiraAssociacaoLink" parameterType="map" useGeneratedKeys="true" keyProperty="id">
    insert into links_mensagem_enviada(mensagem_enviada_id, link_encurtado_id, empresa_id) values
    (#{mensagemEnviada.id}, #{linkEncurtado.id}, #{empresa.id});
  </insert>

  <insert id="atualize" parameterType="MensagemEnviada">
    update mensagem_enviada set status = #{status},
      mensagem = #{mensagem},
      id_whatsapp = #{idWhatsapp},
      tem_menu = #{temMenu},
      menu = #{menu},
      horario_modificacao = now(3),
      qtde_tentativas = #{qtdeTentativas}
      where
      <if test="empresa != null">
      empresa_id = #{empresa.id}
      </if>
      <if test="empresa == null">
        1 = 1
      </if>
      and id = #{id};
  </insert>

  <insert id="atualizeStatus" parameterType="MensagemEnviada">
    update mensagem_enviada set status = #{status}, horario_modificacao = now(3)
       where  id = #{id} and   empresa_id = #{empresa.id};
  </insert>

  <update id="removaPendentes" parameterType="MensagemEnviada">
    DELETE FROM mensagem_enviada
    WHERE
      empresa_id = #{empresa.id}
      AND campanha_id = #{id}
      AND status = 'NOVA';
  </update>

  <update id="atualizeStatus" parameterType="map">
    update mensagem_enviada
    set status = #{status} where
    empresa_id = #{empresa.id}
    and id = #{id};
  </update>
</mapper>
