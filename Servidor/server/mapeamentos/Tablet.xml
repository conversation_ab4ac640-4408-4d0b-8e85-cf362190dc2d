<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tablet">
  <resultMap id="tabletRM" type="Tablet">
    <id property="id" column="tablet_id"/>
    <result property="numero" column="tablet_numero"/>
    <result property="pinAdmin" column="tablet_pin_admin"/>
    <result property="ativo" column="tablet_ativo"/>
    <result property="dataUltimoUso" column="tablet_data_ultimo_uso"/>

    <association property="mesa" resultMap="mesa.mesaTabletRM"/>
    <association property="empresa" resultMap="empresa.empresaRM"/>

  </resultMap>

  <resultMap id="tabletConfiguradoRM" type="Tablet">
    <id property="id" column="tablet_id"/>
    <result property="numero" column="tablet_numero"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="tabletRM" prefix="true">
    select * from tablet
    left join mesa  on tablet.mesa_id = mesa.id
     where tablet.empresa_id = #{idEmpresa}
      <if test="numero">
        and tablet.numero = #{numero}
      </if>
      <if test="id">
        and tablet.id = #{id}
      </if>
      <if test="ativo != null">
        and tablet.ativo = #{ativo}
      </if>

      <if test="idMesa">
        and tablet.mesa_id = #{idMesa}
      </if>
  </select>


  <update id="atualizeMesaAssociada" parameterType="map">
    update tablet
    set mesa_id = #{mesa.id},
        data_ultimo_uso = NOW()
    where id = #{id}  and empresa_id = #{empresa.id}
  </update>

  <update id="atualizeUltimoUso" parameterType="map">
    update tablet
    set data_ultimo_uso = #{dataUltimoUso}
    where numero = #{numero}  and empresa_id = #{empresa.id}
      and ativo = 1
  </update>

  <update id="atualize" parameterType="map">
    update tablet
    set numero = #{numero},
        pin_admin = #{pinAdmin},
        mesa_id = #{mesa.id},
        ativo = #{ativo},
        data_ultimo_uso = #{dataUltimoUso}
    where id = #{id}
      and empresa_id = #{empresa.id}
  </update>

  <insert id="insira" parameterType="map">
    insert into tablet (numero, pin_admin, mesa_id, empresa_id, ativo, data_ultimo_uso)
    values (#{numero}, #{pinAdmin}, #{mesa.id}, #{empresa.id}, #{ativo}, #{dataUltimoUso})
  </insert>
</mapper>
