<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="insumo">
  <resultMap id="insumoRM" type="Insumo">
    <id property="id" column="insumo_id"/>

    <result property="nome" column="insumo_nome"/>
    <result property="codigo" column="insumo_codigo"/>

    <result property="precoDeCusto" column="insumo_preco_de_custo"/>
    <result property="totalVinculos" column="insumo_total_vinculos"/>
    <result property="objeto" column="insumo_objeto"/>
    <result property="tipo" column="insumo_tipo"/>

    <association property="estoque" resultMap="estoque.estoqueRM"/>
    <association property="receita" resultMap="receita.receitaRM"/>
    <association property="grupo" resultMap="grupoDeInsumo.grupoDeInsumoRM"/>
    <association property="unidadeMedida" resultMap="unidadeMedida.unidadeMedidaRM"/>
    <association property="empresa" resultMap="empresa.empresaSimplesRM"/>

    <collection  property="produtos" resultMap="produto.produtoResultMap"/>
    <collection  property="opcoes"  resultMap="opcaoDeAdicionalDeProduto.opcaoComAdicionalRM"/>


    <discriminator javaType="String" column="insumo_objeto" >
      <case value="insumo" resultType="Insumo"></case>
      <case value="ficha-tecnica" resultType="FichaTecnica"></case>
    </discriminator>

  </resultMap>

  <resultMap id="insumoDoEstoqueRM" type="Insumo">
    <id property="id" column="insumo_id"/>

    <result property="nome" column="insumo_nome"/>
    <result property="codigo" column="insumo_codigo"/>
    <result property="objeto" column="insumo_objeto"/>

    <association property="estoque" resultMap="estoque.estoqueRM"/>
    <discriminator javaType="String" column="insumo_objeto" >
      <case value="insumo" resultType="Insumo"></case>
      <case value="ficha-tecnica" resultType="FichaTecnica"></case>
    </discriminator>

  </resultMap>

  <resultMap id="insumoDaReceitaRM" type="Insumo">
    <id property="id" column="receita_insumo_id"/>

    <result property="nome" column="receita_insumo_nome"/>
    <result property="codigo" column="receita_insumo_codigo"/>
    <result property="objeto" column="receita_insumo_objeto"/>
    <result property="tipo" column="receita_insumo_tipo"/>

    <association property="estoque" columnPrefix="receita_" resultMap="estoque.estoqueRM"/>
    <association property="unidadeMedida" columnPrefix="receita_" resultMap="unidadeMedida.unidadeMedidaRM"/>
    <discriminator javaType="String" column="receita_insumo_objeto" >
      <case value="insumo" resultType="Insumo"></case>
      <case value="ficha-tecnica" resultType="FichaTecnica"></case>
    </discriminator>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="insumoRM" prefix="true">
    select * from insumo  left join estoque on estoque.id = insumo.estoque_id
                          left join grupo_de_insumo on grupo_de_insumo.id = grupo_de_insumo_id
                          left join unidade_medida on unidade_medida_id = unidade_medida.id
                          left join insumo_produto on insumo_produto.insumo_id = insumo.id
                          left join produto on produto.id = produto_id
                          left join categoria on categoria.id = categoria_id
                          left join imagem_do_produto on imagem_do_produto.produto_id = produto.id
                          left join insumo_opcao on insumo_opcao.insumo_id = insumo.id
                          left join opcao_adicional_produto on opcao_adicional_produto.id = opcao_id
                          left join adicional_produto adicional on adicional.id = adicional_produto_id
                          left join receita on receita.id = insumo.receita_id
                          left join ingrediente_da_receita on
                              (ingrediente_da_receita.receita_id = receita.id and ingrediente_da_receita.removido is not true)
                          left join insumo receita_insumo on receita_insumo.id = ingrediente_da_receita.insumo_id
                          left join unidade_medida receita_unidade_medida on receita_insumo.unidade_medida_id = receita_unidade_medida.id
                          left join estoque receita_estoque on receita_estoque.id = receita_insumo.estoque_id
        where insumo.empresa_id = #{idEmpresa} and insumo.removido is not true
               <if test="id">
                  and insumo.id = #{id}
               </if>
               <if test="codigo">
                  and insumo.codigo = #{codigo}
               </if>

               <if test="idGrupo">
                  and grupo_de_insumo.id = #{idGrupo}
               </if>

               <if test="semGrupo">
                  and grupo_de_insumo.id  is null
               </if>

              <if test="termo">
                 and insumo.nome like #{termo}
              </if>
              <if test="comEstoque">
                 and estoque.id is not null
              </if>

              <if test="vincular">
                and  (insumo.tipo is null or  insumo.tipo != 'produto-beneficiado')
              </if>


             <if test="alertaEstoque">
                and estoque.quantidade_minima * 1.1 >= estoque.quantidade order by estoque.ultima_atualizacao desc
              </if>

         <if test="orderBy">
           order by insumo.nome
         </if>

  </select>


  <select id="selecioneComposicoes" parameterType="map" resultMap="insumoRM" prefix="true">
      select * from insumo
          where insumo.empresa_id = #{idEmpresa} and insumo.removido is not true and exists
          ( select 1 from receita  join ingrediente_da_receita on  (ingrediente_da_receita.receita_id = receita.id and
                                                                    ingrediente_da_receita.removido is not true)
                        where ingrediente_da_receita.insumo_id = #{idInsumo} and receita.id = insumo.receita_id )
  </select>

  <select id="existe" parameterType="map" resultType="long">
    select count(1) from insumo  where  empresa_id = #{idEmpresa} and codigo = #{codigo}
    <if test="id">
      and id != #{id}
    </if>
  </select>


  <insert id="insira" parameterType="map" keyProperty="id">
    insert into insumo(nome,objeto,estoque_id,grupo_de_insumo_id,codigo,unidade_medida_id,receita_id,tipo,empresa_id,total_vinculos,preco_de_custo)
      values(#{nome},#{objeto},#{estoque.id},#{grupo.id},#{codigo},#{unidadeMedida.id}, #{receita.id}, #{tipo},#{empresa.id},0,0)
  </insert>


  <update id="atualize">
     update insumo  set
         nome = #{nome},    grupo_de_insumo_id = #{grupo.id},
          preco_de_custo = #{precoDeCusto},  codigo = #{codigo},  unidade_medida_id = #{unidadeMedida.id}
      where id  = #{id} and empresa_id = #{empresa.id};
  </update>


  <update id="remova" parameterType="map">
    update insumo
    set  removido  = true
    where id = #{id} and   empresa_id = #{empresa.id}
  </update>

  <update id="atualizeTotalVinculos">
    update insumo set
    total_vinculos = (
    select
    (select count(distinct produto_id) from insumo_produto join produto on produto.id = produto_id and removido is not true where insumo_id = #{id}) +
    (select count(distinct opcao_id) from insumo_opcao join opcao_adicional_produto on  opcao_adicional_produto.id =  opcao_id  and excluido is not true where insumo_id = #{id}) +
    (select count(*) from   ingrediente_da_receita  where ingrediente_da_receita.removido is not true
                                                           and ingrediente_da_receita.insumo_id =  #{id})
    )
    where id = #{id};
  </update>

  <insert id="insiraListaProdutos">
    INSERT INTO insumo_produto
    (insumo_id, produto_id)
    VALUES
    <foreach item="item" collection="dados" open="" separator="," close="">
      ( #{item.idInsumo} ,   #{item.idProduto}  )
    </foreach>
  </insert>

  <update id="removaProdutoInsumo">
    delete from insumo_produto where insumo_id = #{idInsumo} and produto_id = #{idProduto}
  </update>


  <insert id="insiraListaOpcoes">
    INSERT INTO insumo_opcao
    (insumo_id, opcao_id)
    VALUES
    <foreach item="item" collection="dados" open="" separator="," close="">
      ( #{item.idInsumo} ,   #{item.idOpcao}  )
    </foreach>
  </insert>

  <update id="removaOpcaoInsumo">
    delete from insumo_opcao where insumo_id = #{idInsumo} and opcao_id = #{idOpcao}
  </update>


</mapper>
