<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
  quantidadeBC: number;
  aliquota: number;
  valor: number;
  -->
<mapper namespace="cide" >
  <resultMap id="cideRM" type="Cide">
    <id property="id" column="cide_id"/>
    <result property="quantidadeBC" column="cide_quantidade_bc"/>
    <result property="aliquota" column="cide_aliquota"/>
    <result property="valor" column="cide_valor"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists cide (
      id bigint(20) primary key,
      quantidade_bc decimal(10,2),
      aliquota decimal(10,2),
      valor decimal(10,2)
    );
  </create>
</mapper>
