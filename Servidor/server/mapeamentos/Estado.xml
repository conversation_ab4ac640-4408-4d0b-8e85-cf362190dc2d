<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="estado">

  <resultMap id="estadoRM" type="local.Estado">
    <id property="id" column="estado_id" />

    <result property="nome" column="estado_nome" />
    <result property="sigla" column="estado_sigla" />
    <result property="codigoIbge" column="estado_codigo_ibge" />
  </resultMap>

  <resultMap id="dtoInfoQtde" type="dto.DTOResumo">

    <id property="id" column="id" />
    <result property="nome" column="nome" />
    <result property="qtde" column="qtde" />

  </resultMap>


  <select id="selecione" parameterType="map" resultMap="estado.estadoRM">
    select  e.id estado_id,
    e.nome estado_nome,
    e.sigla estado_sigla,
    e.codigo_ibge estado_codigo_ibge
    from estado e
      <if test="nome != null">
        where e.nome = #{nome}
      </if>
  </select>
</mapper>
