<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="endereco">
  <resultMap id="enderecoRM" type="Endereco">
    <id property="id" column="endereco_id" />

    <result property="descricao" column="endereco_descricao" />
    <result property="bairro" column="endereco_bairro" />
    <result property="cep" column="endereco_cep" />
    <result property="localidade" column="endereco_localidade" />
    <result property="localizacao" column="endereco_localizacao" />
    <result property="complemento" column="endereco_complemento" />
    <result property="pontoDeReferencia" column="endereco_ponto_de_referencia" />

    <result property="logradouro" column="endereco_logradouro" />
    <result property="numero" column="endereco_numero" />

    <association property="cidade"  resultMap="cidade.cidadeRM"/>
    <association property="estado"  resultMap="estado.estadoRM"/>

    <association property="zonaDeEntrega"  resultMap="zonaDeEntrega.zonaDeEntregaRM"/>

  </resultMap>

  <select id="selecione"   resultMap="enderecoRM" prefix="true">
    select * from  endereco   left join cidade  on cidade.id = cidade_id
                              left join estado on estado.id = estado_id
                              left join zona_de_entrega on zona_de_entrega.id = zona_de_entrega_id

     where
      <choose>
        <when test="id != null">
          endereco.id = #{id}
        </when>
        <when test="idContato != null">
          endereco.contato_id = #{idContato} and endereco.removido is not true


           <if test="numero">
            and endereco.numero = #{numero}
          </if>

          <if test="cep">
            and endereco.cep = #{cep} limit 1
          </if>

        </when>
      </choose>
  </select>


  <update id="atualize">
    update endereco

    set

    descricao =  #{descricao},
    bairro =  #{bairro},
    cep = #{cep},
    complemento =  #{complemento},
    logradouro = #{logradouro},
    numero = #{numero},
    cidade_id = #{cidade.id},
    localizacao = #{localizacao},
    zona_de_entrega_id = #{zonaDeEntrega.id},
    ponto_de_referencia = #{pontoDeReferencia.id}

    where id = #{id}
  </update>

  <update id="remova">
    update endereco set removido = true     where id = #{id}
  </update>

  <update id="desvicular_enderecos_repetidos">

    create table endereco_principal(id bigint(20) not null, contato_id bigint(20) not null);
    create table endereco_secundario(id bigint(20) not null, contato_id bigint(20) not null);

    insert into endereco_principal(id, contato_id)
      select id, contato_id   from endereco
            where  contato_id is not null  group by contato_id, cep, logradouro  having count(*) > 1
          union
      select id, contato_id   from endereco
          where descricao is not null and contato_id is not null  group by contato_id,   logradouro, descricao having count(*) > 1
        union
    select id, contato_id   from endereco
        where descricao is not null and contato_id is not null  group by contato_id,   logradouro, descricao having count(*)  = 1;


    insert into endereco_secundario(id, contato_id)
     select id, contato_id  from endereco
          where   exists (select 1 from endereco_principal where endereco_principal.contato_id = endereco.contato_id) and
                       not exists (select 1 from endereco_principal where endereco_principal.id = endereco.id) ;

    update  endereco set contato_id = null
        where    exists (select 1 from endereco_secundario where endereco_secundario.id  = endereco.id);

    update  endereco join  endereco_secundario on endereco_secundario.id = endereco.id
        set endereco.contato_id = endereco_secundario.contato_id  ;


  </update>

</mapper>
