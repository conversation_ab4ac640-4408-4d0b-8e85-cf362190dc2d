<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="bandeira">

  <resultMap id="bandeiraRM" type="Bandeira">
    <id property="id" column="bandeira_id"/>

    <result property="nome" column="bandeira_nome"/>
    <result property="imagem" column="bandeira_imagem"/>
    <result property="tipo" column="bandeira_tipo"/>
    <result property="opendeliveryBrand" column="bandeira_opendelivery_brand"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="bandeiraRM" prefix="true">
    select * from  bandeira order by  tipo,nome

  </select>

  <update id="atualize">
    update bandeira
        set nome = #{nome}, imagem = #{imagem}, tipo = #{tipo}
        where id = #{id}
  </update>
</mapper>
